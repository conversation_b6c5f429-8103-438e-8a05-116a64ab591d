const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Basic middleware
app.use(express.json());

// Serve static assets (CSS, JS files) from dist/assets
app.use('/assets', express.static(path.join(__dirname, 'dist', 'assets')));

// API route
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Spiral Cipher Tool API is running' });
});

// Serve the React app
app.get('/', (req, res) => {
  const indexPath = path.join(__dirname, 'dist', 'public', 'index.html');
  console.log(`Serving index from: ${indexPath}`);

  // Check if file exists
  const fs = require('fs');
  if (fs.existsSync(indexPath)) {
    console.log('File exists, serving...');
    res.sendFile(indexPath);
  } else {
    console.log('File does not exist!');
    res.status(404).send('Index file not found');
  }
});

// Start the server and keep it running
const server = app.listen(PORT, () => {
  console.log(`🚀 Bio-Inspired Cipher Tool server is running on http://localhost:${PORT}`);
  console.log(`📁 Serving static files from: ${path.join(__dirname, 'dist')}`);
  console.log(`🌐 Open your browser to: http://localhost:${PORT}`);
  console.log(`✅ Express.js server is ready and waiting for connections...`);
});

// Keep the process alive and handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server gracefully...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Prevent the process from exiting unexpectedly
process.stdin.resume();

console.log('✅ Server script loaded successfully - Express.js is ready!');
