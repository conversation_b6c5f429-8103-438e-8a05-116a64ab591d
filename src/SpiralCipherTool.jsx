import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

// Bio-inspired cipher utilities
const BIO_CIPHER = {
  // Bee waggle dance directions (8 directions like compass)
  BEE_DIRECTIONS: ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'],

  // Ant pheromone strength levels
  PHEROMONE_LEVELS: [1, 2, 3, 4, 5],

  // Spider web pattern: center + rings + radials
  WEB_STRUCTURE: {
    center: [2, 2], // Center of 5x5 grid
    rings: [
      [[2, 2]], // Center
      [[1, 1], [1, 2], [1, 3], [2, 1], [2, 3], [3, 1], [3, 2], [3, 3]], // Ring 1
      [[0, 0], [0, 1], [0, 2], [0, 3], [0, 4], [1, 0], [1, 4], [2, 0], [2, 4], [3, 0], [3, 4], [4, 0], [4, 1], [4, 2], [4, 3], [4, 4]] // Ring 2
    ]
  },

  // Convert letter to bee dance angle (A=0°, B=45°, etc.)
  letterToBeeAngle: (letter) => {
    const charCode = letter.charCodeAt(0) - 65; // A=0, B=1, etc.
    return (charCode * 45) % 360; // 8 directions * 45° each
  },

  // Convert letter to ant pheromone strength
  letterToPheromone: (letter) => {
    const charCode = letter.charCodeAt(0) - 65;
    return (charCode % 5) + 1; // 1-5 strength levels
  },

  // Get spider web position for index
  getWebPosition: (index) => {
    const rings = BIO_CIPHER.WEB_STRUCTURE.rings;
    let totalPositions = 0;

    for (let ring = 0; ring < rings.length; ring++) {
      if (index < totalPositions + rings[ring].length) {
        return {
          ring: ring,
          position: rings[ring][index - totalPositions],
          isCenter: ring === 0
        };
      }
      totalPositions += rings[ring].length;
    }

    // Fallback to spiral pattern for remaining positions
    return { ring: 2, position: [index % 5, Math.floor(index / 5)], isCenter: false };
  },

  // Individual cipher methods
  encryptWithSpiderOnly: (message, gridSize) => {
    const totalCells = gridSize * gridSize;
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, '').padEnd(totalCells, 'X').slice(0, totalCells);
    let webGrid = Array.from({ length: gridSize }, () => Array(gridSize).fill(''));

    // Fill grid row by row
    let charIndex = 0;
    for (let row = 0; row < gridSize; row++) {
      for (let col = 0; col < gridSize; col++) {
        if (charIndex < cleaned.length) {
          webGrid[row][col] = cleaned[charIndex++];
        }
      }
    }

    // Read in spider web pattern (center outward)
    return BIO_CIPHER.readSpiderWebPattern(webGrid, gridSize);
  },

  encryptWithBeeOnly: (message, gridSize) => {
    const totalCells = gridSize * gridSize;
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, '').padEnd(totalCells, 'X').slice(0, totalCells);

    // Bee cipher: sort letters by their dance angles
    let letterAngles = [];
    for (let i = 0; i < cleaned.length; i++) {
      letterAngles.push({
        letter: cleaned[i],
        angle: BIO_CIPHER.letterToBeeAngle(cleaned[i]),
        originalIndex: i
      });
    }

    // Sort by bee dance angle (directional encoding)
    letterAngles.sort((a, b) => a.angle - b.angle);
    return letterAngles.map(item => item.letter).join('');
  },

  encryptWithAntOnly: (message, gridSize) => {
    const totalCells = gridSize * gridSize;
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, '').padEnd(totalCells, 'X').slice(0, totalCells);

    // Ant cipher: sort letters by pheromone strength
    let letterPheromones = [];
    for (let i = 0; i < cleaned.length; i++) {
      letterPheromones.push({
        letter: cleaned[i],
        pheromone: BIO_CIPHER.letterToPheromone(cleaned[i]),
        originalIndex: i
      });
    }

    // Sort by pheromone strength (chemical trail encoding)
    letterPheromones.sort((a, b) => a.pheromone - b.pheromone || a.originalIndex - b.originalIndex);
    return letterPheromones.map(item => item.letter).join('');
  },

  readSpiderWebPattern: (webGrid, gridSize) => {
    let result = '';
    const center = Math.floor(gridSize / 2);
    let visited = Array.from({ length: gridSize }, () => Array(gridSize).fill(false));

    // Read center first
    if (webGrid[center][center]) {
      result += webGrid[center][center];
      visited[center][center] = true;
    }

    // Read in expanding rings
    for (let ring = 1; ring <= center; ring++) {
      let ringPositions = [];
      for (let row = 0; row < gridSize; row++) {
        for (let col = 0; col < gridSize; col++) {
          const distance = Math.max(Math.abs(row - center), Math.abs(col - center));
          if (distance === ring && !visited[row][col]) {
            ringPositions.push([row, col]);
          }
        }
      }

      ringPositions.sort((a, b) => {
        const [r1, c1] = a;
        const [r2, c2] = b;
        const angle1 = Math.atan2(r1 - center, c1 - center);
        const angle2 = Math.atan2(r2 - center, c2 - center);
        return angle1 - angle2;
      });

      for (let [row, col] of ringPositions) {
        if (webGrid[row][col]) {
          result += webGrid[row][col];
          visited[row][col] = true;
        }
      }
    }

    return result.replace(/X+$/, '');
  }
};

// Bio-Inspired Encryption Grid
function BioEncryptionGrid({ message, gridSize, cipherType }) {
  const [grid, setGrid] = useState([]);
  const [bioData, setBioData] = useState([]);

  useEffect(() => {
    const totalCells = gridSize * gridSize;
    const cleaned = message.toUpperCase().replace(/[^A-Z]/g, '').padEnd(totalCells, 'X').slice(0, totalCells);
    let newGrid = Array(totalCells).fill('');
    let newBioData = [];

    // Fill grid row by row (simple placement for encryption)
    for (let i = 0; i < cleaned.length; i++) {
      const letter = cleaned[i];
      const row = Math.floor(i / gridSize);
      const col = i % gridSize;
      const gridIndex = row * gridSize + col;

      const beeAngle = BIO_CIPHER.letterToBeeAngle(letter);
      const pheromoneLevel = BIO_CIPHER.letterToPheromone(letter);

      newGrid[gridIndex] = letter;
      newBioData.push({
        letter,
        beeAngle,
        pheromoneLevel,
        gridIndex,
        row,
        col
      });
    }

    setGrid(newGrid);
    setBioData(newBioData);
  }, [message, gridSize, cipherType]);

  const getCellStyle = (idx) => {
    const bioInfo = bioData.find(b => b.gridIndex === idx);
    const hasLetter = grid[idx] && grid[idx] !== '';

    // Spider web styling based on position
    const row = Math.floor(idx / gridSize);
    const col = idx % gridSize;
    const center = Math.floor(gridSize / 2);
    const isCenter = row === center && col === center;
    const distanceFromCenter = Math.abs(row - center) + Math.abs(col - center);

    let backgroundColor = '#fef3c7'; // Default color
    let borderColor = '#f59e0b';
    let borderWidth = '2px';

    // Apply styling based on cipher type
    if (cipherType === 'spider' || cipherType === 'combined') {
      if (isCenter) {
        backgroundColor = '#dc2626'; // Spider center (red)
        borderColor = '#991b1b';
        borderWidth = '3px';
      } else if (distanceFromCenter === 1) {
        backgroundColor = '#16a34a'; // Spider ring 1 (green)
        borderColor = '#15803d';
      }
    }

    if (cipherType === 'bee' || cipherType === 'combined') {
      if (hasLetter && bioInfo) {
        // Bee dance angle affects rotation and color intensity
        const angleIntensity = (bioInfo.beeAngle / 360);
        backgroundColor = `rgba(251, 191, 36, ${0.3 + angleIntensity * 0.7})`;
        borderColor = '#f59e0b';
      }
    }

    if (cipherType === 'ant' || cipherType === 'combined') {
      if (hasLetter && bioInfo) {
        // Pheromone strength affects opacity and color
        const opacity = bioInfo.pheromoneLevel / 5;
        if (cipherType === 'ant') {
          backgroundColor = `rgba(34, 197, 94, ${opacity})`;
          borderColor = '#16a34a';
        } else if (cipherType === 'combined' && !isCenter && distanceFromCenter > 1) {
          backgroundColor = `rgba(34, 197, 94, ${opacity})`;
        }
      }
    }

    const cellSize = Math.max(30, Math.min(60, 300 / gridSize)); // Dynamic cell size

    return {
      width: `${cellSize}px`,
      height: `${cellSize}px`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      border: `${borderWidth} solid ${borderColor}`,
      borderRadius: isCenter && (cipherType === 'spider' || cipherType === 'combined') ? '50%' : '8px',
      backgroundColor,
      color: isCenter && (cipherType === 'spider' || cipherType === 'combined') ? 'white' : '#92400e',
      fontWeight: 'bold',
      fontSize: `${Math.max(10, cellSize / 3)}px`,
      position: 'relative'
    };
  };

  return (
    <div>
      <div style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${gridSize}, 1fr)`,
        gap: '4px',
        marginTop: '20px',
        maxWidth: `${Math.min(400, gridSize * 70)}px`,
        margin: '20px auto'
      }}>
        {grid.map((letter, idx) => {
          const bioInfo = bioData.find(b => b.gridIndex === idx);
          return (
            <motion.div
              key={idx}
              style={getCellStyle(idx)}
              initial={{ scale: 0, rotate: bioInfo ? bioInfo.beeAngle : 0 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{
                delay: idx * 0.1,
                type: "spring",
                stiffness: 200,
                damping: 10
              }}
              title={bioInfo ? `🐝 Angle: ${bioInfo.beeAngle}° | 🐜 Pheromone: ${bioInfo.pheromoneLevel}` : ''}
            >
              {letter}
              {bioInfo && (
                <div style={{
                  position: 'absolute',
                  top: '-8px',
                  right: '-8px',
                  width: '16px',
                  height: '16px',
                  borderRadius: '50%',
                  backgroundColor: '#dc2626',
                  color: 'white',
                  fontSize: '10px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  {bioInfo.pheromoneLevel}
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Bio-cipher legend */}
      <div style={{
        marginTop: '20px',
        padding: '15px',
        backgroundColor: '#f9fafb',
        borderRadius: '8px',
        fontSize: '12px'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#374151' }}>
          Active Cipher: {cipherType === 'combined' ? 'All Three Combined' :
                         cipherType === 'spider' ? 'Spider Web Only' :
                         cipherType === 'bee' ? 'Bee Dance Only' : 'Ant Trails Only'}
        </div>
        {(cipherType === 'spider' || cipherType === 'combined') && (
          <div><strong>🕷️ Spider Web:</strong> Red center, green inner ring - reads center outward</div>
        )}
        {(cipherType === 'bee' || cipherType === 'combined') && (
          <div><strong>🐝 Bee Dance:</strong> Rotation angle encodes letter direction (A=0°, B=45°, etc.)</div>
        )}
        {(cipherType === 'ant' || cipherType === 'combined') && (
          <div><strong>🐜 Ant Trails:</strong> Pheromone strength (1-5) shown in opacity and red circles</div>
        )}
      </div>
    </div>
  );
}

// Bio-Inspired Decryption Grid
function BioDecryptionGrid({ message, gridSize, cipherType }) {
  const [grid, setGrid] = useState([]);
  const [decrypted, setDecrypted] = useState('');

  useEffect(() => {
    const encryptedText = message.toUpperCase().replace(/[^A-Z]/g, '');
    const totalCells = gridSize * gridSize;

    if (encryptedText.length === 0) {
      setGrid(Array(totalCells).fill(''));
      setDecrypted('');
      return;
    }

    let result = '';
    let newGrid = Array(totalCells).fill('');

    // Decrypt based on cipher type
    if (cipherType === 'spider' || cipherType === 'combined') {
      // Spider web decryption: reverse the spider web reading pattern
      let webGrid = Array.from({ length: gridSize }, () => Array(gridSize).fill(''));
      let charIndex = 0;
      const center = Math.floor(gridSize / 2);

      // Place first character at center
      if (charIndex < encryptedText.length) {
        webGrid[center][center] = encryptedText[charIndex++];
      }

      // Place remaining characters in rings
      for (let ring = 1; ring <= center; ring++) {
        let ringPositions = [];
        for (let row = 0; row < gridSize; row++) {
          for (let col = 0; col < gridSize; col++) {
            const distance = Math.max(Math.abs(row - center), Math.abs(col - center));
            if (distance === ring) {
              ringPositions.push([row, col]);
            }
          }
        }

        ringPositions.sort((a, b) => {
          const [r1, c1] = a;
          const [r2, c2] = b;
          const angle1 = Math.atan2(r1 - center, c1 - center);
          const angle2 = Math.atan2(r2 - center, c2 - center);
          return angle1 - angle2;
        });

        for (let [row, col] of ringPositions) {
          if (charIndex < encryptedText.length) {
            webGrid[row][col] = encryptedText[charIndex++];
          }
        }
      }

      // Read row by row to get original message
      for (let row = 0; row < gridSize; row++) {
        for (let col = 0; col < gridSize; col++) {
          const letter = webGrid[row][col] || '';
          const gridIndex = row * gridSize + col;
          newGrid[gridIndex] = letter;
          if (letter) result += letter;
        }
      }
    } else if (cipherType === 'bee') {
      // Bee decryption: reverse the angle-based sorting
      let letterAngles = [];
      for (let i = 0; i < encryptedText.length; i++) {
        letterAngles.push({
          letter: encryptedText[i],
          angle: BIO_CIPHER.letterToBeeAngle(encryptedText[i]),
          encryptedIndex: i
        });
      }

      // Sort by original angle order to reverse the encryption
      letterAngles.sort((a, b) => a.encryptedIndex - b.encryptedIndex);

      // Place in grid row by row
      for (let i = 0; i < letterAngles.length && i < totalCells; i++) {
        newGrid[i] = letterAngles[i].letter;
        result += letterAngles[i].letter;
      }
    } else if (cipherType === 'ant') {
      // Ant decryption: reverse the pheromone-based sorting
      let letterPheromones = [];
      for (let i = 0; i < encryptedText.length; i++) {
        letterPheromones.push({
          letter: encryptedText[i],
          pheromone: BIO_CIPHER.letterToPheromone(encryptedText[i]),
          encryptedIndex: i
        });
      }

      // Sort by original pheromone order to reverse the encryption
      letterPheromones.sort((a, b) => a.encryptedIndex - b.encryptedIndex);

      // Place in grid row by row
      for (let i = 0; i < letterPheromones.length && i < totalCells; i++) {
        newGrid[i] = letterPheromones[i].letter;
        result += letterPheromones[i].letter;
      }
    }

    setGrid(newGrid);
    setDecrypted(result.replace(/X+$/, ''));
  }, [message, gridSize, cipherType]);

  return (
    <div>
      <div style={{
        display: 'grid',
        gridTemplateColumns: `repeat(${gridSize}, 1fr)`,
        gap: '4px',
        marginTop: '20px',
        maxWidth: `${Math.min(400, gridSize * 70)}px`,
        margin: '20px auto'
      }}>
        {grid.map((letter, idx) => {
          const row = Math.floor(idx / gridSize);
          const col = idx % gridSize;
          const center = Math.floor(gridSize / 2);
          const isCenter = row === center && col === center;
          const distanceFromCenter = Math.abs(row - center) + Math.abs(col - center);
          const cellSize = Math.max(30, Math.min(60, 300 / gridSize));

          let backgroundColor = '#dbeafe';
          let borderColor = '#3b82f6';

          if (isCenter) {
            backgroundColor = '#7c3aed';
            borderColor = '#5b21b6';
          } else if (distanceFromCenter === 1) {
            backgroundColor = '#059669';
            borderColor = '#047857';
          }

          return (
            <motion.div
              key={idx}
              style={{
                width: `${cellSize}px`,
                height: `${cellSize}px`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: `2px solid ${borderColor}`,
                borderRadius: isCenter ? '50%' : '8px',
                backgroundColor,
                color: isCenter || distanceFromCenter === 1 ? 'white' : '#1e40af',
                fontWeight: 'bold',
                fontSize: `${Math.max(10, cellSize / 3)}px`
              }}
              initial={{ scale: 0, rotate: 180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{
                delay: idx * 0.08,
                type: "spring",
                stiffness: 150,
                damping: 12
              }}
            >
              {letter}
            </motion.div>
          );
        })}
      </div>
      
      {decrypted && (
        <motion.div
          style={{
            marginTop: '20px',
            padding: '15px',
            backgroundColor: '#dcfce7',
            border: '2px solid #16a34a',
            borderRadius: '8px',
            textAlign: 'center'
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2.5 }}
        >
          <strong style={{ color: '#15803d' }}>Decrypted Message: {decrypted}</strong>
        </motion.div>
      )}
    </div>
  );
}

// Pen and Paper Guide Component
function PenPaperGuide({ isVisible, onToggle }) {
  if (!isVisible) {
    return (
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <button
          onClick={onToggle}
          style={{
            padding: '10px 20px',
            backgroundColor: '#059669',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold'
          }}
        >
          📝 Pen & Paper Guide (For Demos)
        </button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      style={{
        backgroundColor: '#f0fdf4',
        border: '2px solid #16a34a',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '30px',
        fontSize: '14px',
        lineHeight: '1.6'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ color: '#15803d', margin: 0 }}>📝 Manual Pen & Paper Guide</h3>
        <button
          onClick={onToggle}
          style={{
            padding: '5px 10px',
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          ✕ Close
        </button>
      </div>

      <div style={{ display: 'grid', gap: '25px' }}>
        {/* Materials Needed */}
        <div style={{ backgroundColor: '#fef3c7', padding: '15px', borderRadius: '8px', border: '1px solid #f59e0b' }}>
          <h4 style={{ color: '#92400e', marginTop: 0 }}>📋 MATERIALS NEEDED</h4>
          <ul style={{ marginLeft: '20px' }}>
            <li>✏️ Pencil and eraser</li>
            <li>📄 Graph paper or ruled paper</li>
            <li>📐 Ruler (optional, for neat grids)</li>
            <li>🎨 Colored pens/pencils (optional, for visual coding)</li>
          </ul>
        </div>

        {/* Encryption Steps */}
        <div style={{ backgroundColor: '#fef3c7', padding: '15px', borderRadius: '8px', border: '1px solid #f59e0b' }}>
          <h4 style={{ color: '#92400e', marginTop: 0 }}>🔐 MANUAL ENCRYPTION STEPS</h4>

          <div style={{ marginLeft: '10px' }}>
            <p><strong>Step 1: Prepare Your Message</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: 0 }}>✍️ <strong>Example:</strong> "MEET AT NOON"</p>
              <p style={{ margin: 0 }}>🧹 <strong>Clean:</strong> Remove spaces → "MEETATNOON"</p>
              <p style={{ margin: 0 }}>📏 <strong>Count:</strong> 10 letters → Need at least 4×4 grid (16 spaces)</p>
              <p style={{ margin: 0 }}>➕ <strong>Pad:</strong> "MEETATNOONXXXXXX" (16 letters total)</p>
            </div>

            <p><strong>Step 2: Draw Your Grid</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: '0 0 10px 0' }}>📐 Draw a 4×4 grid (or your chosen size):</p>
              <div style={{ fontFamily: 'monospace', fontSize: '16px', lineHeight: '1.2' }}>
                ┌───┬───┬───┬───┐<br/>
                │   │   │   │   │<br/>
                ├───┼───┼───┼───┤<br/>
                │   │   │   │   │<br/>
                ├───┼───┼───┼───┤<br/>
                │   │   │   │   │<br/>
                ├───┼───┼───┼───┤<br/>
                │   │   │   │   │<br/>
                └───┴───┴───┴───┘
              </div>
            </div>

            <p><strong>Step 3: Fill Grid Row by Row</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: '0 0 10px 0' }}>✍️ Write letters left to right, top to bottom:</p>
              <div style={{ fontFamily: 'monospace', fontSize: '16px', lineHeight: '1.2' }}>
                ┌───┬───┬───┬───┐<br/>
                │ M │ E │ E │ T │<br/>
                ├───┼───┼───┼───┤<br/>
                │ A │ T │ N │ O │<br/>
                ├───┼───┼───┼───┤<br/>
                │ O │ N │ X │ X │<br/>
                ├───┼───┼───┼───┤<br/>
                │ X │ X │ X │ X │<br/>
                └───┴───┴───┴───┘
              </div>
            </div>

            <p><strong>Step 4: Mark the Spider Web Pattern</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: '0 0 10px 0' }}>🕷️ Number the cells in spider web order (center outward):</p>
              <div style={{ fontFamily: 'monospace', fontSize: '14px', lineHeight: '1.2' }}>
                ┌───┬───┬───┬───┐<br/>
                │ 9 │ 2 │ 3 │10 │<br/>
                ├───┼───┼───┼───┤<br/>
                │ 8 │ 1 │ 4 │11 │<br/>
                ├───┼───┼───┼───┤<br/>
                │ 7 │ 6 │ 5 │12 │<br/>
                ├───┼───┼───┼───┤<br/>
                │16 │15 │14 │13 │<br/>
                └───┴───┴───┴───┘
              </div>
              <p style={{ margin: '10px 0 0 0', fontSize: '12px' }}>
                🎯 <strong>Pattern:</strong> Start at center (1), then spiral outward clockwise
              </p>
            </div>

            <p><strong>Step 5: Read in Spider Web Order</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: '0 0 10px 0' }}>📖 Follow the numbers to read letters:</p>
              <p style={{ margin: 0, fontFamily: 'monospace' }}>
                1→T, 2→E, 3→E, 4→N, 5→X, 6→N, 7→O, 8→A, 9→M, 10→T, 11→O, 12→X, 13→X, 14→X, 15→X, 16→X
              </p>
              <p style={{ margin: '10px 0 0 0', fontWeight: 'bold', color: '#dc2626' }}>
                🔐 <strong>Encrypted Result:</strong> TEENXNOAMTOXXX
              </p>
            </div>
          </div>
        </div>

        {/* Decryption Steps */}
        <div style={{ backgroundColor: '#dbeafe', padding: '15px', borderRadius: '8px', border: '1px solid #3b82f6' }}>
          <h4 style={{ color: '#1e40af', marginTop: 0 }}>🔓 MANUAL DECRYPTION STEPS</h4>

          <div style={{ marginLeft: '10px' }}>
            <p><strong>Step 1: Prepare the Encrypted Message</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: 0 }}>📝 <strong>Given:</strong> "TEENXNOAMTOXXX"</p>
              <p style={{ margin: 0 }}>📏 <strong>Count:</strong> 14 letters → Use 4×4 grid (16 spaces)</p>
            </div>

            <p><strong>Step 2: Draw Empty Grid with Spider Numbers</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <div style={{ fontFamily: 'monospace', fontSize: '14px', lineHeight: '1.2' }}>
                ┌───┬───┬───┬───┐<br/>
                │ 9 │ 2 │ 3 │10 │<br/>
                ├───┼───┼───┼───┤<br/>
                │ 8 │ 1 │ 4 │11 │<br/>
                ├───┼───┼───┼───┤<br/>
                │ 7 │ 6 │ 5 │12 │<br/>
                ├───┼───┼───┼───┤<br/>
                │16 │15 │14 │13 │<br/>
                └───┴───┴───┴───┘
              </div>
            </div>

            <p><strong>Step 3: Place Letters in Spider Web Order</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: '0 0 10px 0' }}>🕷️ Place each letter according to its spider web number:</p>
              <p style={{ margin: '0 0 10px 0', fontFamily: 'monospace', fontSize: '12px' }}>
                T→1, E→2, E→3, N→4, X→5, N→6, O→7, A→8, M→9, T→10, O→11, X→12, X→13, X→14, X→15, X→16
              </p>
              <div style={{ fontFamily: 'monospace', fontSize: '16px', lineHeight: '1.2' }}>
                ┌───┬───┬───┬───┐<br/>
                │ M │ E │ E │ T │<br/>
                ├───┼───┼───┼───┤<br/>
                │ A │ T │ N │ O │<br/>
                ├───┼───┼───┼───┤<br/>
                │ O │ N │ X │ X │<br/>
                ├───┼───┼───┼───┤<br/>
                │ X │ X │ X │ X │<br/>
                └───┴───┴───┴───┘
              </div>
            </div>

            <p><strong>Step 4: Read Row by Row</strong></p>
            <div style={{ backgroundColor: 'white', padding: '10px', borderRadius: '4px', marginBottom: '10px' }}>
              <p style={{ margin: '0 0 10px 0' }}>📖 Read left to right, top to bottom:</p>
              <p style={{ margin: 0, fontFamily: 'monospace' }}>
                Row 1: M-E-E-T<br/>
                Row 2: A-T-N-O<br/>
                Row 3: O-N-X-X<br/>
                Row 4: X-X-X-X
              </p>
              <p style={{ margin: '10px 0 0 0' }}>
                🧹 <strong>Remove padding X's:</strong> MEETATNOON
              </p>
              <p style={{ margin: '10px 0 0 0', fontWeight: 'bold', color: '#16a34a' }}>
                🎉 <strong>Decrypted Message:</strong> "MEET AT NOON"
              </p>
            </div>
          </div>
        </div>

        {/* Quick Reference */}
        <div style={{ backgroundColor: '#fdf4ff', padding: '15px', borderRadius: '8px', border: '1px solid #a855f7' }}>
          <h4 style={{ color: '#7c3aed', marginTop: 0 }}>⚡ QUICK REFERENCE CARD</h4>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
            <div>
              <p><strong>🔐 Encryption:</strong></p>
              <ol style={{ fontSize: '12px', marginLeft: '15px' }}>
                <li>Clean message (letters only)</li>
                <li>Fill grid row by row</li>
                <li>Read in spider web pattern</li>
              </ol>
            </div>
            <div>
              <p><strong>🔓 Decryption:</strong></p>
              <ol style={{ fontSize: '12px', marginLeft: '15px' }}>
                <li>Place letters in spider web order</li>
                <li>Read grid row by row</li>
                <li>Remove padding X's</li>
              </ol>
            </div>
          </div>

          <div style={{ marginTop: '15px', padding: '10px', backgroundColor: 'white', borderRadius: '4px' }}>
            <p style={{ margin: '0 0 10px 0', fontWeight: 'bold' }}>🕷️ Spider Web Pattern (for any grid size):</p>
            <p style={{ margin: 0, fontSize: '12px' }}>
              1. Find center of grid<br/>
              2. Number center as "1"<br/>
              3. Move outward in rings<br/>
              4. Within each ring, go clockwise<br/>
              5. Continue until all cells numbered
            </p>
          </div>
        </div>

        {/* Individual Cipher Instructions */}
        <div style={{ backgroundColor: '#f0f9ff', padding: '15px', borderRadius: '8px', border: '1px solid #0ea5e9' }}>
          <h4 style={{ color: '#0369a1', marginTop: 0 }}>🔧 INDIVIDUAL CIPHER METHODS</h4>

          <div style={{ marginBottom: '20px' }}>
            <h5 style={{ color: '#dc2626', margin: '10px 0 5px 0' }}>🐝 BEE DANCE CIPHER (Manual Steps)</h5>
            <ol style={{ marginLeft: '20px', fontSize: '12px' }}>
              <li><strong>Assign angles:</strong> A=0°, B=45°, C=90°, D=135°, E=180°, F=225°, G=270°, H=315°, then repeat (I=0°, J=45°, etc.)</li>
              <li><strong>List letters with angles:</strong> For "HELLO" → H=315°, E=180°, L=90°, L=90°, O=135°</li>
              <li><strong>Sort by angle:</strong> L=90°, L=90°, O=135°, E=180°, H=315°</li>
              <li><strong>Encrypted result:</strong> "LLOEH"</li>
              <li><strong>To decrypt:</strong> Reverse the process by restoring original order</li>
            </ol>
          </div>

          <div style={{ marginBottom: '20px' }}>
            <h5 style={{ color: '#16a34a', margin: '10px 0 5px 0' }}>🐜 ANT PHEROMONE CIPHER (Manual Steps)</h5>
            <ol style={{ marginLeft: '20px', fontSize: '12px' }}>
              <li><strong>Assign pheromone levels:</strong> A,F,K,P,U=1; B,G,L,Q,V=2; C,H,M,R,W=3; D,I,N,S,X=4; E,J,O,T,Y,Z=5</li>
              <li><strong>List letters with levels:</strong> For "HELLO" → H=3, E=5, L=2, L=2, O=5</li>
              <li><strong>Sort by pheromone level:</strong> L=2, L=2, H=3, E=5, O=5</li>
              <li><strong>Encrypted result:</strong> "LLHEO"</li>
              <li><strong>To decrypt:</strong> Reverse the process by restoring original order</li>
            </ol>
          </div>

          <div style={{ marginBottom: '20px' }}>
            <h5 style={{ color: '#7c3aed', margin: '10px 0 5px 0' }}>🕷️ SPIDER WEB CIPHER (Manual Steps)</h5>
            <p style={{ fontSize: '12px', marginLeft: '20px' }}>
              <strong>This is the main method shown in the detailed steps above.</strong> Fill grid row-by-row, then read in spider web pattern (center outward, clockwise in each ring).
            </p>
          </div>
        </div>

        {/* Demo Tips */}
        <div style={{ backgroundColor: '#fef2f2', padding: '15px', borderRadius: '8px', border: '1px solid #ef4444' }}>
          <h4 style={{ color: '#dc2626', marginTop: 0 }}>🎭 PRESENTATION & DEMO TIPS</h4>
          <ul style={{ marginLeft: '20px', fontSize: '13px' }}>
            <li>🎨 <strong>Use colors:</strong> Different colors for each ring of the spider web</li>
            <li>📏 <strong>Large grids:</strong> Use poster board or whiteboard for audience visibility</li>
            <li>👥 <strong>Audience participation:</strong> Have volunteers help fill the grid</li>
            <li>🔄 <strong>Show both ways:</strong> Encrypt a message, then decrypt it back</li>
            <li>🌟 <strong>Bio-connection:</strong> Explain the spider, bee, and ant inspiration</li>
            <li>📱 <strong>Compare:</strong> Show manual vs. digital tool results</li>
            <li>🎯 <strong>Start simple:</strong> Use 3×3 grid with short words like "HELLO"</li>
            <li>🔀 <strong>Try different ciphers:</strong> Show how each bio-inspired method works differently</li>
            <li>📊 <strong>Compare results:</strong> Encrypt same message with spider, bee, and ant methods</li>
          </ul>
        </div>
      </div>
    </motion.div>
  );
}

// How It Works Explanation Component
function HowItWorksExplanation({ isVisible, onToggle }) {
  if (!isVisible) {
    return (
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <button
          onClick={onToggle}
          style={{
            padding: '10px 20px',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '14px',
            fontWeight: 'bold'
          }}
        >
          📚 How Does This Cipher Work?
        </button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      style={{
        backgroundColor: '#f8fafc',
        border: '2px solid #e2e8f0',
        borderRadius: '12px',
        padding: '20px',
        marginBottom: '30px',
        fontSize: '14px',
        lineHeight: '1.6'
      }}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ color: '#1e293b', margin: 0 }}>🧠 How the Bio-Inspired Cipher Works</h3>
        <button
          onClick={onToggle}
          style={{
            padding: '5px 10px',
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          ✕ Close
        </button>
      </div>

      <div style={{ display: 'grid', gap: '20px' }}>
        {/* Encryption Process */}
        <div style={{ backgroundColor: '#fef3c7', padding: '15px', borderRadius: '8px', border: '1px solid #f59e0b' }}>
          <h4 style={{ color: '#92400e', marginTop: 0 }}>🔐 ENCRYPTION PROCESS</h4>
          <div style={{ marginLeft: '10px' }}>
            <p><strong>Step 1: Message Preparation</strong></p>
            <ul style={{ marginLeft: '20px' }}>
              <li>Your message is cleaned (only letters A-Z kept)</li>
              <li>Padded with 'X' characters to fill the grid completely</li>
              <li>Example: "HELLO" → "HELLOX..." for a 5×5 grid (25 characters)</li>
            </ul>

            <p><strong>Step 2: Grid Filling (Like Ants Building)</strong></p>
            <ul style={{ marginLeft: '20px' }}>
              <li>Letters are placed in the grid <em>row by row</em>, left to right</li>
              <li>Just like ants systematically building their nest</li>
              <li>Each letter gets bio-inspired properties:</li>
              <li style={{ marginLeft: '20px' }}>🐝 <strong>Bee Angle:</strong> Each letter has a dance direction (A=0°, B=45°, etc.)</li>
              <li style={{ marginLeft: '20px' }}>🐜 <strong>Pheromone Level:</strong> Strength from 1-5 based on the letter</li>
            </ul>

            <p><strong>Step 3: Spider Web Reading Pattern</strong></p>
            <ul style={{ marginLeft: '20px' }}>
              <li>🕷️ <strong>Start from center:</strong> Like a spider beginning its web</li>
              <li>📍 <strong>Expand in rings:</strong> Read outward in concentric circles</li>
              <li>🔄 <strong>Clockwise spiral:</strong> Within each ring, go clockwise</li>
              <li>This creates the encrypted message by reading in spider-web order!</li>
            </ul>
          </div>
        </div>

        {/* Decryption Process */}
        <div style={{ backgroundColor: '#dbeafe', padding: '15px', borderRadius: '8px', border: '1px solid #3b82f6' }}>
          <h4 style={{ color: '#1e40af', marginTop: 0 }}>🔓 DECRYPTION PROCESS</h4>
          <div style={{ marginLeft: '10px' }}>
            <p><strong>Step 1: Reverse Spider Web Placement</strong></p>
            <ul style={{ marginLeft: '20px' }}>
              <li>Take the encrypted message and place it back using spider web pattern</li>
              <li>🕷️ <strong>First character:</strong> Goes to center of the web</li>
              <li>📍 <strong>Next characters:</strong> Fill rings outward (center → ring 1 → ring 2...)</li>
              <li>🔄 <strong>Clockwise order:</strong> Within each ring, place clockwise</li>
            </ul>

            <p><strong>Step 2: Row-by-Row Reading</strong></p>
            <ul style={{ marginLeft: '20px' }}>
              <li>Once the grid is filled using spider pattern, read it normally</li>
              <li>📖 <strong>Left to right, top to bottom</strong> (like reading a book)</li>
              <li>This reverses the encryption and reveals the original message!</li>
            </ul>

            <p><strong>Step 3: Clean Up</strong></p>
            <ul style={{ marginLeft: '20px' }}>
              <li>Remove trailing 'X' characters that were padding</li>
              <li>🎉 <strong>Result:</strong> Your original message is revealed!</li>
            </ul>
          </div>
        </div>

        {/* Visual Example */}
        <div style={{ backgroundColor: '#f0fdf4', padding: '15px', borderRadius: '8px', border: '1px solid #16a34a' }}>
          <h4 style={{ color: '#15803d', marginTop: 0 }}>📋 SIMPLE EXAMPLE</h4>
          <div style={{ marginLeft: '10px' }}>
            <p><strong>Message:</strong> "HELLO" (using 3×3 grid)</p>

            <div style={{ display: 'flex', gap: '20px', flexWrap: 'wrap', alignItems: 'flex-start' }}>
              <div>
                <p><strong>1. Fill row by row:</strong></p>
                <div style={{ fontFamily: 'monospace', backgroundColor: 'white', padding: '10px', borderRadius: '4px' }}>
                  H E L<br/>
                  L O X<br/>
                  X X X
                </div>
              </div>

              <div>
                <p><strong>2. Read spider-web style:</strong></p>
                <div style={{ fontFamily: 'monospace', backgroundColor: 'white', padding: '10px', borderRadius: '4px' }}>
                  3 1 4<br/>
                  2 🕷️ 5<br/>
                  8 7 6
                </div>
                <p style={{ fontSize: '12px', margin: '5px 0' }}>🕷️ = center (start here)</p>
              </div>

              <div>
                <p><strong>3. Encrypted result:</strong></p>
                <div style={{ fontFamily: 'monospace', backgroundColor: 'white', padding: '10px', borderRadius: '4px', fontWeight: 'bold', color: '#dc2626' }}>
                  OHELXLXXX
                </div>
                <p style={{ fontSize: '12px', margin: '5px 0' }}>Reading: O(center) → H,E,L,L(ring1) → X,X,X,X(ring2)</p>
              </div>
            </div>
          </div>
        </div>

        {/* Individual Cipher Explanations */}
        <div style={{ backgroundColor: '#fdf4ff', padding: '15px', borderRadius: '8px', border: '1px solid #a855f7' }}>
          <h4 style={{ color: '#7c3aed', marginTop: 0 }}>🌿 INDIVIDUAL CIPHER METHODS</h4>
          <div style={{ marginLeft: '10px' }}>
            <div style={{ marginBottom: '15px' }}>
              <p><strong>🕷️ Spider Web Cipher:</strong></p>
              <ul style={{ marginLeft: '20px', fontSize: '13px' }}>
                <li><strong>Encryption:</strong> Fill grid row-by-row, then read in spider web pattern (center → rings clockwise)</li>
                <li><strong>Decryption:</strong> Place encrypted letters in spider web order, then read row-by-row</li>
                <li><strong>Inspiration:</strong> Spiders build webs from center outward in organized spiral patterns</li>
              </ul>
            </div>

            <div style={{ marginBottom: '15px' }}>
              <p><strong>🐝 Bee Dance Cipher:</strong></p>
              <ul style={{ marginLeft: '20px', fontSize: '13px' }}>
                <li><strong>Encryption:</strong> Sort letters by their "dance angles" (A=0°, B=45°, C=90°, etc.)</li>
                <li><strong>Decryption:</strong> Reverse the angle-based sorting to restore original order</li>
                <li><strong>Inspiration:</strong> Bees communicate direction and distance through waggle dance angles</li>
              </ul>
            </div>

            <div style={{ marginBottom: '15px' }}>
              <p><strong>🐜 Ant Pheromone Cipher:</strong></p>
              <ul style={{ marginLeft: '20px', fontSize: '13px' }}>
                <li><strong>Encryption:</strong> Sort letters by "pheromone strength" (A,F,K,P,U=1; B,G,L,Q,V=2; etc.)</li>
                <li><strong>Decryption:</strong> Reverse the pheromone-based sorting to restore original order</li>
                <li><strong>Inspiration:</strong> Ants leave chemical trails with varying strengths to guide colony members</li>
              </ul>
            </div>

            <div>
              <p><strong>🕷️🐝🐜 Combined Cipher:</strong></p>
              <ul style={{ marginLeft: '20px', fontSize: '13px' }}>
                <li><strong>Primary:</strong> Uses spider web pattern for main encryption</li>
                <li><strong>Visual:</strong> Bee angles and ant pheromones provide additional encoding layers</li>
                <li><strong>Security:</strong> Multiple bio-inspired patterns make the cipher more complex</li>
              </ul>
            </div>

            <p style={{ fontStyle: 'italic', color: '#6b46c1', marginTop: '15px' }}>
              Each cipher can be used independently or combined for enhanced security. Nature's communication methods inspire beautiful and secure encryption patterns!
            </p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

// Main Spiral Cipher Tool Component
function SpiralCipherTool() {
  const [message, setMessage] = useState('MEETATNOON');
  const [mode, setMode] = useState('encrypt');
  const [gridSize, setGridSize] = useState(5);
  const [cipherType, setCipherType] = useState('combined');
  const [showExplanation, setShowExplanation] = useState(false);
  const [showPenPaperGuide, setShowPenPaperGuide] = useState(false);

  const getBioEncryptedResult = () => {
    if (mode !== 'encrypt') return '';

    // Use the appropriate encryption method based on cipher type
    switch (cipherType) {
      case 'spider':
        return BIO_CIPHER.encryptWithSpiderOnly(message, gridSize);
      case 'bee':
        return BIO_CIPHER.encryptWithBeeOnly(message, gridSize);
      case 'ant':
        return BIO_CIPHER.encryptWithAntOnly(message, gridSize);
      case 'combined':
      default:
        // Combined approach: use spider web pattern as base
        return BIO_CIPHER.encryptWithSpiderOnly(message, gridSize);
    }
  };

  return (
    <div style={{
      backgroundColor: '#ffffff',
      border: '2px solid #e5e7eb',
      borderRadius: '16px',
      padding: '30px',
      boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
    }}>
      <h2 style={{
        fontSize: '28px',
        fontWeight: 'bold',
        color: '#374151',
        marginBottom: '20px',
        textAlign: 'center'
      }}>
        �️🐝🐜 Bio-Inspired Cipher: Nature's Communication Secrets
      </h2>

      <div style={{
        backgroundColor: '#f0f9ff',
        border: '1px solid #0ea5e9',
        borderRadius: '8px',
        padding: '15px',
        marginBottom: '20px',
        fontSize: '14px',
        textAlign: 'center'
      }}>
        <strong>Inspired by Nature:</strong> Spider web construction 🕷️ + Bee waggle dance 🐝 + Ant pheromone trails 🐜
      </div>

      {/* Pen and Paper Guide */}
      <PenPaperGuide
        isVisible={showPenPaperGuide}
        onToggle={() => setShowPenPaperGuide(!showPenPaperGuide)}
      />

      {/* How It Works Explanation */}
      <HowItWorksExplanation
        isVisible={showExplanation}
        onToggle={() => setShowExplanation(!showExplanation)}
      />
      
      {/* Input Section */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{ 
          display: 'block', 
          marginBottom: '8px', 
          fontWeight: 'bold',
          color: '#374151'
        }}>
          Enter your message:
        </label>
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Enter your message here..."
          style={{
            width: '100%',
            padding: '12px 16px',
            border: '2px solid #d1d5db',
            borderRadius: '8px',
            fontSize: '16px',
            outline: 'none',
            transition: 'border-color 0.2s',
          }}
          onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
          onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
        />
      </div>
      
      {/* Grid Size Selection */}
      <div style={{
        marginBottom: '20px',
        textAlign: 'center'
      }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#374151'
        }}>
          🕸️ Spider Web Size (Grid Dimensions):
        </label>
        <div style={{
          display: 'flex',
          gap: '8px',
          justifyContent: 'center',
          flexWrap: 'wrap'
        }}>
          {[3, 4, 5, 6, 7, 8].map(size => (
            <button
              key={size}
              onClick={() => setGridSize(size)}
              style={{
                padding: '8px 16px',
                border: '2px solid',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: 'bold',
                transition: 'all 0.2s',
                backgroundColor: gridSize === size ? '#8b5cf6' : '#f3f4f6',
                borderColor: gridSize === size ? '#7c3aed' : '#d1d5db',
                color: gridSize === size ? 'white' : '#374151',
                transform: gridSize === size ? 'scale(1.05)' : 'scale(1)'
              }}
            >
              {size}×{size}
            </button>
          ))}
        </div>
        <p style={{
          fontSize: '12px',
          color: '#6b7280',
          marginTop: '8px'
        }}>
          Larger grids can hold more characters ({gridSize}×{gridSize} = {gridSize * gridSize} characters)
        </p>
      </div>

      {/* Cipher Type Selection */}
      <div style={{
        marginBottom: '20px',
        textAlign: 'center'
      }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#374151'
        }}>
          🌿 Choose Your Bio-Inspired Cipher:
        </label>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '8px',
          justifyContent: 'center',
          maxWidth: '800px',
          margin: '0 auto'
        }}>
          <button
            onClick={() => setCipherType('combined')}
            style={{
              padding: '12px 16px',
              border: '2px solid',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'all 0.2s',
              backgroundColor: cipherType === 'combined' ? '#8b5cf6' : '#f3f4f6',
              borderColor: cipherType === 'combined' ? '#7c3aed' : '#d1d5db',
              color: cipherType === 'combined' ? 'white' : '#374151',
              transform: cipherType === 'combined' ? 'scale(1.02)' : 'scale(1)'
            }}
          >
            🕷️🐝🐜 All Combined
          </button>
          <button
            onClick={() => setCipherType('spider')}
            style={{
              padding: '12px 16px',
              border: '2px solid',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'all 0.2s',
              backgroundColor: cipherType === 'spider' ? '#dc2626' : '#fef2f2',
              borderColor: cipherType === 'spider' ? '#991b1b' : '#fecaca',
              color: cipherType === 'spider' ? 'white' : '#dc2626',
              transform: cipherType === 'spider' ? 'scale(1.02)' : 'scale(1)'
            }}
          >
            🕷️ Spider Web Only
          </button>
          <button
            onClick={() => setCipherType('bee')}
            style={{
              padding: '12px 16px',
              border: '2px solid',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'all 0.2s',
              backgroundColor: cipherType === 'bee' ? '#f59e0b' : '#fef3c7',
              borderColor: cipherType === 'bee' ? '#d97706' : '#fde68a',
              color: cipherType === 'bee' ? 'white' : '#92400e',
              transform: cipherType === 'bee' ? 'scale(1.02)' : 'scale(1)'
            }}
          >
            🐝 Bee Dance Only
          </button>
          <button
            onClick={() => setCipherType('ant')}
            style={{
              padding: '12px 16px',
              border: '2px solid',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: 'bold',
              transition: 'all 0.2s',
              backgroundColor: cipherType === 'ant' ? '#16a34a' : '#f0fdf4',
              borderColor: cipherType === 'ant' ? '#15803d' : '#bbf7d0',
              color: cipherType === 'ant' ? 'white' : '#16a34a',
              transform: cipherType === 'ant' ? 'scale(1.02)' : 'scale(1)'
            }}
          >
            🐜 Ant Trails Only
          </button>
        </div>
        <p style={{
          fontSize: '12px',
          color: '#6b7280',
          marginTop: '8px'
        }}>
          {cipherType === 'combined' && 'Uses spider web pattern with bee and ant visual encoding'}
          {cipherType === 'spider' && 'Encrypts by reading grid in spider web pattern (center outward)'}
          {cipherType === 'bee' && 'Encrypts by sorting letters based on bee dance angles'}
          {cipherType === 'ant' && 'Encrypts by sorting letters based on ant pheromone strength'}
        </p>
      </div>

      {/* Mode Selection */}
      <div style={{
        display: 'flex',
        gap: '12px',
        marginBottom: '30px',
        justifyContent: 'center'
      }}>
        <button
          onClick={() => setMode('encrypt')}
          style={{
            padding: '12px 24px',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            transition: 'all 0.2s',
            backgroundColor: mode === 'encrypt' ? '#f59e0b' : '#fef3c7',
            color: mode === 'encrypt' ? 'white' : '#92400e',
            transform: mode === 'encrypt' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          🕷️ Encrypt
        </button>
        <button
          onClick={() => setMode('decrypt')}
          style={{
            padding: '12px 24px',
            border: 'none',
            borderRadius: '8px',
            cursor: 'pointer',
            fontSize: '16px',
            fontWeight: 'bold',
            transition: 'all 0.2s',
            backgroundColor: mode === 'decrypt' ? '#3b82f6' : '#dbeafe',
            color: mode === 'decrypt' ? 'white' : '#1e40af',
            transform: mode === 'decrypt' ? 'scale(1.05)' : 'scale(1)'
          }}
        >
          🔓 Decrypt
        </button>
      </div>
      
      {/* Bio-Inspired Visualization */}
      {mode === 'encrypt' ? (
        <div>
          <h3 style={{ textAlign: 'center', color: '#92400e', marginBottom: '10px' }}>
            {cipherType === 'combined' && '🕷️ Spider Web Construction + 🐝 Bee Dance + 🐜 Ant Trails'}
            {cipherType === 'spider' && '🕷️ Spider Web Construction'}
            {cipherType === 'bee' && '🐝 Bee Waggle Dance Encryption'}
            {cipherType === 'ant' && '🐜 Ant Pheromone Trail Encryption'}
          </h3>
          <BioEncryptionGrid message={message} gridSize={gridSize} cipherType={cipherType} />
          {message && (
            <div style={{
              marginTop: '20px',
              padding: '15px',
              backgroundColor: '#fef3c7',
              border: '2px solid #f59e0b',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <strong style={{ color: '#92400e' }}>
                🔐 Bio-Encrypted: {getBioEncryptedResult()}
              </strong>
            </div>
          )}
        </div>
      ) : (
        <div>
          <h3 style={{ textAlign: 'center', color: '#1e40af', marginBottom: '10px' }}>
            🔍 Bio-Pattern Analysis & Decryption
          </h3>
          <BioDecryptionGrid message={message} gridSize={gridSize} cipherType={cipherType} />
        </div>
      )}
    </div>
  );
}

export default SpiralCipherTool;
