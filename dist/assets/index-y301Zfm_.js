(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Wh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ld={exports:{}},vo={},ad={exports:{}},N={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qr=Symbol.for("react.element"),Uh=Symbol.for("react.portal"),Hh=Symbol.for("react.fragment"),$h=Symbol.for("react.strict_mode"),Xh=Symbol.for("react.profiler"),Gh=Symbol.for("react.provider"),Kh=Symbol.for("react.context"),Qh=Symbol.for("react.forward_ref"),Yh=Symbol.for("react.suspense"),Zh=Symbol.for("react.memo"),Jh=Symbol.for("react.lazy"),Qa=Symbol.iterator;function qh(e){return e===null||typeof e!="object"?null:(e=Qa&&e[Qa]||e["@@iterator"],typeof e=="function"?e:null)}var ud={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cd=Object.assign,dd={};function Zn(e,t,n){this.props=e,this.context=t,this.refs=dd,this.updater=n||ud}Zn.prototype.isReactComponent={};Zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function fd(){}fd.prototype=Zn.prototype;function bl(e,t,n){this.props=e,this.context=t,this.refs=dd,this.updater=n||ud}var Vl=bl.prototype=new fd;Vl.constructor=bl;cd(Vl,Zn.prototype);Vl.isPureReactComponent=!0;var Ya=Array.isArray,pd=Object.prototype.hasOwnProperty,Ol={current:null},hd={key:!0,ref:!0,__self:!0,__source:!0};function md(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)pd.call(t,r)&&!hd.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var u=Array(a),c=0;c<a;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:Qr,type:e,key:o,ref:s,props:i,_owner:Ol.current}}function em(e,t){return{$$typeof:Qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Nl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qr}function tm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Za=/\/+/g;function Uo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?tm(""+e.key):t.toString(36)}function Ei(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Qr:case Uh:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Uo(s,0):r,Ya(i)?(n="",e!=null&&(n=e.replace(Za,"$&/")+"/"),Ei(i,t,n,"",function(c){return c})):i!=null&&(Nl(i)&&(i=em(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Za,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Ya(e))for(var a=0;a<e.length;a++){o=e[a];var u=r+Uo(o,a);s+=Ei(o,t,n,u,i)}else if(u=qh(e),typeof u=="function")for(e=u.call(e),a=0;!(o=e.next()).done;)o=o.value,u=r+Uo(o,a++),s+=Ei(o,t,n,u,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function oi(e,t,n){if(e==null)return e;var r=[],i=0;return Ei(e,r,"","",function(o){return t.call(n,o,i++)}),r}function nm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Se={current:null},Ti={transition:null},rm={ReactCurrentDispatcher:Se,ReactCurrentBatchConfig:Ti,ReactCurrentOwner:Ol};function gd(){throw Error("act(...) is not supported in production builds of React.")}N.Children={map:oi,forEach:function(e,t,n){oi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return oi(e,function(){t++}),t},toArray:function(e){return oi(e,function(t){return t})||[]},only:function(e){if(!Nl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};N.Component=Zn;N.Fragment=Hh;N.Profiler=Xh;N.PureComponent=bl;N.StrictMode=$h;N.Suspense=Yh;N.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rm;N.act=gd;N.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cd({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Ol.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(u in t)pd.call(t,u)&&!hd.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&a!==void 0?a[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){a=Array(u);for(var c=0;c<u;c++)a[c]=arguments[c+2];r.children=a}return{$$typeof:Qr,type:e.type,key:i,ref:o,props:r,_owner:s}};N.createContext=function(e){return e={$$typeof:Kh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Gh,_context:e},e.Consumer=e};N.createElement=md;N.createFactory=function(e){var t=md.bind(null,e);return t.type=e,t};N.createRef=function(){return{current:null}};N.forwardRef=function(e){return{$$typeof:Qh,render:e}};N.isValidElement=Nl;N.lazy=function(e){return{$$typeof:Jh,_payload:{_status:-1,_result:e},_init:nm}};N.memo=function(e,t){return{$$typeof:Zh,type:e,compare:t===void 0?null:t}};N.startTransition=function(e){var t=Ti.transition;Ti.transition={};try{e()}finally{Ti.transition=t}};N.unstable_act=gd;N.useCallback=function(e,t){return Se.current.useCallback(e,t)};N.useContext=function(e){return Se.current.useContext(e)};N.useDebugValue=function(){};N.useDeferredValue=function(e){return Se.current.useDeferredValue(e)};N.useEffect=function(e,t){return Se.current.useEffect(e,t)};N.useId=function(){return Se.current.useId()};N.useImperativeHandle=function(e,t,n){return Se.current.useImperativeHandle(e,t,n)};N.useInsertionEffect=function(e,t){return Se.current.useInsertionEffect(e,t)};N.useLayoutEffect=function(e,t){return Se.current.useLayoutEffect(e,t)};N.useMemo=function(e,t){return Se.current.useMemo(e,t)};N.useReducer=function(e,t,n){return Se.current.useReducer(e,t,n)};N.useRef=function(e){return Se.current.useRef(e)};N.useState=function(e){return Se.current.useState(e)};N.useSyncExternalStore=function(e,t,n){return Se.current.useSyncExternalStore(e,t,n)};N.useTransition=function(){return Se.current.useTransition()};N.version="18.3.1";ad.exports=N;var L=ad.exports;const zl=Wh(L);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var im=L,om=Symbol.for("react.element"),sm=Symbol.for("react.fragment"),lm=Object.prototype.hasOwnProperty,am=im.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,um={key:!0,ref:!0,__self:!0,__source:!0};function yd(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)lm.call(t,r)&&!um.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:om,type:e,key:o,ref:s,props:i,_owner:am.current}}vo.Fragment=sm;vo.jsx=yd;vo.jsxs=yd;ld.exports=vo;var l=ld.exports,Ts={},xd={exports:{}},Ve={},vd={exports:{}},Sd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,M){var O=T.length;T.push(M);e:for(;0<O;){var b=O-1>>>1,U=T[b];if(0<i(U,M))T[b]=M,T[O]=U,O=b;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var M=T[0],O=T.pop();if(O!==M){T[0]=O;e:for(var b=0,U=T.length,Gt=U>>>1;b<Gt;){var qe=2*(b+1)-1,xn=T[qe],Re=qe+1,Kt=T[Re];if(0>i(xn,O))Re<U&&0>i(Kt,xn)?(T[b]=Kt,T[Re]=O,b=Re):(T[b]=xn,T[qe]=O,b=qe);else if(Re<U&&0>i(Kt,O))T[b]=Kt,T[Re]=O,b=Re;else break e}}return M}function i(T,M){var O=T.sortIndex-M.sortIndex;return O!==0?O:T.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var u=[],c=[],f=1,d=null,p=3,m=!1,x=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(T){for(var M=n(c);M!==null;){if(M.callback===null)r(c);else if(M.startTime<=T)r(c),M.sortIndex=M.expirationTime,t(u,M);else break;M=n(c)}}function S(T){if(v=!1,g(T),!x)if(n(u)!==null)x=!0,Y(j);else{var M=n(c);M!==null&&Ne(S,M.startTime-T)}}function j(T,M){x=!1,v&&(v=!1,y(k),k=-1),m=!0;var O=p;try{for(g(M),d=n(u);d!==null&&(!(d.expirationTime>M)||T&&!re());){var b=d.callback;if(typeof b=="function"){d.callback=null,p=d.priorityLevel;var U=b(d.expirationTime<=M);M=e.unstable_now(),typeof U=="function"?d.callback=U:d===n(u)&&r(u),g(M)}else r(u);d=n(u)}if(d!==null)var Gt=!0;else{var qe=n(c);qe!==null&&Ne(S,qe.startTime-M),Gt=!1}return Gt}finally{d=null,p=O,m=!1}}var E=!1,P=null,k=-1,V=5,D=-1;function re(){return!(e.unstable_now()-D<V)}function le(){if(P!==null){var T=e.unstable_now();D=T;var M=!0;try{M=P(!0,T)}finally{M?ge():(E=!1,P=null)}}else E=!1}var ge;if(typeof h=="function")ge=function(){h(le)};else if(typeof MessageChannel<"u"){var ie=new MessageChannel,St=ie.port2;ie.port1.onmessage=le,ge=function(){St.postMessage(null)}}else ge=function(){w(le,0)};function Y(T){P=T,E||(E=!0,ge())}function Ne(T,M){k=w(function(){T(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){x||m||(x=!0,Y(j))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(T){switch(p){case 1:case 2:case 3:var M=3;break;default:M=p}var O=p;p=M;try{return T()}finally{p=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,M){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var O=p;p=T;try{return M()}finally{p=O}},e.unstable_scheduleCallback=function(T,M,O){var b=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?b+O:b):O=b,T){case 1:var U=-1;break;case 2:U=250;break;case 5:U=**********;break;case 4:U=1e4;break;default:U=5e3}return U=O+U,T={id:f++,callback:M,priorityLevel:T,startTime:O,expirationTime:U,sortIndex:-1},O>b?(T.sortIndex=O,t(c,T),n(u)===null&&T===n(c)&&(v?(y(k),k=-1):v=!0,Ne(S,O-b))):(T.sortIndex=U,t(u,T),x||m||(x=!0,Y(j))),T},e.unstable_shouldYield=re,e.unstable_wrapCallback=function(T){var M=p;return function(){var O=p;p=M;try{return T.apply(this,arguments)}finally{p=O}}}})(Sd);vd.exports=Sd;var cm=vd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dm=L,De=cm;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var wd=new Set,Rr={};function hn(e,t){Wn(e,t),Wn(e+"Capture",t)}function Wn(e,t){for(Rr[e]=t,e=0;e<t.length;e++)wd.add(t[e])}var ht=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Rs=Object.prototype.hasOwnProperty,fm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ja={},qa={};function pm(e){return Rs.call(qa,e)?!0:Rs.call(Ja,e)?!1:fm.test(e)?qa[e]=!0:(Ja[e]=!0,!1)}function hm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function mm(e,t,n,r){if(t===null||typeof t>"u"||hm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function we(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new we(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new we(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new we(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new we(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new we(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new we(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new we(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new we(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new we(e,5,!1,e.toLowerCase(),null,!1,!1)});var Bl=/[\-:]([a-z])/g;function Il(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Bl,Il);ce[t]=new we(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Bl,Il);ce[t]=new we(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Bl,Il);ce[t]=new we(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new we("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new we(e,1,!1,e.toLowerCase(),null,!0,!0)});function Fl(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(mm(t,n,i,r)&&(n=null),r||i===null?pm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var vt=dm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,si=Symbol.for("react.element"),Sn=Symbol.for("react.portal"),wn=Symbol.for("react.fragment"),_l=Symbol.for("react.strict_mode"),Ls=Symbol.for("react.profiler"),jd=Symbol.for("react.provider"),Cd=Symbol.for("react.context"),Wl=Symbol.for("react.forward_ref"),As=Symbol.for("react.suspense"),Ms=Symbol.for("react.suspense_list"),Ul=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),kd=Symbol.for("react.offscreen"),eu=Symbol.iterator;function er(e){return e===null||typeof e!="object"?null:(e=eu&&e[eu]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,Ho;function cr(e){if(Ho===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ho=t&&t[1]||""}return`
`+Ho+e}var $o=!1;function Xo(e,t){if(!e||$o)return"";$o=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,a=o.length-1;1<=s&&0<=a&&i[s]!==o[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==o[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==o[a]){var u=`
`+i[s].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=s&&0<=a);break}}}finally{$o=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?cr(e):""}function gm(e){switch(e.tag){case 5:return cr(e.type);case 16:return cr("Lazy");case 13:return cr("Suspense");case 19:return cr("SuspenseList");case 0:case 2:case 15:return e=Xo(e.type,!1),e;case 11:return e=Xo(e.type.render,!1),e;case 1:return e=Xo(e.type,!0),e;default:return""}}function Ds(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case wn:return"Fragment";case Sn:return"Portal";case Ls:return"Profiler";case _l:return"StrictMode";case As:return"Suspense";case Ms:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Cd:return(e.displayName||"Context")+".Consumer";case jd:return(e._context.displayName||"Context")+".Provider";case Wl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ul:return t=e.displayName||null,t!==null?t:Ds(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return Ds(e(t))}catch{}}return null}function ym(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ds(t);case 8:return t===_l?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function It(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function xm(e){var t=Pd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function li(e){e._valueTracker||(e._valueTracker=xm(e))}function Ed(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Pd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Fi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function bs(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=It(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Td(e,t){t=t.checked,t!=null&&Fl(e,"checked",t,!1)}function Vs(e,t){Td(e,t);var n=It(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Os(e,t.type,n):t.hasOwnProperty("defaultValue")&&Os(e,t.type,It(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Os(e,t,n){(t!=="number"||Fi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var dr=Array.isArray;function Nn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+It(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ns(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ru(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(dr(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:It(n)}}function Rd(e,t){var n=It(t.value),r=It(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function iu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ld(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function zs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ld(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ai,Ad=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ai=ai||document.createElement("div"),ai.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ai.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Lr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var mr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vm=["Webkit","ms","Moz","O"];Object.keys(mr).forEach(function(e){vm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),mr[t]=mr[e]})});function Md(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||mr.hasOwnProperty(e)&&mr[e]?(""+t).trim():t+"px"}function Dd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Md(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var Sm=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Bs(e,t){if(t){if(Sm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function Is(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Fs=null;function Hl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var _s=null,zn=null,Bn=null;function ou(e){if(e=Jr(e)){if(typeof _s!="function")throw Error(C(280));var t=e.stateNode;t&&(t=ko(t),_s(e.stateNode,e.type,t))}}function bd(e){zn?Bn?Bn.push(e):Bn=[e]:zn=e}function Vd(){if(zn){var e=zn,t=Bn;if(Bn=zn=null,ou(e),t)for(e=0;e<t.length;e++)ou(t[e])}}function Od(e,t){return e(t)}function Nd(){}var Go=!1;function zd(e,t,n){if(Go)return e(t,n);Go=!0;try{return Od(e,t,n)}finally{Go=!1,(zn!==null||Bn!==null)&&(Nd(),Vd())}}function Ar(e,t){var n=e.stateNode;if(n===null)return null;var r=ko(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var Ws=!1;if(ht)try{var tr={};Object.defineProperty(tr,"passive",{get:function(){Ws=!0}}),window.addEventListener("test",tr,tr),window.removeEventListener("test",tr,tr)}catch{Ws=!1}function wm(e,t,n,r,i,o,s,a,u){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var gr=!1,_i=null,Wi=!1,Us=null,jm={onError:function(e){gr=!0,_i=e}};function Cm(e,t,n,r,i,o,s,a,u){gr=!1,_i=null,wm.apply(jm,arguments)}function km(e,t,n,r,i,o,s,a,u){if(Cm.apply(this,arguments),gr){if(gr){var c=_i;gr=!1,_i=null}else throw Error(C(198));Wi||(Wi=!0,Us=c)}}function mn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Bd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function su(e){if(mn(e)!==e)throw Error(C(188))}function Pm(e){var t=e.alternate;if(!t){if(t=mn(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return su(i),e;if(o===r)return su(i),t;o=o.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s){for(a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function Id(e){return e=Pm(e),e!==null?Fd(e):null}function Fd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Fd(e);if(t!==null)return t;e=e.sibling}return null}var _d=De.unstable_scheduleCallback,lu=De.unstable_cancelCallback,Em=De.unstable_shouldYield,Tm=De.unstable_requestPaint,Z=De.unstable_now,Rm=De.unstable_getCurrentPriorityLevel,$l=De.unstable_ImmediatePriority,Wd=De.unstable_UserBlockingPriority,Ui=De.unstable_NormalPriority,Lm=De.unstable_LowPriority,Ud=De.unstable_IdlePriority,So=null,rt=null;function Am(e){if(rt&&typeof rt.onCommitFiberRoot=="function")try{rt.onCommitFiberRoot(So,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:bm,Mm=Math.log,Dm=Math.LN2;function bm(e){return e>>>=0,e===0?32:31-(Mm(e)/Dm|0)|0}var ui=64,ci=4194304;function fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Hi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=fr(a):(o&=s,o!==0&&(r=fr(o)))}else s=n&~i,s!==0?r=fr(s):o!==0&&(r=fr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),i=1<<n,r|=e[n],t&=~i;return r}function Vm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Om(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ye(o),a=1<<s,u=i[s];u===-1?(!(a&n)||a&r)&&(i[s]=Vm(a,t)):u<=t&&(e.expiredLanes|=a),o&=~a}}function Hs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hd(){var e=ui;return ui<<=1,!(ui&4194240)&&(ui=64),e}function Ko(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Yr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function Nm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ye(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Xl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var B=0;function $d(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Xd,Gl,Gd,Kd,Qd,$s=!1,di=[],At=null,Mt=null,Dt=null,Mr=new Map,Dr=new Map,Et=[],zm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function au(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":Dt=null;break;case"pointerover":case"pointerout":Mr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Dr.delete(t.pointerId)}}function nr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Jr(t),t!==null&&Gl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Bm(e,t,n,r,i){switch(t){case"focusin":return At=nr(At,e,t,n,r,i),!0;case"dragenter":return Mt=nr(Mt,e,t,n,r,i),!0;case"mouseover":return Dt=nr(Dt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Mr.set(o,nr(Mr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Dr.set(o,nr(Dr.get(o)||null,e,t,n,r,i)),!0}return!1}function Yd(e){var t=tn(e.target);if(t!==null){var n=mn(t);if(n!==null){if(t=n.tag,t===13){if(t=Bd(n),t!==null){e.blockedOn=t,Qd(e.priority,function(){Gd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ri(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Xs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Fs=r,n.target.dispatchEvent(r),Fs=null}else return t=Jr(n),t!==null&&Gl(t),e.blockedOn=n,!1;t.shift()}return!0}function uu(e,t,n){Ri(e)&&n.delete(t)}function Im(){$s=!1,At!==null&&Ri(At)&&(At=null),Mt!==null&&Ri(Mt)&&(Mt=null),Dt!==null&&Ri(Dt)&&(Dt=null),Mr.forEach(uu),Dr.forEach(uu)}function rr(e,t){e.blockedOn===t&&(e.blockedOn=null,$s||($s=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,Im)))}function br(e){function t(i){return rr(i,e)}if(0<di.length){rr(di[0],e);for(var n=1;n<di.length;n++){var r=di[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&rr(At,e),Mt!==null&&rr(Mt,e),Dt!==null&&rr(Dt,e),Mr.forEach(t),Dr.forEach(t),n=0;n<Et.length;n++)r=Et[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Et.length&&(n=Et[0],n.blockedOn===null);)Yd(n),n.blockedOn===null&&Et.shift()}var In=vt.ReactCurrentBatchConfig,$i=!0;function Fm(e,t,n,r){var i=B,o=In.transition;In.transition=null;try{B=1,Kl(e,t,n,r)}finally{B=i,In.transition=o}}function _m(e,t,n,r){var i=B,o=In.transition;In.transition=null;try{B=4,Kl(e,t,n,r)}finally{B=i,In.transition=o}}function Kl(e,t,n,r){if($i){var i=Xs(e,t,n,r);if(i===null)is(e,t,r,Xi,n),au(e,r);else if(Bm(i,e,t,n,r))r.stopPropagation();else if(au(e,r),t&4&&-1<zm.indexOf(e)){for(;i!==null;){var o=Jr(i);if(o!==null&&Xd(o),o=Xs(e,t,n,r),o===null&&is(e,t,r,Xi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else is(e,t,r,null,n)}}var Xi=null;function Xs(e,t,n,r){if(Xi=null,e=Hl(r),e=tn(e),e!==null)if(t=mn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Bd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Xi=e,null}function Zd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Rm()){case $l:return 1;case Wd:return 4;case Ui:case Lm:return 16;case Ud:return 536870912;default:return 16}default:return 16}}var Rt=null,Ql=null,Li=null;function Jd(){if(Li)return Li;var e,t=Ql,n=t.length,r,i="value"in Rt?Rt.value:Rt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Li=i.slice(e,1<r?1-r:void 0)}function Ai(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function fi(){return!0}function cu(){return!1}function Oe(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(o):o[a]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?fi:cu,this.isPropagationStopped=cu,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=fi)},persist:function(){},isPersistent:fi}),t}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yl=Oe(Jn),Zr=K({},Jn,{view:0,detail:0}),Wm=Oe(Zr),Qo,Yo,ir,wo=K({},Zr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Zl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ir&&(ir&&e.type==="mousemove"?(Qo=e.screenX-ir.screenX,Yo=e.screenY-ir.screenY):Yo=Qo=0,ir=e),Qo)},movementY:function(e){return"movementY"in e?e.movementY:Yo}}),du=Oe(wo),Um=K({},wo,{dataTransfer:0}),Hm=Oe(Um),$m=K({},Zr,{relatedTarget:0}),Zo=Oe($m),Xm=K({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),Gm=Oe(Xm),Km=K({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qm=Oe(Km),Ym=K({},Jn,{data:0}),fu=Oe(Ym),Zm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function eg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qm[e])?!!t[e]:!1}function Zl(){return eg}var tg=K({},Zr,{key:function(e){if(e.key){var t=Zm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ai(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Zl,charCode:function(e){return e.type==="keypress"?Ai(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ai(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ng=Oe(tg),rg=K({},wo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=Oe(rg),ig=K({},Zr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Zl}),og=Oe(ig),sg=K({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),lg=Oe(sg),ag=K({},wo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ug=Oe(ag),cg=[9,13,27,32],Jl=ht&&"CompositionEvent"in window,yr=null;ht&&"documentMode"in document&&(yr=document.documentMode);var dg=ht&&"TextEvent"in window&&!yr,qd=ht&&(!Jl||yr&&8<yr&&11>=yr),hu=" ",mu=!1;function ef(e,t){switch(e){case"keyup":return cg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var jn=!1;function fg(e,t){switch(e){case"compositionend":return tf(t);case"keypress":return t.which!==32?null:(mu=!0,hu);case"textInput":return e=t.data,e===hu&&mu?null:e;default:return null}}function pg(e,t){if(jn)return e==="compositionend"||!Jl&&ef(e,t)?(e=Jd(),Li=Ql=Rt=null,jn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qd&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function nf(e,t,n,r){bd(r),t=Gi(t,"onChange"),0<t.length&&(n=new Yl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var xr=null,Vr=null;function mg(e){hf(e,0)}function jo(e){var t=Pn(e);if(Ed(t))return e}function gg(e,t){if(e==="change")return t}var rf=!1;if(ht){var Jo;if(ht){var qo="oninput"in document;if(!qo){var yu=document.createElement("div");yu.setAttribute("oninput","return;"),qo=typeof yu.oninput=="function"}Jo=qo}else Jo=!1;rf=Jo&&(!document.documentMode||9<document.documentMode)}function xu(){xr&&(xr.detachEvent("onpropertychange",of),Vr=xr=null)}function of(e){if(e.propertyName==="value"&&jo(Vr)){var t=[];nf(t,Vr,e,Hl(e)),zd(mg,t)}}function yg(e,t,n){e==="focusin"?(xu(),xr=t,Vr=n,xr.attachEvent("onpropertychange",of)):e==="focusout"&&xu()}function xg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return jo(Vr)}function vg(e,t){if(e==="click")return jo(t)}function Sg(e,t){if(e==="input"||e==="change")return jo(t)}function wg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Je=typeof Object.is=="function"?Object.is:wg;function Or(e,t){if(Je(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Rs.call(t,i)||!Je(e[i],t[i]))return!1}return!0}function vu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Su(e,t){var n=vu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=vu(n)}}function sf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function lf(){for(var e=window,t=Fi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Fi(e.document)}return t}function ql(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function jg(e){var t=lf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sf(n.ownerDocument.documentElement,n)){if(r!==null&&ql(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=Su(n,o);var s=Su(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Cg=ht&&"documentMode"in document&&11>=document.documentMode,Cn=null,Gs=null,vr=null,Ks=!1;function wu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ks||Cn==null||Cn!==Fi(r)||(r=Cn,"selectionStart"in r&&ql(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vr&&Or(vr,r)||(vr=r,r=Gi(Gs,"onSelect"),0<r.length&&(t=new Yl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cn)))}function pi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kn={animationend:pi("Animation","AnimationEnd"),animationiteration:pi("Animation","AnimationIteration"),animationstart:pi("Animation","AnimationStart"),transitionend:pi("Transition","TransitionEnd")},es={},af={};ht&&(af=document.createElement("div").style,"AnimationEvent"in window||(delete kn.animationend.animation,delete kn.animationiteration.animation,delete kn.animationstart.animation),"TransitionEvent"in window||delete kn.transitionend.transition);function Co(e){if(es[e])return es[e];if(!kn[e])return e;var t=kn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in af)return es[e]=t[n];return e}var uf=Co("animationend"),cf=Co("animationiteration"),df=Co("animationstart"),ff=Co("transitionend"),pf=new Map,ju="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ut(e,t){pf.set(e,t),hn(t,[e])}for(var ts=0;ts<ju.length;ts++){var ns=ju[ts],kg=ns.toLowerCase(),Pg=ns[0].toUpperCase()+ns.slice(1);Ut(kg,"on"+Pg)}Ut(uf,"onAnimationEnd");Ut(cf,"onAnimationIteration");Ut(df,"onAnimationStart");Ut("dblclick","onDoubleClick");Ut("focusin","onFocus");Ut("focusout","onBlur");Ut(ff,"onTransitionEnd");Wn("onMouseEnter",["mouseout","mouseover"]);Wn("onMouseLeave",["mouseout","mouseover"]);Wn("onPointerEnter",["pointerout","pointerover"]);Wn("onPointerLeave",["pointerout","pointerover"]);hn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));hn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));hn("onBeforeInput",["compositionend","keypress","textInput","paste"]);hn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Eg=new Set("cancel close invalid load scroll toggle".split(" ").concat(pr));function Cu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,km(r,t,void 0,e),e.currentTarget=null}function hf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],u=a.instance,c=a.currentTarget;if(a=a.listener,u!==o&&i.isPropagationStopped())break e;Cu(i,a,c),o=u}else for(s=0;s<r.length;s++){if(a=r[s],u=a.instance,c=a.currentTarget,a=a.listener,u!==o&&i.isPropagationStopped())break e;Cu(i,a,c),o=u}}}if(Wi)throw e=Us,Wi=!1,Us=null,e}function F(e,t){var n=t[qs];n===void 0&&(n=t[qs]=new Set);var r=e+"__bubble";n.has(r)||(mf(t,e,2,!1),n.add(r))}function rs(e,t,n){var r=0;t&&(r|=4),mf(n,e,r,t)}var hi="_reactListening"+Math.random().toString(36).slice(2);function Nr(e){if(!e[hi]){e[hi]=!0,wd.forEach(function(n){n!=="selectionchange"&&(Eg.has(n)||rs(n,!1,e),rs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[hi]||(t[hi]=!0,rs("selectionchange",!1,t))}}function mf(e,t,n,r){switch(Zd(t)){case 1:var i=Fm;break;case 4:i=_m;break;default:i=Kl}n=i.bind(null,t,n,e),i=void 0,!Ws||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function is(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var u=s.tag;if((u===3||u===4)&&(u=s.stateNode.containerInfo,u===i||u.nodeType===8&&u.parentNode===i))return;s=s.return}for(;a!==null;){if(s=tn(a),s===null)return;if(u=s.tag,u===5||u===6){r=o=s;continue e}a=a.parentNode}}r=r.return}zd(function(){var c=o,f=Hl(n),d=[];e:{var p=pf.get(e);if(p!==void 0){var m=Yl,x=e;switch(e){case"keypress":if(Ai(n)===0)break e;case"keydown":case"keyup":m=ng;break;case"focusin":x="focus",m=Zo;break;case"focusout":x="blur",m=Zo;break;case"beforeblur":case"afterblur":m=Zo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=du;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Hm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=og;break;case uf:case cf:case df:m=Gm;break;case ff:m=lg;break;case"scroll":m=Wm;break;case"wheel":m=ug;break;case"copy":case"cut":case"paste":m=Qm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=pu}var v=(t&4)!==0,w=!v&&e==="scroll",y=v?p!==null?p+"Capture":null:p;v=[];for(var h=c,g;h!==null;){g=h;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,y!==null&&(S=Ar(h,y),S!=null&&v.push(zr(h,S,g)))),w)break;h=h.return}0<v.length&&(p=new m(p,x,null,n,f),d.push({event:p,listeners:v}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",p&&n!==Fs&&(x=n.relatedTarget||n.fromElement)&&(tn(x)||x[mt]))break e;if((m||p)&&(p=f.window===f?f:(p=f.ownerDocument)?p.defaultView||p.parentWindow:window,m?(x=n.relatedTarget||n.toElement,m=c,x=x?tn(x):null,x!==null&&(w=mn(x),x!==w||x.tag!==5&&x.tag!==6)&&(x=null)):(m=null,x=c),m!==x)){if(v=du,S="onMouseLeave",y="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(v=pu,S="onPointerLeave",y="onPointerEnter",h="pointer"),w=m==null?p:Pn(m),g=x==null?p:Pn(x),p=new v(S,h+"leave",m,n,f),p.target=w,p.relatedTarget=g,S=null,tn(f)===c&&(v=new v(y,h+"enter",x,n,f),v.target=g,v.relatedTarget=w,S=v),w=S,m&&x)t:{for(v=m,y=x,h=0,g=v;g;g=vn(g))h++;for(g=0,S=y;S;S=vn(S))g++;for(;0<h-g;)v=vn(v),h--;for(;0<g-h;)y=vn(y),g--;for(;h--;){if(v===y||y!==null&&v===y.alternate)break t;v=vn(v),y=vn(y)}v=null}else v=null;m!==null&&ku(d,p,m,v,!1),x!==null&&w!==null&&ku(d,w,x,v,!0)}}e:{if(p=c?Pn(c):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var j=gg;else if(gu(p))if(rf)j=Sg;else{j=xg;var E=yg}else(m=p.nodeName)&&m.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(j=vg);if(j&&(j=j(e,c))){nf(d,j,n,f);break e}E&&E(e,p,c),e==="focusout"&&(E=p._wrapperState)&&E.controlled&&p.type==="number"&&Os(p,"number",p.value)}switch(E=c?Pn(c):window,e){case"focusin":(gu(E)||E.contentEditable==="true")&&(Cn=E,Gs=c,vr=null);break;case"focusout":vr=Gs=Cn=null;break;case"mousedown":Ks=!0;break;case"contextmenu":case"mouseup":case"dragend":Ks=!1,wu(d,n,f);break;case"selectionchange":if(Cg)break;case"keydown":case"keyup":wu(d,n,f)}var P;if(Jl)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else jn?ef(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(qd&&n.locale!=="ko"&&(jn||k!=="onCompositionStart"?k==="onCompositionEnd"&&jn&&(P=Jd()):(Rt=f,Ql="value"in Rt?Rt.value:Rt.textContent,jn=!0)),E=Gi(c,k),0<E.length&&(k=new fu(k,e,null,n,f),d.push({event:k,listeners:E}),P?k.data=P:(P=tf(n),P!==null&&(k.data=P)))),(P=dg?fg(e,n):pg(e,n))&&(c=Gi(c,"onBeforeInput"),0<c.length&&(f=new fu("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:c}),f.data=P))}hf(d,t)})}function zr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Ar(e,n),o!=null&&r.unshift(zr(e,o,i)),o=Ar(e,t),o!=null&&r.push(zr(e,o,i))),e=e.return}return r}function vn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ku(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var a=n,u=a.alternate,c=a.stateNode;if(u!==null&&u===r)break;a.tag===5&&c!==null&&(a=c,i?(u=Ar(n,o),u!=null&&s.unshift(zr(n,u,a))):i||(u=Ar(n,o),u!=null&&s.push(zr(n,u,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Tg=/\r\n?/g,Rg=/\u0000|\uFFFD/g;function Pu(e){return(typeof e=="string"?e:""+e).replace(Tg,`
`).replace(Rg,"")}function mi(e,t,n){if(t=Pu(t),Pu(e)!==t&&n)throw Error(C(425))}function Ki(){}var Qs=null,Ys=null;function Zs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Js=typeof setTimeout=="function"?setTimeout:void 0,Lg=typeof clearTimeout=="function"?clearTimeout:void 0,Eu=typeof Promise=="function"?Promise:void 0,Ag=typeof queueMicrotask=="function"?queueMicrotask:typeof Eu<"u"?function(e){return Eu.resolve(null).then(e).catch(Mg)}:Js;function Mg(e){setTimeout(function(){throw e})}function os(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),br(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);br(t)}function bt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Tu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var qn=Math.random().toString(36).slice(2),nt="__reactFiber$"+qn,Br="__reactProps$"+qn,mt="__reactContainer$"+qn,qs="__reactEvents$"+qn,Dg="__reactListeners$"+qn,bg="__reactHandles$"+qn;function tn(e){var t=e[nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mt]||n[nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Tu(e);e!==null;){if(n=e[nt])return n;e=Tu(e)}return t}e=n,n=e.parentNode}return null}function Jr(e){return e=e[nt]||e[mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Pn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function ko(e){return e[Br]||null}var el=[],En=-1;function Ht(e){return{current:e}}function _(e){0>En||(e.current=el[En],el[En]=null,En--)}function I(e,t){En++,el[En]=e.current,e.current=t}var Ft={},me=Ht(Ft),ke=Ht(!1),un=Ft;function Un(e,t){var n=e.type.contextTypes;if(!n)return Ft;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Pe(e){return e=e.childContextTypes,e!=null}function Qi(){_(ke),_(me)}function Ru(e,t,n){if(me.current!==Ft)throw Error(C(168));I(me,t),I(ke,n)}function gf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(C(108,ym(e)||"Unknown",i));return K({},n,r)}function Yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ft,un=me.current,I(me,e),I(ke,ke.current),!0}function Lu(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=gf(e,t,un),r.__reactInternalMemoizedMergedChildContext=e,_(ke),_(me),I(me,e)):_(ke),I(ke,n)}var lt=null,Po=!1,ss=!1;function yf(e){lt===null?lt=[e]:lt.push(e)}function Vg(e){Po=!0,yf(e)}function $t(){if(!ss&&lt!==null){ss=!0;var e=0,t=B;try{var n=lt;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Po=!1}catch(i){throw lt!==null&&(lt=lt.slice(e+1)),_d($l,$t),i}finally{B=t,ss=!1}}return null}var Tn=[],Rn=0,Zi=null,Ji=0,Ie=[],Fe=0,cn=null,at=1,ut="";function Zt(e,t){Tn[Rn++]=Ji,Tn[Rn++]=Zi,Zi=e,Ji=t}function xf(e,t,n){Ie[Fe++]=at,Ie[Fe++]=ut,Ie[Fe++]=cn,cn=e;var r=at;e=ut;var i=32-Ye(r)-1;r&=~(1<<i),n+=1;var o=32-Ye(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,at=1<<32-Ye(t)+i|n<<i|r,ut=o+e}else at=1<<o|n<<i|r,ut=e}function ea(e){e.return!==null&&(Zt(e,1),xf(e,1,0))}function ta(e){for(;e===Zi;)Zi=Tn[--Rn],Tn[Rn]=null,Ji=Tn[--Rn],Tn[Rn]=null;for(;e===cn;)cn=Ie[--Fe],Ie[Fe]=null,ut=Ie[--Fe],Ie[Fe]=null,at=Ie[--Fe],Ie[Fe]=null}var Me=null,Ae=null,H=!1,Qe=null;function vf(e,t){var n=_e(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Au(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Me=e,Ae=bt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Me=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=cn!==null?{id:at,overflow:ut}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=_e(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Me=e,Ae=null,!0):!1;default:return!1}}function tl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nl(e){if(H){var t=Ae;if(t){var n=t;if(!Au(e,t)){if(tl(e))throw Error(C(418));t=bt(n.nextSibling);var r=Me;t&&Au(e,t)?vf(r,n):(e.flags=e.flags&-4097|2,H=!1,Me=e)}}else{if(tl(e))throw Error(C(418));e.flags=e.flags&-4097|2,H=!1,Me=e}}}function Mu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Me=e}function gi(e){if(e!==Me)return!1;if(!H)return Mu(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Zs(e.type,e.memoizedProps)),t&&(t=Ae)){if(tl(e))throw Sf(),Error(C(418));for(;t;)vf(e,t),t=bt(t.nextSibling)}if(Mu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=bt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=Me?bt(e.stateNode.nextSibling):null;return!0}function Sf(){for(var e=Ae;e;)e=bt(e.nextSibling)}function Hn(){Ae=Me=null,H=!1}function na(e){Qe===null?Qe=[e]:Qe.push(e)}var Og=vt.ReactCurrentBatchConfig;function or(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var a=i.refs;s===null?delete a[o]:a[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function yi(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Du(e){var t=e._init;return t(e._payload)}function wf(e){function t(y,h){if(e){var g=y.deletions;g===null?(y.deletions=[h],y.flags|=16):g.push(h)}}function n(y,h){if(!e)return null;for(;h!==null;)t(y,h),h=h.sibling;return null}function r(y,h){for(y=new Map;h!==null;)h.key!==null?y.set(h.key,h):y.set(h.index,h),h=h.sibling;return y}function i(y,h){return y=zt(y,h),y.index=0,y.sibling=null,y}function o(y,h,g){return y.index=g,e?(g=y.alternate,g!==null?(g=g.index,g<h?(y.flags|=2,h):g):(y.flags|=2,h)):(y.flags|=1048576,h)}function s(y){return e&&y.alternate===null&&(y.flags|=2),y}function a(y,h,g,S){return h===null||h.tag!==6?(h=ps(g,y.mode,S),h.return=y,h):(h=i(h,g),h.return=y,h)}function u(y,h,g,S){var j=g.type;return j===wn?f(y,h,g.props.children,S,g.key):h!==null&&(h.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===Ct&&Du(j)===h.type)?(S=i(h,g.props),S.ref=or(y,h,g),S.return=y,S):(S=zi(g.type,g.key,g.props,null,y.mode,S),S.ref=or(y,h,g),S.return=y,S)}function c(y,h,g,S){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=hs(g,y.mode,S),h.return=y,h):(h=i(h,g.children||[]),h.return=y,h)}function f(y,h,g,S,j){return h===null||h.tag!==7?(h=ln(g,y.mode,S,j),h.return=y,h):(h=i(h,g),h.return=y,h)}function d(y,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ps(""+h,y.mode,g),h.return=y,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case si:return g=zi(h.type,h.key,h.props,null,y.mode,g),g.ref=or(y,null,h),g.return=y,g;case Sn:return h=hs(h,y.mode,g),h.return=y,h;case Ct:var S=h._init;return d(y,S(h._payload),g)}if(dr(h)||er(h))return h=ln(h,y.mode,g,null),h.return=y,h;yi(y,h)}return null}function p(y,h,g,S){var j=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return j!==null?null:a(y,h,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case si:return g.key===j?u(y,h,g,S):null;case Sn:return g.key===j?c(y,h,g,S):null;case Ct:return j=g._init,p(y,h,j(g._payload),S)}if(dr(g)||er(g))return j!==null?null:f(y,h,g,S,null);yi(y,g)}return null}function m(y,h,g,S,j){if(typeof S=="string"&&S!==""||typeof S=="number")return y=y.get(g)||null,a(h,y,""+S,j);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case si:return y=y.get(S.key===null?g:S.key)||null,u(h,y,S,j);case Sn:return y=y.get(S.key===null?g:S.key)||null,c(h,y,S,j);case Ct:var E=S._init;return m(y,h,g,E(S._payload),j)}if(dr(S)||er(S))return y=y.get(g)||null,f(h,y,S,j,null);yi(h,S)}return null}function x(y,h,g,S){for(var j=null,E=null,P=h,k=h=0,V=null;P!==null&&k<g.length;k++){P.index>k?(V=P,P=null):V=P.sibling;var D=p(y,P,g[k],S);if(D===null){P===null&&(P=V);break}e&&P&&D.alternate===null&&t(y,P),h=o(D,h,k),E===null?j=D:E.sibling=D,E=D,P=V}if(k===g.length)return n(y,P),H&&Zt(y,k),j;if(P===null){for(;k<g.length;k++)P=d(y,g[k],S),P!==null&&(h=o(P,h,k),E===null?j=P:E.sibling=P,E=P);return H&&Zt(y,k),j}for(P=r(y,P);k<g.length;k++)V=m(P,y,k,g[k],S),V!==null&&(e&&V.alternate!==null&&P.delete(V.key===null?k:V.key),h=o(V,h,k),E===null?j=V:E.sibling=V,E=V);return e&&P.forEach(function(re){return t(y,re)}),H&&Zt(y,k),j}function v(y,h,g,S){var j=er(g);if(typeof j!="function")throw Error(C(150));if(g=j.call(g),g==null)throw Error(C(151));for(var E=j=null,P=h,k=h=0,V=null,D=g.next();P!==null&&!D.done;k++,D=g.next()){P.index>k?(V=P,P=null):V=P.sibling;var re=p(y,P,D.value,S);if(re===null){P===null&&(P=V);break}e&&P&&re.alternate===null&&t(y,P),h=o(re,h,k),E===null?j=re:E.sibling=re,E=re,P=V}if(D.done)return n(y,P),H&&Zt(y,k),j;if(P===null){for(;!D.done;k++,D=g.next())D=d(y,D.value,S),D!==null&&(h=o(D,h,k),E===null?j=D:E.sibling=D,E=D);return H&&Zt(y,k),j}for(P=r(y,P);!D.done;k++,D=g.next())D=m(P,y,k,D.value,S),D!==null&&(e&&D.alternate!==null&&P.delete(D.key===null?k:D.key),h=o(D,h,k),E===null?j=D:E.sibling=D,E=D);return e&&P.forEach(function(le){return t(y,le)}),H&&Zt(y,k),j}function w(y,h,g,S){if(typeof g=="object"&&g!==null&&g.type===wn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case si:e:{for(var j=g.key,E=h;E!==null;){if(E.key===j){if(j=g.type,j===wn){if(E.tag===7){n(y,E.sibling),h=i(E,g.props.children),h.return=y,y=h;break e}}else if(E.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===Ct&&Du(j)===E.type){n(y,E.sibling),h=i(E,g.props),h.ref=or(y,E,g),h.return=y,y=h;break e}n(y,E);break}else t(y,E);E=E.sibling}g.type===wn?(h=ln(g.props.children,y.mode,S,g.key),h.return=y,y=h):(S=zi(g.type,g.key,g.props,null,y.mode,S),S.ref=or(y,h,g),S.return=y,y=S)}return s(y);case Sn:e:{for(E=g.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(y,h.sibling),h=i(h,g.children||[]),h.return=y,y=h;break e}else{n(y,h);break}else t(y,h);h=h.sibling}h=hs(g,y.mode,S),h.return=y,y=h}return s(y);case Ct:return E=g._init,w(y,h,E(g._payload),S)}if(dr(g))return x(y,h,g,S);if(er(g))return v(y,h,g,S);yi(y,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(y,h.sibling),h=i(h,g),h.return=y,y=h):(n(y,h),h=ps(g,y.mode,S),h.return=y,y=h),s(y)):n(y,h)}return w}var $n=wf(!0),jf=wf(!1),qi=Ht(null),eo=null,Ln=null,ra=null;function ia(){ra=Ln=eo=null}function oa(e){var t=qi.current;_(qi),e._currentValue=t}function rl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Fn(e,t){eo=e,ra=Ln=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(ra!==e)if(e={context:e,memoizedValue:t,next:null},Ln===null){if(eo===null)throw Error(C(308));Ln=e,eo.dependencies={lanes:0,firstContext:e}}else Ln=Ln.next=e;return t}var nn=null;function sa(e){nn===null?nn=[e]:nn.push(e)}function Cf(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,sa(t)):(n.next=i.next,i.next=n),t.interleaved=n,gt(e,r)}function gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kt=!1;function la(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function kf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,gt(e,n)}return i=r.interleaved,i===null?(t.next=t,sa(r)):(t.next=i.next,i.next=t),r.interleaved=t,gt(e,n)}function Mi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Xl(e,n)}}function bu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function to(e,t,n,r){var i=e.updateQueue;kt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var u=a,c=u.next;u.next=null,s===null?o=c:s.next=c,s=u;var f=e.alternate;f!==null&&(f=f.updateQueue,a=f.lastBaseUpdate,a!==s&&(a===null?f.firstBaseUpdate=c:a.next=c,f.lastBaseUpdate=u))}if(o!==null){var d=i.baseState;s=0,f=c=u=null,a=o;do{var p=a.lane,m=a.eventTime;if((r&p)===p){f!==null&&(f=f.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var x=e,v=a;switch(p=t,m=n,v.tag){case 1:if(x=v.payload,typeof x=="function"){d=x.call(m,d,p);break e}d=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=v.payload,p=typeof x=="function"?x.call(m,d,p):x,p==null)break e;d=K({},d,p);break e;case 2:kt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[a]:p.push(a))}else m={eventTime:m,lane:p,tag:a.tag,payload:a.payload,callback:a.callback,next:null},f===null?(c=f=m,u=d):f=f.next=m,s|=p;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;p=a,a=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(f===null&&(u=d),i.baseState=u,i.firstBaseUpdate=c,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);fn|=s,e.lanes=s,e.memoizedState=d}}function Vu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var qr={},it=Ht(qr),Ir=Ht(qr),Fr=Ht(qr);function rn(e){if(e===qr)throw Error(C(174));return e}function aa(e,t){switch(I(Fr,t),I(Ir,e),I(it,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:zs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=zs(t,e)}_(it),I(it,t)}function Xn(){_(it),_(Ir),_(Fr)}function Pf(e){rn(Fr.current);var t=rn(it.current),n=zs(t,e.type);t!==n&&(I(Ir,e),I(it,n))}function ua(e){Ir.current===e&&(_(it),_(Ir))}var $=Ht(0);function no(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ls=[];function ca(){for(var e=0;e<ls.length;e++)ls[e]._workInProgressVersionPrimary=null;ls.length=0}var Di=vt.ReactCurrentDispatcher,as=vt.ReactCurrentBatchConfig,dn=0,G=null,te=null,oe=null,ro=!1,Sr=!1,_r=0,Ng=0;function de(){throw Error(C(321))}function da(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Je(e[n],t[n]))return!1;return!0}function fa(e,t,n,r,i,o){if(dn=o,G=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Di.current=e===null||e.memoizedState===null?Fg:_g,e=n(r,i),Sr){o=0;do{if(Sr=!1,_r=0,25<=o)throw Error(C(301));o+=1,oe=te=null,t.updateQueue=null,Di.current=Wg,e=n(r,i)}while(Sr)}if(Di.current=io,t=te!==null&&te.next!==null,dn=0,oe=te=G=null,ro=!1,t)throw Error(C(300));return e}function pa(){var e=_r!==0;return _r=0,e}function tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?G.memoizedState=oe=e:oe=oe.next=e,oe}function $e(){if(te===null){var e=G.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=oe===null?G.memoizedState:oe.next;if(t!==null)oe=t,te=e;else{if(e===null)throw Error(C(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},oe===null?G.memoizedState=oe=e:oe=oe.next=e}return oe}function Wr(e,t){return typeof t=="function"?t(e):t}function us(e){var t=$e(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=te,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var a=s=null,u=null,c=o;do{var f=c.lane;if((dn&f)===f)u!==null&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var d={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};u===null?(a=u=d,s=r):u=u.next=d,G.lanes|=f,fn|=f}c=c.next}while(c!==null&&c!==o);u===null?s=r:u.next=a,Je(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,G.lanes|=o,fn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function cs(e){var t=$e(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Je(o,t.memoizedState)||(Ce=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ef(){}function Tf(e,t){var n=G,r=$e(),i=t(),o=!Je(r.memoizedState,i);if(o&&(r.memoizedState=i,Ce=!0),r=r.queue,ha(Af.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,Ur(9,Lf.bind(null,n,r,i,t),void 0,null),se===null)throw Error(C(349));dn&30||Rf(n,t,i)}return i}function Rf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Lf(e,t,n,r){t.value=n,t.getSnapshot=r,Mf(t)&&Df(e)}function Af(e,t,n){return n(function(){Mf(t)&&Df(e)})}function Mf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Je(e,n)}catch{return!0}}function Df(e){var t=gt(e,1);t!==null&&Ze(t,e,1,-1)}function Ou(e){var t=tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Wr,lastRenderedState:e},t.queue=e,e=e.dispatch=Ig.bind(null,G,e),[t.memoizedState,e]}function Ur(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function bf(){return $e().memoizedState}function bi(e,t,n,r){var i=tt();G.flags|=e,i.memoizedState=Ur(1|t,n,void 0,r===void 0?null:r)}function Eo(e,t,n,r){var i=$e();r=r===void 0?null:r;var o=void 0;if(te!==null){var s=te.memoizedState;if(o=s.destroy,r!==null&&da(r,s.deps)){i.memoizedState=Ur(t,n,o,r);return}}G.flags|=e,i.memoizedState=Ur(1|t,n,o,r)}function Nu(e,t){return bi(8390656,8,e,t)}function ha(e,t){return Eo(2048,8,e,t)}function Vf(e,t){return Eo(4,2,e,t)}function Of(e,t){return Eo(4,4,e,t)}function Nf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function zf(e,t,n){return n=n!=null?n.concat([e]):null,Eo(4,4,Nf.bind(null,t,e),n)}function ma(){}function Bf(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&da(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function If(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&da(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ff(e,t,n){return dn&21?(Je(n,t)||(n=Hd(),G.lanes|=n,fn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function zg(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=as.transition;as.transition={};try{e(!1),t()}finally{B=n,as.transition=r}}function _f(){return $e().memoizedState}function Bg(e,t,n){var r=Nt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Wf(e))Uf(t,n);else if(n=Cf(e,t,n,r),n!==null){var i=ve();Ze(n,e,r,i),Hf(n,t,r)}}function Ig(e,t,n){var r=Nt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Wf(e))Uf(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,a=o(s,n);if(i.hasEagerState=!0,i.eagerState=a,Je(a,s)){var u=t.interleaved;u===null?(i.next=i,sa(t)):(i.next=u.next,u.next=i),t.interleaved=i;return}}catch{}finally{}n=Cf(e,t,i,r),n!==null&&(i=ve(),Ze(n,e,r,i),Hf(n,t,r))}}function Wf(e){var t=e.alternate;return e===G||t!==null&&t===G}function Uf(e,t){Sr=ro=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Xl(e,n)}}var io={readContext:He,useCallback:de,useContext:de,useEffect:de,useImperativeHandle:de,useInsertionEffect:de,useLayoutEffect:de,useMemo:de,useReducer:de,useRef:de,useState:de,useDebugValue:de,useDeferredValue:de,useTransition:de,useMutableSource:de,useSyncExternalStore:de,useId:de,unstable_isNewReconciler:!1},Fg={readContext:He,useCallback:function(e,t){return tt().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:Nu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,bi(4194308,4,Nf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return bi(4194308,4,e,t)},useInsertionEffect:function(e,t){return bi(4,2,e,t)},useMemo:function(e,t){var n=tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Bg.bind(null,G,e),[r.memoizedState,e]},useRef:function(e){var t=tt();return e={current:e},t.memoizedState=e},useState:Ou,useDebugValue:ma,useDeferredValue:function(e){return tt().memoizedState=e},useTransition:function(){var e=Ou(!1),t=e[0];return e=zg.bind(null,e[1]),tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=G,i=tt();if(H){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),se===null)throw Error(C(349));dn&30||Rf(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Nu(Af.bind(null,r,o,e),[e]),r.flags|=2048,Ur(9,Lf.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=tt(),t=se.identifierPrefix;if(H){var n=ut,r=at;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=_r++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Ng++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},_g={readContext:He,useCallback:Bf,useContext:He,useEffect:ha,useImperativeHandle:zf,useInsertionEffect:Vf,useLayoutEffect:Of,useMemo:If,useReducer:us,useRef:bf,useState:function(){return us(Wr)},useDebugValue:ma,useDeferredValue:function(e){var t=$e();return Ff(t,te.memoizedState,e)},useTransition:function(){var e=us(Wr)[0],t=$e().memoizedState;return[e,t]},useMutableSource:Ef,useSyncExternalStore:Tf,useId:_f,unstable_isNewReconciler:!1},Wg={readContext:He,useCallback:Bf,useContext:He,useEffect:ha,useImperativeHandle:zf,useInsertionEffect:Vf,useLayoutEffect:Of,useMemo:If,useReducer:cs,useRef:bf,useState:function(){return cs(Wr)},useDebugValue:ma,useDeferredValue:function(e){var t=$e();return te===null?t.memoizedState=e:Ff(t,te.memoizedState,e)},useTransition:function(){var e=cs(Wr)[0],t=$e().memoizedState;return[e,t]},useMutableSource:Ef,useSyncExternalStore:Tf,useId:_f,unstable_isNewReconciler:!1};function Ge(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function il(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var To={isMounted:function(e){return(e=e._reactInternals)?mn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=ve(),i=Nt(e),o=dt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Vt(e,o,i),t!==null&&(Ze(t,e,i,r),Mi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=ve(),i=Nt(e),o=dt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Vt(e,o,i),t!==null&&(Ze(t,e,i,r),Mi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=ve(),r=Nt(e),i=dt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Vt(e,i,r),t!==null&&(Ze(t,e,r,n),Mi(t,e,r))}};function zu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Or(n,r)||!Or(i,o):!0}function $f(e,t,n){var r=!1,i=Ft,o=t.contextType;return typeof o=="object"&&o!==null?o=He(o):(i=Pe(t)?un:me.current,r=t.contextTypes,o=(r=r!=null)?Un(e,i):Ft),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=To,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Bu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&To.enqueueReplaceState(t,t.state,null)}function ol(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},la(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=He(o):(o=Pe(t)?un:me.current,i.context=Un(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(il(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&To.enqueueReplaceState(i,i.state,null),to(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Gn(e,t){try{var n="",r=t;do n+=gm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function ds(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function sl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ug=typeof WeakMap=="function"?WeakMap:Map;function Xf(e,t,n){n=dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){so||(so=!0,gl=r),sl(e,t)},n}function Gf(e,t,n){n=dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){sl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){sl(e,t),typeof r!="function"&&(Ot===null?Ot=new Set([this]):Ot.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Iu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ug;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=ry.bind(null,e,t,n),t.then(e,e))}function Fu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function _u(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=dt(-1,1),t.tag=2,Vt(n,t,1))),n.lanes|=1),e)}var Hg=vt.ReactCurrentOwner,Ce=!1;function xe(e,t,n,r){t.child=e===null?jf(t,null,n,r):$n(t,e.child,n,r)}function Wu(e,t,n,r,i){n=n.render;var o=t.ref;return Fn(t,i),r=fa(e,t,n,r,o,i),n=pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):(H&&n&&ea(t),t.flags|=1,xe(e,t,r,i),t.child)}function Uu(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Ca(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Kf(e,t,o,r,i)):(e=zi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Or,n(s,r)&&e.ref===t.ref)return yt(e,t,i)}return t.flags|=1,e=zt(o,r),e.ref=t.ref,e.return=t,t.child=e}function Kf(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Or(o,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,yt(e,t,i)}return ll(e,t,n,r,i)}function Qf(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},I(Mn,Le),Le|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,I(Mn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,I(Mn,Le),Le|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,I(Mn,Le),Le|=r;return xe(e,t,i,n),t.child}function Yf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ll(e,t,n,r,i){var o=Pe(n)?un:me.current;return o=Un(t,o),Fn(t,i),n=fa(e,t,n,r,o,i),r=pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):(H&&r&&ea(t),t.flags|=1,xe(e,t,n,i),t.child)}function Hu(e,t,n,r,i){if(Pe(n)){var o=!0;Yi(t)}else o=!1;if(Fn(t,i),t.stateNode===null)Vi(e,t),$f(t,n,r),ol(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var u=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=He(c):(c=Pe(n)?un:me.current,c=Un(t,c));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||u!==c)&&Bu(t,s,r,c),kt=!1;var p=t.memoizedState;s.state=p,to(t,r,s,i),u=t.memoizedState,a!==r||p!==u||ke.current||kt?(typeof f=="function"&&(il(t,n,f,r),u=t.memoizedState),(a=kt||zu(t,n,a,r,p,u,c))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),s.props=r,s.state=u,s.context=c,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,kf(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:Ge(t.type,a),s.props=c,d=t.pendingProps,p=s.context,u=n.contextType,typeof u=="object"&&u!==null?u=He(u):(u=Pe(n)?un:me.current,u=Un(t,u));var m=n.getDerivedStateFromProps;(f=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||p!==u)&&Bu(t,s,r,u),kt=!1,p=t.memoizedState,s.state=p,to(t,r,s,i);var x=t.memoizedState;a!==d||p!==x||ke.current||kt?(typeof m=="function"&&(il(t,n,m,r),x=t.memoizedState),(c=kt||zu(t,n,c,r,p,x,u)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,u),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,u)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=u,r=c):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return al(e,t,n,r,o,i)}function al(e,t,n,r,i,o){Yf(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Lu(t,n,!1),yt(e,t,o);r=t.stateNode,Hg.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=$n(t,e.child,null,o),t.child=$n(t,null,a,o)):xe(e,t,a,o),t.memoizedState=r.state,i&&Lu(t,n,!0),t.child}function Zf(e){var t=e.stateNode;t.pendingContext?Ru(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ru(e,t.context,!1),aa(e,t.containerInfo)}function $u(e,t,n,r,i){return Hn(),na(i),t.flags|=256,xe(e,t,n,r),t.child}var ul={dehydrated:null,treeContext:null,retryLane:0};function cl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Jf(e,t,n){var r=t.pendingProps,i=$.current,o=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),I($,i&1),e===null)return nl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Ao(s,r,0,null),e=ln(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=cl(n),t.memoizedState=ul,e):ga(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return $g(e,t,s,r,a,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,a=i.sibling;var u={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=zt(i,u),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?o=zt(a,o):(o=ln(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?cl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=ul,r}return o=e.child,e=o.sibling,r=zt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ga(e,t){return t=Ao({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function xi(e,t,n,r){return r!==null&&na(r),$n(t,e.child,null,n),e=ga(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $g(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=ds(Error(C(422))),xi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Ao({mode:"visible",children:r.children},i,0,null),o=ln(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&$n(t,e.child,null,s),t.child.memoizedState=cl(s),t.memoizedState=ul,o);if(!(t.mode&1))return xi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,o=Error(C(419)),r=ds(o,r,void 0),xi(e,t,s,r)}if(a=(s&e.childLanes)!==0,Ce||a){if(r=se,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,gt(e,i),Ze(r,e,i,-1))}return ja(),r=ds(Error(C(421))),xi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=iy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Ae=bt(i.nextSibling),Me=t,H=!0,Qe=null,e!==null&&(Ie[Fe++]=at,Ie[Fe++]=ut,Ie[Fe++]=cn,at=e.id,ut=e.overflow,cn=t),t=ga(t,r.children),t.flags|=4096,t)}function Xu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),rl(e.return,t,n)}function fs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function qf(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(xe(e,t,r.children,n),r=$.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Xu(e,n,t);else if(e.tag===19)Xu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(I($,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&no(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),fs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&no(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}fs(t,!0,n,null,o);break;case"together":fs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function yt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),fn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=zt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=zt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Xg(e,t,n){switch(t.tag){case 3:Zf(t),Hn();break;case 5:Pf(t);break;case 1:Pe(t.type)&&Yi(t);break;case 4:aa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;I(qi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(I($,$.current&1),t.flags|=128,null):n&t.child.childLanes?Jf(e,t,n):(I($,$.current&1),e=yt(e,t,n),e!==null?e.sibling:null);I($,$.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),I($,$.current),r)break;return null;case 22:case 23:return t.lanes=0,Qf(e,t,n)}return yt(e,t,n)}var ep,dl,tp,np;ep=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};dl=function(){};tp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,rn(it.current);var o=null;switch(n){case"input":i=bs(e,i),r=bs(e,r),o=[];break;case"select":i=K({},i,{value:void 0}),r=K({},r,{value:void 0}),o=[];break;case"textarea":i=Ns(e,i),r=Ns(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ki)}Bs(n,r);var s;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var a=i[c];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Rr.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(a=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&u!==a&&(u!=null||a!=null))if(c==="style")if(a){for(s in a)!a.hasOwnProperty(s)||u&&u.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in u)u.hasOwnProperty(s)&&a[s]!==u[s]&&(n||(n={}),n[s]=u[s])}else n||(o||(o=[]),o.push(c,n)),n=u;else c==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,a=a?a.__html:void 0,u!=null&&a!==u&&(o=o||[]).push(c,u)):c==="children"?typeof u!="string"&&typeof u!="number"||(o=o||[]).push(c,""+u):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Rr.hasOwnProperty(c)?(u!=null&&c==="onScroll"&&F("scroll",e),o||a===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};np=function(e,t,n,r){n!==r&&(t.flags|=4)};function sr(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Gg(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return fe(t),null;case 1:return Pe(t.type)&&Qi(),fe(t),null;case 3:return r=t.stateNode,Xn(),_(ke),_(me),ca(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(gi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Qe!==null&&(vl(Qe),Qe=null))),dl(e,t),fe(t),null;case 5:ua(t);var i=rn(Fr.current);if(n=t.type,e!==null&&t.stateNode!=null)tp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return fe(t),null}if(e=rn(it.current),gi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[nt]=t,r[Br]=o,e=(t.mode&1)!==0,n){case"dialog":F("cancel",r),F("close",r);break;case"iframe":case"object":case"embed":F("load",r);break;case"video":case"audio":for(i=0;i<pr.length;i++)F(pr[i],r);break;case"source":F("error",r);break;case"img":case"image":case"link":F("error",r),F("load",r);break;case"details":F("toggle",r);break;case"input":tu(r,o),F("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},F("invalid",r);break;case"textarea":ru(r,o),F("invalid",r)}Bs(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var a=o[s];s==="children"?typeof a=="string"?r.textContent!==a&&(o.suppressHydrationWarning!==!0&&mi(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(o.suppressHydrationWarning!==!0&&mi(r.textContent,a,e),i=["children",""+a]):Rr.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&F("scroll",r)}switch(n){case"input":li(r),nu(r,o,!0);break;case"textarea":li(r),iu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ki)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ld(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[nt]=t,e[Br]=r,ep(e,t,!1,!1),t.stateNode=e;e:{switch(s=Is(n,r),n){case"dialog":F("cancel",e),F("close",e),i=r;break;case"iframe":case"object":case"embed":F("load",e),i=r;break;case"video":case"audio":for(i=0;i<pr.length;i++)F(pr[i],e);i=r;break;case"source":F("error",e),i=r;break;case"img":case"image":case"link":F("error",e),F("load",e),i=r;break;case"details":F("toggle",e),i=r;break;case"input":tu(e,r),i=bs(e,r),F("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=K({},r,{value:void 0}),F("invalid",e);break;case"textarea":ru(e,r),i=Ns(e,r),F("invalid",e);break;default:i=r}Bs(n,i),a=i;for(o in a)if(a.hasOwnProperty(o)){var u=a[o];o==="style"?Dd(e,u):o==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&Ad(e,u)):o==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Lr(e,u):typeof u=="number"&&Lr(e,""+u):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Rr.hasOwnProperty(o)?u!=null&&o==="onScroll"&&F("scroll",e):u!=null&&Fl(e,o,u,s))}switch(n){case"input":li(e),nu(e,r,!1);break;case"textarea":li(e),iu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+It(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Nn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Nn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ki)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return fe(t),null;case 6:if(e&&t.stateNode!=null)np(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=rn(Fr.current),rn(it.current),gi(t)){if(r=t.stateNode,n=t.memoizedProps,r[nt]=t,(o=r.nodeValue!==n)&&(e=Me,e!==null))switch(e.tag){case 3:mi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&mi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nt]=t,t.stateNode=r}return fe(t),null;case 13:if(_($),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Ae!==null&&t.mode&1&&!(t.flags&128))Sf(),Hn(),t.flags|=98560,o=!1;else if(o=gi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(C(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(C(317));o[nt]=t}else Hn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;fe(t),o=!1}else Qe!==null&&(vl(Qe),Qe=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||$.current&1?ne===0&&(ne=3):ja())),t.updateQueue!==null&&(t.flags|=4),fe(t),null);case 4:return Xn(),dl(e,t),e===null&&Nr(t.stateNode.containerInfo),fe(t),null;case 10:return oa(t.type._context),fe(t),null;case 17:return Pe(t.type)&&Qi(),fe(t),null;case 19:if(_($),o=t.memoizedState,o===null)return fe(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)sr(o,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=no(e),s!==null){for(t.flags|=128,sr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return I($,$.current&1|2),t.child}e=e.sibling}o.tail!==null&&Z()>Kn&&(t.flags|=128,r=!0,sr(o,!1),t.lanes=4194304)}else{if(!r)if(e=no(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),sr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return fe(t),null}else 2*Z()-o.renderingStartTime>Kn&&n!==1073741824&&(t.flags|=128,r=!0,sr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Z(),t.sibling=null,n=$.current,I($,r?n&1|2:n&1),t):(fe(t),null);case 22:case 23:return wa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(fe(t),t.subtreeFlags&6&&(t.flags|=8192)):fe(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function Kg(e,t){switch(ta(t),t.tag){case 1:return Pe(t.type)&&Qi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Xn(),_(ke),_(me),ca(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ua(t),null;case 13:if(_($),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));Hn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return _($),null;case 4:return Xn(),null;case 10:return oa(t.type._context),null;case 22:case 23:return wa(),null;case 24:return null;default:return null}}var vi=!1,he=!1,Qg=typeof WeakSet=="function"?WeakSet:Set,R=null;function An(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function fl(e,t,n){try{n()}catch(r){Q(e,t,r)}}var Gu=!1;function Yg(e,t){if(Qs=$i,e=lf(),ql(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,a=-1,u=-1,c=0,f=0,d=e,p=null;t:for(;;){for(var m;d!==n||i!==0&&d.nodeType!==3||(a=s+i),d!==o||r!==0&&d.nodeType!==3||(u=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(m=d.firstChild)!==null;)p=d,d=m;for(;;){if(d===e)break t;if(p===n&&++c===i&&(a=s),p===o&&++f===r&&(u=s),(m=d.nextSibling)!==null)break;d=p,p=d.parentNode}d=m}n=a===-1||u===-1?null:{start:a,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ys={focusedElem:e,selectionRange:n},$i=!1,R=t;R!==null;)if(t=R,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,R=e;else for(;R!==null;){t=R;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var v=x.memoizedProps,w=x.memoizedState,y=t.stateNode,h=y.getSnapshotBeforeUpdate(t.elementType===t.type?v:Ge(t.type,v),w);y.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(S){Q(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,R=e;break}R=t.return}return x=Gu,Gu=!1,x}function wr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&fl(t,n,o)}i=i.next}while(i!==r)}}function Ro(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function rp(e){var t=e.alternate;t!==null&&(e.alternate=null,rp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nt],delete t[Br],delete t[qs],delete t[Dg],delete t[bg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ip(e){return e.tag===5||e.tag===3||e.tag===4}function Ku(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ip(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ki));else if(r!==4&&(e=e.child,e!==null))for(hl(e,t,n),e=e.sibling;e!==null;)hl(e,t,n),e=e.sibling}function ml(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ml(e,t,n),e=e.sibling;e!==null;)ml(e,t,n),e=e.sibling}var ae=null,Ke=!1;function wt(e,t,n){for(n=n.child;n!==null;)op(e,t,n),n=n.sibling}function op(e,t,n){if(rt&&typeof rt.onCommitFiberUnmount=="function")try{rt.onCommitFiberUnmount(So,n)}catch{}switch(n.tag){case 5:he||An(n,t);case 6:var r=ae,i=Ke;ae=null,wt(e,t,n),ae=r,Ke=i,ae!==null&&(Ke?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Ke?(e=ae,n=n.stateNode,e.nodeType===8?os(e.parentNode,n):e.nodeType===1&&os(e,n),br(e)):os(ae,n.stateNode));break;case 4:r=ae,i=Ke,ae=n.stateNode.containerInfo,Ke=!0,wt(e,t,n),ae=r,Ke=i;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&fl(n,t,s),i=i.next}while(i!==r)}wt(e,t,n);break;case 1:if(!he&&(An(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Q(n,t,a)}wt(e,t,n);break;case 21:wt(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,wt(e,t,n),he=r):wt(e,t,n);break;default:wt(e,t,n)}}function Qu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Qg),t.forEach(function(r){var i=oy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Xe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:ae=a.stateNode,Ke=!1;break e;case 3:ae=a.stateNode.containerInfo,Ke=!0;break e;case 4:ae=a.stateNode.containerInfo,Ke=!0;break e}a=a.return}if(ae===null)throw Error(C(160));op(o,s,i),ae=null,Ke=!1;var u=i.alternate;u!==null&&(u.return=null),i.return=null}catch(c){Q(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sp(t,e),t=t.sibling}function sp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Xe(t,e),et(e),r&4){try{wr(3,e,e.return),Ro(3,e)}catch(v){Q(e,e.return,v)}try{wr(5,e,e.return)}catch(v){Q(e,e.return,v)}}break;case 1:Xe(t,e),et(e),r&512&&n!==null&&An(n,n.return);break;case 5:if(Xe(t,e),et(e),r&512&&n!==null&&An(n,n.return),e.flags&32){var i=e.stateNode;try{Lr(i,"")}catch(v){Q(e,e.return,v)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,a=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{a==="input"&&o.type==="radio"&&o.name!=null&&Td(i,o),Is(a,s);var c=Is(a,o);for(s=0;s<u.length;s+=2){var f=u[s],d=u[s+1];f==="style"?Dd(i,d):f==="dangerouslySetInnerHTML"?Ad(i,d):f==="children"?Lr(i,d):Fl(i,f,d,c)}switch(a){case"input":Vs(i,o);break;case"textarea":Rd(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var m=o.value;m!=null?Nn(i,!!o.multiple,m,!1):p!==!!o.multiple&&(o.defaultValue!=null?Nn(i,!!o.multiple,o.defaultValue,!0):Nn(i,!!o.multiple,o.multiple?[]:"",!1))}i[Br]=o}catch(v){Q(e,e.return,v)}}break;case 6:if(Xe(t,e),et(e),r&4){if(e.stateNode===null)throw Error(C(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(v){Q(e,e.return,v)}}break;case 3:if(Xe(t,e),et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{br(t.containerInfo)}catch(v){Q(e,e.return,v)}break;case 4:Xe(t,e),et(e);break;case 13:Xe(t,e),et(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(va=Z())),r&4&&Qu(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(he=(c=he)||f,Xe(t,e),he=c):Xe(t,e),et(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(R=e,f=e.child;f!==null;){for(d=R=f;R!==null;){switch(p=R,m=p.child,p.tag){case 0:case 11:case 14:case 15:wr(4,p,p.return);break;case 1:An(p,p.return);var x=p.stateNode;if(typeof x.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(v){Q(r,n,v)}}break;case 5:An(p,p.return);break;case 22:if(p.memoizedState!==null){Zu(d);continue}}m!==null?(m.return=p,R=m):Zu(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{i=d.stateNode,c?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(a=d.stateNode,u=d.memoizedProps.style,s=u!=null&&u.hasOwnProperty("display")?u.display:null,a.style.display=Md("display",s))}catch(v){Q(e,e.return,v)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(v){Q(e,e.return,v)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Xe(t,e),et(e),r&4&&Qu(e);break;case 21:break;default:Xe(t,e),et(e)}}function et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ip(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Lr(i,""),r.flags&=-33);var o=Ku(e);ml(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Ku(e);hl(e,a,s);break;default:throw Error(C(161))}}catch(u){Q(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zg(e,t,n){R=e,lp(e)}function lp(e,t,n){for(var r=(e.mode&1)!==0;R!==null;){var i=R,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||vi;if(!s){var a=i.alternate,u=a!==null&&a.memoizedState!==null||he;a=vi;var c=he;if(vi=s,(he=u)&&!c)for(R=i;R!==null;)s=R,u=s.child,s.tag===22&&s.memoizedState!==null?Ju(i):u!==null?(u.return=s,R=u):Ju(i);for(;o!==null;)R=o,lp(o),o=o.sibling;R=i,vi=a,he=c}Yu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,R=o):Yu(e)}}function Yu(e){for(;R!==null;){var t=R;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Ro(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ge(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Vu(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Vu(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&br(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}he||t.flags&512&&pl(t)}catch(p){Q(t,t.return,p)}}if(t===e){R=null;break}if(n=t.sibling,n!==null){n.return=t.return,R=n;break}R=t.return}}function Zu(e){for(;R!==null;){var t=R;if(t===e){R=null;break}var n=t.sibling;if(n!==null){n.return=t.return,R=n;break}R=t.return}}function Ju(e){for(;R!==null;){var t=R;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ro(4,t)}catch(u){Q(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(u){Q(t,i,u)}}var o=t.return;try{pl(t)}catch(u){Q(t,o,u)}break;case 5:var s=t.return;try{pl(t)}catch(u){Q(t,s,u)}}}catch(u){Q(t,t.return,u)}if(t===e){R=null;break}var a=t.sibling;if(a!==null){a.return=t.return,R=a;break}R=t.return}}var Jg=Math.ceil,oo=vt.ReactCurrentDispatcher,ya=vt.ReactCurrentOwner,Ue=vt.ReactCurrentBatchConfig,z=0,se=null,ee=null,ue=0,Le=0,Mn=Ht(0),ne=0,Hr=null,fn=0,Lo=0,xa=0,jr=null,je=null,va=0,Kn=1/0,st=null,so=!1,gl=null,Ot=null,Si=!1,Lt=null,lo=0,Cr=0,yl=null,Oi=-1,Ni=0;function ve(){return z&6?Z():Oi!==-1?Oi:Oi=Z()}function Nt(e){return e.mode&1?z&2&&ue!==0?ue&-ue:Og.transition!==null?(Ni===0&&(Ni=Hd()),Ni):(e=B,e!==0||(e=window.event,e=e===void 0?16:Zd(e.type)),e):1}function Ze(e,t,n,r){if(50<Cr)throw Cr=0,yl=null,Error(C(185));Yr(e,n,r),(!(z&2)||e!==se)&&(e===se&&(!(z&2)&&(Lo|=n),ne===4&&Tt(e,ue)),Ee(e,r),n===1&&z===0&&!(t.mode&1)&&(Kn=Z()+500,Po&&$t()))}function Ee(e,t){var n=e.callbackNode;Om(e,t);var r=Hi(e,e===se?ue:0);if(r===0)n!==null&&lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lu(n),t===1)e.tag===0?Vg(qu.bind(null,e)):yf(qu.bind(null,e)),Ag(function(){!(z&6)&&$t()}),n=null;else{switch($d(r)){case 1:n=$l;break;case 4:n=Wd;break;case 16:n=Ui;break;case 536870912:n=Ud;break;default:n=Ui}n=mp(n,ap.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ap(e,t){if(Oi=-1,Ni=0,z&6)throw Error(C(327));var n=e.callbackNode;if(_n()&&e.callbackNode!==n)return null;var r=Hi(e,e===se?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ao(e,r);else{t=r;var i=z;z|=2;var o=cp();(se!==e||ue!==t)&&(st=null,Kn=Z()+500,sn(e,t));do try{ty();break}catch(a){up(e,a)}while(!0);ia(),oo.current=o,z=i,ee!==null?t=0:(se=null,ue=0,t=ne)}if(t!==0){if(t===2&&(i=Hs(e),i!==0&&(r=i,t=xl(e,i))),t===1)throw n=Hr,sn(e,0),Tt(e,r),Ee(e,Z()),n;if(t===6)Tt(e,r);else{if(i=e.current.alternate,!(r&30)&&!qg(i)&&(t=ao(e,r),t===2&&(o=Hs(e),o!==0&&(r=o,t=xl(e,o))),t===1))throw n=Hr,sn(e,0),Tt(e,r),Ee(e,Z()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:Jt(e,je,st);break;case 3:if(Tt(e,r),(r&130023424)===r&&(t=va+500-Z(),10<t)){if(Hi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){ve(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Js(Jt.bind(null,e,je,st),t);break}Jt(e,je,st);break;case 4:if(Tt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ye(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=Z()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Jg(r/1960))-r,10<r){e.timeoutHandle=Js(Jt.bind(null,e,je,st),r);break}Jt(e,je,st);break;case 5:Jt(e,je,st);break;default:throw Error(C(329))}}}return Ee(e,Z()),e.callbackNode===n?ap.bind(null,e):null}function xl(e,t){var n=jr;return e.current.memoizedState.isDehydrated&&(sn(e,t).flags|=256),e=ao(e,t),e!==2&&(t=je,je=n,t!==null&&vl(t)),e}function vl(e){je===null?je=e:je.push.apply(je,e)}function qg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Je(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Tt(e,t){for(t&=~xa,t&=~Lo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function qu(e){if(z&6)throw Error(C(327));_n();var t=Hi(e,0);if(!(t&1))return Ee(e,Z()),null;var n=ao(e,t);if(e.tag!==0&&n===2){var r=Hs(e);r!==0&&(t=r,n=xl(e,r))}if(n===1)throw n=Hr,sn(e,0),Tt(e,t),Ee(e,Z()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Jt(e,je,st),Ee(e,Z()),null}function Sa(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&(Kn=Z()+500,Po&&$t())}}function pn(e){Lt!==null&&Lt.tag===0&&!(z&6)&&_n();var t=z;z|=1;var n=Ue.transition,r=B;try{if(Ue.transition=null,B=1,e)return e()}finally{B=r,Ue.transition=n,z=t,!(z&6)&&$t()}}function wa(){Le=Mn.current,_(Mn)}function sn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Lg(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(ta(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Qi();break;case 3:Xn(),_(ke),_(me),ca();break;case 5:ua(r);break;case 4:Xn();break;case 13:_($);break;case 19:_($);break;case 10:oa(r.type._context);break;case 22:case 23:wa()}n=n.return}if(se=e,ee=e=zt(e.current,null),ue=Le=t,ne=0,Hr=null,xa=Lo=fn=0,je=jr=null,nn!==null){for(t=0;t<nn.length;t++)if(n=nn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}nn=null}return e}function up(e,t){do{var n=ee;try{if(ia(),Di.current=io,ro){for(var r=G.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ro=!1}if(dn=0,oe=te=G=null,Sr=!1,_r=0,ya.current=null,n===null||n.return===null){ne=1,Hr=t,ee=null;break}e:{var o=e,s=n.return,a=n,u=t;if(t=ue,a.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var c=u,f=a,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var p=f.alternate;p?(f.updateQueue=p.updateQueue,f.memoizedState=p.memoizedState,f.lanes=p.lanes):(f.updateQueue=null,f.memoizedState=null)}var m=Fu(s);if(m!==null){m.flags&=-257,_u(m,s,a,o,t),m.mode&1&&Iu(o,c,t),t=m,u=c;var x=t.updateQueue;if(x===null){var v=new Set;v.add(u),t.updateQueue=v}else x.add(u);break e}else{if(!(t&1)){Iu(o,c,t),ja();break e}u=Error(C(426))}}else if(H&&a.mode&1){var w=Fu(s);if(w!==null){!(w.flags&65536)&&(w.flags|=256),_u(w,s,a,o,t),na(Gn(u,a));break e}}o=u=Gn(u,a),ne!==4&&(ne=2),jr===null?jr=[o]:jr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var y=Xf(o,u,t);bu(o,y);break e;case 1:a=u;var h=o.type,g=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Ot===null||!Ot.has(g)))){o.flags|=65536,t&=-t,o.lanes|=t;var S=Gf(o,a,t);bu(o,S);break e}}o=o.return}while(o!==null)}fp(n)}catch(j){t=j,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function cp(){var e=oo.current;return oo.current=io,e===null?io:e}function ja(){(ne===0||ne===3||ne===2)&&(ne=4),se===null||!(fn&268435455)&&!(Lo&268435455)||Tt(se,ue)}function ao(e,t){var n=z;z|=2;var r=cp();(se!==e||ue!==t)&&(st=null,sn(e,t));do try{ey();break}catch(i){up(e,i)}while(!0);if(ia(),z=n,oo.current=r,ee!==null)throw Error(C(261));return se=null,ue=0,ne}function ey(){for(;ee!==null;)dp(ee)}function ty(){for(;ee!==null&&!Em();)dp(ee)}function dp(e){var t=hp(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?fp(e):ee=t,ya.current=null}function fp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Kg(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,ee=null;return}}else if(n=Gg(n,t,Le),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);ne===0&&(ne=5)}function Jt(e,t,n){var r=B,i=Ue.transition;try{Ue.transition=null,B=1,ny(e,t,n,r)}finally{Ue.transition=i,B=r}return null}function ny(e,t,n,r){do _n();while(Lt!==null);if(z&6)throw Error(C(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Nm(e,o),e===se&&(ee=se=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Si||(Si=!0,mp(Ui,function(){return _n(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=Ue.transition,Ue.transition=null;var s=B;B=1;var a=z;z|=4,ya.current=null,Yg(e,n),sp(n,e),jg(Ys),$i=!!Qs,Ys=Qs=null,e.current=n,Zg(n),Tm(),z=a,B=s,Ue.transition=o}else e.current=n;if(Si&&(Si=!1,Lt=e,lo=i),o=e.pendingLanes,o===0&&(Ot=null),Am(n.stateNode),Ee(e,Z()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(so)throw so=!1,e=gl,gl=null,e;return lo&1&&e.tag!==0&&_n(),o=e.pendingLanes,o&1?e===yl?Cr++:(Cr=0,yl=e):Cr=0,$t(),null}function _n(){if(Lt!==null){var e=$d(lo),t=Ue.transition,n=B;try{if(Ue.transition=null,B=16>e?16:e,Lt===null)var r=!1;else{if(e=Lt,Lt=null,lo=0,z&6)throw Error(C(331));var i=z;for(z|=4,R=e.current;R!==null;){var o=R,s=o.child;if(R.flags&16){var a=o.deletions;if(a!==null){for(var u=0;u<a.length;u++){var c=a[u];for(R=c;R!==null;){var f=R;switch(f.tag){case 0:case 11:case 15:wr(8,f,o)}var d=f.child;if(d!==null)d.return=f,R=d;else for(;R!==null;){f=R;var p=f.sibling,m=f.return;if(rp(f),f===c){R=null;break}if(p!==null){p.return=m,R=p;break}R=m}}}var x=o.alternate;if(x!==null){var v=x.child;if(v!==null){x.child=null;do{var w=v.sibling;v.sibling=null,v=w}while(v!==null)}}R=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,R=s;else e:for(;R!==null;){if(o=R,o.flags&2048)switch(o.tag){case 0:case 11:case 15:wr(9,o,o.return)}var y=o.sibling;if(y!==null){y.return=o.return,R=y;break e}R=o.return}}var h=e.current;for(R=h;R!==null;){s=R;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,R=g;else e:for(s=h;R!==null;){if(a=R,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Ro(9,a)}}catch(j){Q(a,a.return,j)}if(a===s){R=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,R=S;break e}R=a.return}}if(z=i,$t(),rt&&typeof rt.onPostCommitFiberRoot=="function")try{rt.onPostCommitFiberRoot(So,e)}catch{}r=!0}return r}finally{B=n,Ue.transition=t}}return!1}function ec(e,t,n){t=Gn(n,t),t=Xf(e,t,1),e=Vt(e,t,1),t=ve(),e!==null&&(Yr(e,1,t),Ee(e,t))}function Q(e,t,n){if(e.tag===3)ec(e,e,n);else for(;t!==null;){if(t.tag===3){ec(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ot===null||!Ot.has(r))){e=Gn(n,e),e=Gf(t,e,1),t=Vt(t,e,1),e=ve(),t!==null&&(Yr(t,1,e),Ee(t,e));break}}t=t.return}}function ry(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=ve(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(ue&n)===n&&(ne===4||ne===3&&(ue&130023424)===ue&&500>Z()-va?sn(e,0):xa|=n),Ee(e,t)}function pp(e,t){t===0&&(e.mode&1?(t=ci,ci<<=1,!(ci&130023424)&&(ci=4194304)):t=1);var n=ve();e=gt(e,t),e!==null&&(Yr(e,t,n),Ee(e,n))}function iy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),pp(e,n)}function oy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),pp(e,n)}var hp;hp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ke.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,Xg(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,H&&t.flags&1048576&&xf(t,Ji,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vi(e,t),e=t.pendingProps;var i=Un(t,me.current);Fn(t,n),i=fa(null,t,r,e,i,n);var o=pa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(o=!0,Yi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,la(t),i.updater=To,t.stateNode=i,i._reactInternals=t,ol(t,r,e,n),t=al(null,t,r,!0,o,n)):(t.tag=0,H&&o&&ea(t),xe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ly(r),e=Ge(r,e),i){case 0:t=ll(null,t,r,e,n);break e;case 1:t=Hu(null,t,r,e,n);break e;case 11:t=Wu(null,t,r,e,n);break e;case 14:t=Uu(null,t,r,Ge(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),ll(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Hu(e,t,r,i,n);case 3:e:{if(Zf(t),e===null)throw Error(C(387));r=t.pendingProps,o=t.memoizedState,i=o.element,kf(e,t),to(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Gn(Error(C(423)),t),t=$u(e,t,r,n,i);break e}else if(r!==i){i=Gn(Error(C(424)),t),t=$u(e,t,r,n,i);break e}else for(Ae=bt(t.stateNode.containerInfo.firstChild),Me=t,H=!0,Qe=null,n=jf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hn(),r===i){t=yt(e,t,n);break e}xe(e,t,r,n)}t=t.child}return t;case 5:return Pf(t),e===null&&nl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Zs(r,i)?s=null:o!==null&&Zs(r,o)&&(t.flags|=32),Yf(e,t),xe(e,t,s,n),t.child;case 6:return e===null&&nl(t),null;case 13:return Jf(e,t,n);case 4:return aa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=$n(t,null,r,n):xe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Wu(e,t,r,i,n);case 7:return xe(e,t,t.pendingProps,n),t.child;case 8:return xe(e,t,t.pendingProps.children,n),t.child;case 12:return xe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,I(qi,r._currentValue),r._currentValue=s,o!==null)if(Je(o.value,s)){if(o.children===i.children&&!ke.current){t=yt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var a=o.dependencies;if(a!==null){s=o.child;for(var u=a.firstContext;u!==null;){if(u.context===r){if(o.tag===1){u=dt(-1,n&-n),u.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?u.next=u:(u.next=f.next,f.next=u),c.pending=u}}o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),rl(o.return,n,t),a.lanes|=n;break}u=u.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(C(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),rl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}xe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Fn(t,n),i=He(i),r=r(i),t.flags|=1,xe(e,t,r,n),t.child;case 14:return r=t.type,i=Ge(r,t.pendingProps),i=Ge(r.type,i),Uu(e,t,r,i,n);case 15:return Kf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Vi(e,t),t.tag=1,Pe(r)?(e=!0,Yi(t)):e=!1,Fn(t,n),$f(t,r,i),ol(t,r,i,n),al(null,t,r,!0,e,n);case 19:return qf(e,t,n);case 22:return Qf(e,t,n)}throw Error(C(156,t.tag))};function mp(e,t){return _d(e,t)}function sy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function _e(e,t,n,r){return new sy(e,t,n,r)}function Ca(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ly(e){if(typeof e=="function")return Ca(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Wl)return 11;if(e===Ul)return 14}return 2}function zt(e,t){var n=e.alternate;return n===null?(n=_e(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function zi(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Ca(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case wn:return ln(n.children,i,o,t);case _l:s=8,i|=8;break;case Ls:return e=_e(12,n,t,i|2),e.elementType=Ls,e.lanes=o,e;case As:return e=_e(13,n,t,i),e.elementType=As,e.lanes=o,e;case Ms:return e=_e(19,n,t,i),e.elementType=Ms,e.lanes=o,e;case kd:return Ao(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case jd:s=10;break e;case Cd:s=9;break e;case Wl:s=11;break e;case Ul:s=14;break e;case Ct:s=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=_e(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function ln(e,t,n,r){return e=_e(7,e,r,t),e.lanes=n,e}function Ao(e,t,n,r){return e=_e(22,e,r,t),e.elementType=kd,e.lanes=n,e.stateNode={isHidden:!1},e}function ps(e,t,n){return e=_e(6,e,null,t),e.lanes=n,e}function hs(e,t,n){return t=_e(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ay(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ko(0),this.expirationTimes=Ko(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ko(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ka(e,t,n,r,i,o,s,a,u){return e=new ay(e,t,n,a,u),t===1?(t=1,o===!0&&(t|=8)):t=0,o=_e(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},la(o),e}function uy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Sn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gp(e){if(!e)return Ft;e=e._reactInternals;e:{if(mn(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Pe(n))return gf(e,n,t)}return t}function yp(e,t,n,r,i,o,s,a,u){return e=ka(n,r,!0,e,i,o,s,a,u),e.context=gp(null),n=e.current,r=ve(),i=Nt(n),o=dt(r,i),o.callback=t??null,Vt(n,o,i),e.current.lanes=i,Yr(e,i,r),Ee(e,r),e}function Mo(e,t,n,r){var i=t.current,o=ve(),s=Nt(i);return n=gp(n),t.context===null?t.context=n:t.pendingContext=n,t=dt(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Vt(i,t,s),e!==null&&(Ze(e,i,s,o),Mi(e,i,s)),s}function uo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Pa(e,t){tc(e,t),(e=e.alternate)&&tc(e,t)}function cy(){return null}var xp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ea(e){this._internalRoot=e}Do.prototype.render=Ea.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));Mo(e,t,null,null)};Do.prototype.unmount=Ea.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;pn(function(){Mo(null,e,null,null)}),t[mt]=null}};function Do(e){this._internalRoot=e}Do.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Et.length&&t!==0&&t<Et[n].priority;n++);Et.splice(n,0,e),n===0&&Yd(e)}};function Ta(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function bo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function nc(){}function dy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var c=uo(s);o.call(c)}}var s=yp(t,r,e,0,null,!1,!1,"",nc);return e._reactRootContainer=s,e[mt]=s.current,Nr(e.nodeType===8?e.parentNode:e),pn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var c=uo(u);a.call(c)}}var u=ka(e,0,!1,null,null,!1,!1,"",nc);return e._reactRootContainer=u,e[mt]=u.current,Nr(e.nodeType===8?e.parentNode:e),pn(function(){Mo(t,u,n,r)}),u}function Vo(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var a=i;i=function(){var u=uo(s);a.call(u)}}Mo(t,s,e,i)}else s=dy(n,t,e,i,r);return uo(s)}Xd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fr(t.pendingLanes);n!==0&&(Xl(t,n|1),Ee(t,Z()),!(z&6)&&(Kn=Z()+500,$t()))}break;case 13:pn(function(){var r=gt(e,1);if(r!==null){var i=ve();Ze(r,e,1,i)}}),Pa(e,1)}};Gl=function(e){if(e.tag===13){var t=gt(e,134217728);if(t!==null){var n=ve();Ze(t,e,134217728,n)}Pa(e,134217728)}};Gd=function(e){if(e.tag===13){var t=Nt(e),n=gt(e,t);if(n!==null){var r=ve();Ze(n,e,t,r)}Pa(e,t)}};Kd=function(){return B};Qd=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};_s=function(e,t,n){switch(t){case"input":if(Vs(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=ko(r);if(!i)throw Error(C(90));Ed(r),Vs(r,i)}}}break;case"textarea":Rd(e,n);break;case"select":t=n.value,t!=null&&Nn(e,!!n.multiple,t,!1)}};Od=Sa;Nd=pn;var fy={usingClientEntryPoint:!1,Events:[Jr,Pn,ko,bd,Vd,Sa]},lr={findFiberByHostInstance:tn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},py={bundleType:lr.bundleType,version:lr.version,rendererPackageName:lr.rendererPackageName,rendererConfig:lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Id(e),e===null?null:e.stateNode},findFiberByHostInstance:lr.findFiberByHostInstance||cy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var wi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!wi.isDisabled&&wi.supportsFiber)try{So=wi.inject(py),rt=wi}catch{}}Ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fy;Ve.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ta(t))throw Error(C(200));return uy(e,t,null,n)};Ve.createRoot=function(e,t){if(!Ta(e))throw Error(C(299));var n=!1,r="",i=xp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ka(e,1,!1,null,null,n,!1,r,i),e[mt]=t.current,Nr(e.nodeType===8?e.parentNode:e),new Ea(t)};Ve.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=Id(t),e=e===null?null:e.stateNode,e};Ve.flushSync=function(e){return pn(e)};Ve.hydrate=function(e,t,n){if(!bo(t))throw Error(C(200));return Vo(null,e,t,!0,n)};Ve.hydrateRoot=function(e,t,n){if(!Ta(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=xp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=yp(t,null,e,1,n??null,i,!1,o,s),e[mt]=t.current,Nr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Do(t)};Ve.render=function(e,t,n){if(!bo(t))throw Error(C(200));return Vo(null,e,t,!1,n)};Ve.unmountComponentAtNode=function(e){if(!bo(e))throw Error(C(40));return e._reactRootContainer?(pn(function(){Vo(null,null,e,!1,function(){e._reactRootContainer=null,e[mt]=null})}),!0):!1};Ve.unstable_batchedUpdates=Sa;Ve.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!bo(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return Vo(e,t,n,!1,r)};Ve.version="18.3.1-next-f1338f8080-20240426";function vp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vp)}catch(e){console.error(e)}}vp(),xd.exports=Ve;var hy=xd.exports,rc=hy;Ts.createRoot=rc.createRoot,Ts.hydrateRoot=rc.hydrateRoot;const Sp=L.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Oo=L.createContext({}),Ra=L.createContext(null),No=typeof document<"u",my=No?L.useLayoutEffect:L.useEffect,wp=L.createContext({strict:!1}),La=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),gy="framerAppearId",jp="data-"+La(gy);function yy(e,t,n,r){const{visualElement:i}=L.useContext(Oo),o=L.useContext(wp),s=L.useContext(Ra),a=L.useContext(Sp).reducedMotion,u=L.useRef();r=r||o.renderer,!u.current&&r&&(u.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:a}));const c=u.current;L.useInsertionEffect(()=>{c&&c.update(n,s)});const f=L.useRef(!!(n[jp]&&!window.HandoffComplete));return my(()=>{c&&(c.render(),f.current&&c.animationState&&c.animationState.animateChanges())}),L.useEffect(()=>{c&&(c.updateFeatures(),!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(f.current=!1,window.HandoffComplete=!0))}),c}function Dn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function xy(e,t,n){return L.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Dn(n)&&(n.current=r))},[t])}function $r(e){return typeof e=="string"||Array.isArray(e)}function zo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Aa=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ma=["initial",...Aa];function Bo(e){return zo(e.animate)||Ma.some(t=>$r(e[t]))}function Cp(e){return!!(Bo(e)||e.variants)}function vy(e,t){if(Bo(e)){const{initial:n,animate:r}=e;return{initial:n===!1||$r(n)?n:void 0,animate:$r(r)?r:void 0}}return e.inherit!==!1?t:{}}function Sy(e){const{initial:t,animate:n}=vy(e,L.useContext(Oo));return L.useMemo(()=>({initial:t,animate:n}),[ic(t),ic(n)])}function ic(e){return Array.isArray(e)?e.join(" "):e}const oc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Xr={};for(const e in oc)Xr[e]={isEnabled:t=>oc[e].some(n=>!!t[n])};function wy(e){for(const t in e)Xr[t]={...Xr[t],...e[t]}}const kp=L.createContext({}),Pp=L.createContext({}),jy=Symbol.for("motionComponentSymbol");function Cy({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&wy(e);function o(a,u){let c;const f={...L.useContext(Sp),...a,layoutId:ky(a)},{isStatic:d}=f,p=Sy(a),m=r(a,d);if(!d&&No){p.visualElement=yy(i,m,f,t);const x=L.useContext(Pp),v=L.useContext(wp).strict;p.visualElement&&(c=p.visualElement.loadFeatures(f,v,e,x))}return L.createElement(Oo.Provider,{value:p},c&&p.visualElement?L.createElement(c,{visualElement:p.visualElement,...f}):null,n(i,a,xy(m,p.visualElement,u),m,d,p.visualElement))}const s=L.forwardRef(o);return s[jy]=i,s}function ky({layoutId:e}){const t=L.useContext(kp).id;return t&&e!==void 0?t+"-"+e:e}function Py(e){function t(r,i={}){return Cy(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Ey=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Da(e){return typeof e!="string"||e.includes("-")?!1:!!(Ey.indexOf(e)>-1||/[A-Z]/.test(e))}const co={};function Ty(e){Object.assign(co,e)}const ei=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gn=new Set(ei);function Ep(e,{layout:t,layoutId:n}){return gn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!co[e]||e==="opacity")}const Te=e=>!!(e&&e.getVelocity),Ry={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ly=ei.length;function Ay(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Ly;s++){const a=ei[s];if(e[a]!==void 0){const u=Ry[a]||a;o+=`${u}(${e[a]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const Tp=e=>t=>typeof t=="string"&&t.startsWith(e),Rp=Tp("--"),Sl=Tp("var(--"),My=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Dy=(e,t)=>t&&typeof e=="number"?t.transform(e):e,_t=(e,t,n)=>Math.min(Math.max(n,e),t),yn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},kr={...yn,transform:e=>_t(0,1,e)},ji={...yn,default:1},Pr=e=>Math.round(e*1e5)/1e5,Io=/(-)?([\d]*\.?[\d])+/g,Lp=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,by=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ti(e){return typeof e=="string"}const ni=e=>({test:t=>ti(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),jt=ni("deg"),ot=ni("%"),A=ni("px"),Vy=ni("vh"),Oy=ni("vw"),sc={...ot,parse:e=>ot.parse(e)/100,transform:e=>ot.transform(e*100)},lc={...yn,transform:Math.round},Ap={borderWidth:A,borderTopWidth:A,borderRightWidth:A,borderBottomWidth:A,borderLeftWidth:A,borderRadius:A,radius:A,borderTopLeftRadius:A,borderTopRightRadius:A,borderBottomRightRadius:A,borderBottomLeftRadius:A,width:A,maxWidth:A,height:A,maxHeight:A,size:A,top:A,right:A,bottom:A,left:A,padding:A,paddingTop:A,paddingRight:A,paddingBottom:A,paddingLeft:A,margin:A,marginTop:A,marginRight:A,marginBottom:A,marginLeft:A,rotate:jt,rotateX:jt,rotateY:jt,rotateZ:jt,scale:ji,scaleX:ji,scaleY:ji,scaleZ:ji,skew:jt,skewX:jt,skewY:jt,distance:A,translateX:A,translateY:A,translateZ:A,x:A,y:A,z:A,perspective:A,transformPerspective:A,opacity:kr,originX:sc,originY:sc,originZ:A,zIndex:lc,fillOpacity:kr,strokeOpacity:kr,numOctaves:lc};function ba(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:a}=e;let u=!1,c=!1,f=!0;for(const d in t){const p=t[d];if(Rp(d)){o[d]=p;continue}const m=Ap[d],x=Dy(p,m);if(gn.has(d)){if(u=!0,s[d]=x,!f)continue;p!==(m.default||0)&&(f=!1)}else d.startsWith("origin")?(c=!0,a[d]=x):i[d]=x}if(t.transform||(u||r?i.transform=Ay(e.transform,n,f,r):i.transform&&(i.transform="none")),c){const{originX:d="50%",originY:p="50%",originZ:m=0}=a;i.transformOrigin=`${d} ${p} ${m}`}}const Va=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Mp(e,t,n){for(const r in t)!Te(t[r])&&!Ep(r,n)&&(e[r]=t[r])}function Ny({transformTemplate:e},t,n){return L.useMemo(()=>{const r=Va();return ba(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function zy(e,t,n){const r=e.style||{},i={};return Mp(i,r,e),Object.assign(i,Ny(e,t,n)),e.transformValues?e.transformValues(i):i}function By(e,t,n){const r={},i=zy(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const Iy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function fo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Iy.has(e)}let Dp=e=>!fo(e);function Fy(e){e&&(Dp=t=>t.startsWith("on")?!fo(t):e(t))}try{Fy(require("@emotion/is-prop-valid").default)}catch{}function _y(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Dp(i)||n===!0&&fo(i)||!t&&!fo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function ac(e,t,n){return typeof e=="string"?e:A.transform(t+n*e)}function Wy(e,t,n){const r=ac(t,e.x,e.width),i=ac(n,e.y,e.height);return`${r} ${i}`}const Uy={offset:"stroke-dashoffset",array:"stroke-dasharray"},Hy={offset:"strokeDashoffset",array:"strokeDasharray"};function $y(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Uy:Hy;e[o.offset]=A.transform(-r);const s=A.transform(t),a=A.transform(n);e[o.array]=`${s} ${a}`}function Oa(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:a=1,pathOffset:u=0,...c},f,d,p){if(ba(e,c,f,p),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:m,style:x,dimensions:v}=e;m.transform&&(v&&(x.transform=m.transform),delete m.transform),v&&(i!==void 0||o!==void 0||x.transform)&&(x.transformOrigin=Wy(v,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(m.x=t),n!==void 0&&(m.y=n),r!==void 0&&(m.scale=r),s!==void 0&&$y(m,s,a,u,!1)}const bp=()=>({...Va(),attrs:{}}),Na=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Xy(e,t,n,r){const i=L.useMemo(()=>{const o=bp();return Oa(o,t,{enableHardwareAcceleration:!1},Na(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Mp(o,e.style,e),i.style={...o,...i.style}}return i}function Gy(e=!1){return(n,r,i,{latestValues:o},s)=>{const u=(Da(n)?Xy:By)(r,o,s,n),f={..._y(r,typeof n=="string",e),...u,ref:i},{children:d}=r,p=L.useMemo(()=>Te(d)?d.get():d,[d]);return L.createElement(n,{...f,children:p})}}function Vp(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Op=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Np(e,t,n,r){Vp(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Op.has(i)?i:La(i),t.attrs[i])}function za(e,t){const{style:n}=e,r={};for(const i in n)(Te(n[i])||t.style&&Te(t.style[i])||Ep(i,e))&&(r[i]=n[i]);return r}function zp(e,t){const n=za(e,t);for(const r in e)if(Te(e[r])||Te(t[r])){const i=ei.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Ba(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Ky(e){const t=L.useRef(null);return t.current===null&&(t.current=e()),t.current}const po=e=>Array.isArray(e),Qy=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Yy=e=>po(e)?e[e.length-1]||0:e;function Bi(e){const t=Te(e)?e.get():e;return Qy(t)?t.toValue():t}function Zy({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Jy(r,i,o,e),renderState:t()};return n&&(s.mount=a=>n(r,a,s)),s}const Bp=e=>(t,n)=>{const r=L.useContext(Oo),i=L.useContext(Ra),o=()=>Zy(e,t,r,i);return n?o():Ky(o)};function Jy(e,t,n,r){const i={},o=r(e,{});for(const p in o)i[p]=Bi(o[p]);let{initial:s,animate:a}=e;const u=Bo(e),c=Cp(e);t&&c&&!u&&e.inherit!==!1&&(s===void 0&&(s=t.initial),a===void 0&&(a=t.animate));let f=n?n.initial===!1:!1;f=f||s===!1;const d=f?a:s;return d&&typeof d!="boolean"&&!zo(d)&&(Array.isArray(d)?d:[d]).forEach(m=>{const x=Ba(e,m);if(!x)return;const{transitionEnd:v,transition:w,...y}=x;for(const h in y){let g=y[h];if(Array.isArray(g)){const S=f?g.length-1:0;g=g[S]}g!==null&&(i[h]=g)}for(const h in v)i[h]=v[h]}),i}const J=e=>e;class uc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function qy(e){let t=new uc,n=new uc,r=0,i=!1,o=!1;const s=new WeakSet,a={schedule:(u,c=!1,f=!1)=>{const d=f&&i,p=d?t:n;return c&&s.add(u),p.add(u)&&d&&i&&(r=t.order.length),u},cancel:u=>{n.remove(u),s.delete(u)},process:u=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let c=0;c<r;c++){const f=t.order[c];f(u),s.has(f)&&(a.schedule(f),e())}i=!1,o&&(o=!1,a.process(u))}};return a}const Ci=["prepare","read","update","preRender","render","postRender"],e0=40;function t0(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=Ci.reduce((d,p)=>(d[p]=qy(()=>n=!0),d),{}),s=d=>o[d].process(i),a=()=>{const d=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(d-i.timestamp,e0),1),i.timestamp=d,i.isProcessing=!0,Ci.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(a))},u=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:Ci.reduce((d,p)=>{const m=o[p];return d[p]=(x,v=!1,w=!1)=>(n||u(),m.schedule(x,v,w)),d},{}),cancel:d=>Ci.forEach(p=>o[p].cancel(d)),state:i,steps:o}}const{schedule:W,cancel:xt,state:pe,steps:ms}=t0(typeof requestAnimationFrame<"u"?requestAnimationFrame:J,!0),n0={useVisualState:Bp({scrapeMotionValuesFromProps:zp,createRenderState:bp,onMount:(e,t,{renderState:n,latestValues:r})=>{W.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),W.render(()=>{Oa(n,r,{enableHardwareAcceleration:!1},Na(t.tagName),e.transformTemplate),Np(t,n)})}})},r0={useVisualState:Bp({scrapeMotionValuesFromProps:za,createRenderState:Va})};function i0(e,{forwardMotionProps:t=!1},n,r){return{...Da(e)?n0:r0,preloadedFeatures:n,useRender:Gy(t),createVisualElement:r,Component:e}}function ct(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Ip=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function Fo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const o0=e=>t=>Ip(t)&&e(t,Fo(t));function ft(e,t,n,r){return ct(e,t,o0(n),r)}const s0=(e,t)=>n=>t(e(n)),Bt=(...e)=>e.reduce(s0);function Fp(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const cc=Fp("dragHorizontal"),dc=Fp("dragVertical");function _p(e){let t=!1;if(e==="y")t=dc();else if(e==="x")t=cc();else{const n=cc(),r=dc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Wp(){const e=_p(!0);return e?(e(),!1):!0}class Xt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function fc(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||Wp())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&W.update(()=>a[r](o,s))};return ft(e.current,n,i,{passive:!e.getProps()[r]})}class l0 extends Xt{mount(){this.unmount=Bt(fc(this.node,!0),fc(this.node,!1))}unmount(){}}class a0 extends Xt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Bt(ct(this.node.current,"focus",()=>this.onFocus()),ct(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Up=(e,t)=>t?e===t?!0:Up(e,t.parentElement):!1;function gs(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Fo(n))}class u0 extends Xt{constructor(){super(...arguments),this.removeStartListeners=J,this.removeEndListeners=J,this.removeAccessibleListeners=J,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=ft(window,"pointerup",(a,u)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:f,globalTapTarget:d}=this.node.getProps();W.update(()=>{!d&&!Up(this.node.current,a.target)?f&&f(a,u):c&&c(a,u)})},{passive:!(r.onTap||r.onPointerUp)}),s=ft(window,"pointercancel",(a,u)=>this.cancelPress(a,u),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Bt(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=a=>{a.key!=="Enter"||!this.checkPressEnd()||gs("up",(u,c)=>{const{onTap:f}=this.node.getProps();f&&W.update(()=>f(u,c))})};this.removeEndListeners(),this.removeEndListeners=ct(this.node.current,"keyup",s),gs("down",(a,u)=>{this.startPress(a,u)})},n=ct(this.node.current,"keydown",t),r=()=>{this.isPressing&&gs("cancel",(o,s)=>this.cancelPress(o,s))},i=ct(this.node.current,"blur",r);this.removeAccessibleListeners=Bt(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&W.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Wp()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&W.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=ft(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=ct(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Bt(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const wl=new WeakMap,ys=new WeakMap,c0=e=>{const t=wl.get(e.target);t&&t(e)},d0=e=>{e.forEach(c0)};function f0({root:e,...t}){const n=e||document;ys.has(n)||ys.set(n,{});const r=ys.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(d0,{root:e,...t})),r[i]}function p0(e,t,n){const r=f0(t);return wl.set(e,n),r.observe(e),()=>{wl.delete(e),r.unobserve(e)}}const h0={some:0,all:1};class m0 extends Xt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:h0[i]},a=u=>{const{isIntersecting:c}=u;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:f,onViewportLeave:d}=this.node.getProps(),p=c?f:d;p&&p(u)};return p0(this.node.current,s,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(g0(t,n))&&this.startObserver()}unmount(){}}function g0({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const y0={inView:{Feature:m0},tap:{Feature:u0},focus:{Feature:a0},hover:{Feature:l0}};function Hp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function x0(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function v0(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function _o(e,t,n){const r=e.getProps();return Ba(r,t,n!==void 0?n:r.custom,x0(e),v0(e))}let Ia=J;const an=e=>e*1e3,pt=e=>e/1e3,S0={current:!1},$p=e=>Array.isArray(e)&&typeof e[0]=="number";function Xp(e){return!!(!e||typeof e=="string"&&Gp[e]||$p(e)||Array.isArray(e)&&e.every(Xp))}const hr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Gp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:hr([0,.65,.55,1]),circOut:hr([.55,0,1,.45]),backIn:hr([.31,.01,.66,-.59]),backOut:hr([.33,1.53,.69,.99])};function Kp(e){if(e)return $p(e)?hr(e):Array.isArray(e)?e.map(Kp):Gp[e]}function w0(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:a,times:u}={}){const c={[t]:n};u&&(c.offset=u);const f=Kp(a);return Array.isArray(f)&&(c.easing=f),e.animate(c,{delay:r,duration:i,easing:Array.isArray(f)?"linear":f,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function j0(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Qp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,C0=1e-7,k0=12;function P0(e,t,n,r,i){let o,s,a=0;do s=t+(n-t)/2,o=Qp(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>C0&&++a<k0);return s}function ri(e,t,n,r){if(e===t&&n===r)return J;const i=o=>P0(o,0,1,e,n);return o=>o===0||o===1?o:Qp(i(o),t,r)}const E0=ri(.42,0,1,1),T0=ri(0,0,.58,1),Yp=ri(.42,0,.58,1),R0=e=>Array.isArray(e)&&typeof e[0]!="number",Zp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Jp=e=>t=>1-e(1-t),Fa=e=>1-Math.sin(Math.acos(e)),qp=Jp(Fa),L0=Zp(Fa),eh=ri(.33,1.53,.69,.99),_a=Jp(eh),A0=Zp(_a),M0=e=>(e*=2)<1?.5*_a(e):.5*(2-Math.pow(2,-10*(e-1))),D0={linear:J,easeIn:E0,easeInOut:Yp,easeOut:T0,circIn:Fa,circInOut:L0,circOut:qp,backIn:_a,backInOut:A0,backOut:eh,anticipate:M0},pc=e=>{if(Array.isArray(e)){Ia(e.length===4);const[t,n,r,i]=e;return ri(t,n,r,i)}else if(typeof e=="string")return D0[e];return e},Wa=(e,t)=>n=>!!(ti(n)&&by.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),th=(e,t,n)=>r=>{if(!ti(r))return r;const[i,o,s,a]=r.match(Io);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:a!==void 0?parseFloat(a):1}},b0=e=>_t(0,255,e),xs={...yn,transform:e=>Math.round(b0(e))},on={test:Wa("rgb","red"),parse:th("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+xs.transform(e)+", "+xs.transform(t)+", "+xs.transform(n)+", "+Pr(kr.transform(r))+")"};function V0(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const jl={test:Wa("#"),parse:V0,transform:on.transform},bn={test:Wa("hsl","hue"),parse:th("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+ot.transform(Pr(t))+", "+ot.transform(Pr(n))+", "+Pr(kr.transform(r))+")"},ye={test:e=>on.test(e)||jl.test(e)||bn.test(e),parse:e=>on.test(e)?on.parse(e):bn.test(e)?bn.parse(e):jl.parse(e),transform:e=>ti(e)?e:e.hasOwnProperty("red")?on.transform(e):bn.transform(e)},X=(e,t,n)=>-n*e+n*t+e;function vs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function O0({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const a=n<.5?n*(1+t):n+t-n*t,u=2*n-a;i=vs(u,a,e+1/3),o=vs(u,a,e),s=vs(u,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const Ss=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},N0=[jl,on,bn],z0=e=>N0.find(t=>t.test(e));function hc(e){const t=z0(e);let n=t.parse(e);return t===bn&&(n=O0(n)),n}const nh=(e,t)=>{const n=hc(e),r=hc(t),i={...n};return o=>(i.red=Ss(n.red,r.red,o),i.green=Ss(n.green,r.green,o),i.blue=Ss(n.blue,r.blue,o),i.alpha=X(n.alpha,r.alpha,o),on.transform(i))};function B0(e){var t,n;return isNaN(e)&&ti(e)&&(((t=e.match(Io))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Lp))===null||n===void 0?void 0:n.length)||0)>0}const rh={regex:My,countKey:"Vars",token:"${v}",parse:J},ih={regex:Lp,countKey:"Colors",token:"${c}",parse:ye.parse},oh={regex:Io,countKey:"Numbers",token:"${n}",parse:yn.parse};function ws(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function ho(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&ws(n,rh),ws(n,ih),ws(n,oh),n}function sh(e){return ho(e).values}function lh(e){const{values:t,numColors:n,numVars:r,tokenised:i}=ho(e),o=t.length;return s=>{let a=i;for(let u=0;u<o;u++)u<r?a=a.replace(rh.token,s[u]):u<r+n?a=a.replace(ih.token,ye.transform(s[u])):a=a.replace(oh.token,Pr(s[u]));return a}}const I0=e=>typeof e=="number"?0:e;function F0(e){const t=sh(e);return lh(e)(t.map(I0))}const Wt={test:B0,parse:sh,createTransformer:lh,getAnimatableNone:F0},ah=(e,t)=>n=>`${n>0?t:e}`;function uh(e,t){return typeof e=="number"?n=>X(e,t,n):ye.test(e)?nh(e,t):e.startsWith("var(")?ah(e,t):dh(e,t)}const ch=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>uh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},_0=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=uh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},dh=(e,t)=>{const n=Wt.createTransformer(t),r=ho(e),i=ho(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Bt(ch(r.values,i.values),n):ah(e,t)},Gr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},mc=(e,t)=>n=>X(e,t,n);function W0(e){return typeof e=="number"?mc:typeof e=="string"?ye.test(e)?nh:dh:Array.isArray(e)?ch:typeof e=="object"?_0:mc}function U0(e,t,n){const r=[],i=n||W0(e[0]),o=e.length-1;for(let s=0;s<o;s++){let a=i(e[s],e[s+1]);if(t){const u=Array.isArray(t)?t[s]||J:t;a=Bt(u,a)}r.push(a)}return r}function fh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(Ia(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=U0(t,r,i),a=s.length,u=c=>{let f=0;if(a>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const d=Gr(e[f],e[f+1],c);return s[f](d)};return n?c=>u(_t(e[0],e[o-1],c)):u}function H0(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Gr(0,t,r);e.push(X(n,1,i))}}function $0(e){const t=[0];return H0(t,e.length-1),t}function X0(e,t){return e.map(n=>n*t)}function G0(e,t){return e.map(()=>t||Yp).splice(0,e.length-1)}function mo({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=R0(r)?r.map(pc):pc(r),o={done:!1,value:t[0]},s=X0(n&&n.length===t.length?n:$0(t),e),a=fh(s,t,{ease:Array.isArray(i)?i:G0(t,i)});return{calculatedDuration:e,next:u=>(o.value=a(u),o.done=u>=e,o)}}function ph(e,t){return t?e*(1e3/t):0}const K0=5;function hh(e,t,n){const r=Math.max(t-K0,0);return ph(n-e(r),t-r)}const js=.001,Q0=.01,Y0=10,Z0=.05,J0=1;function q0({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=_t(Z0,J0,s),e=_t(Q0,Y0,pt(e)),s<1?(i=c=>{const f=c*s,d=f*e,p=f-n,m=Cl(c,s),x=Math.exp(-d);return js-p/m*x},o=c=>{const d=c*s*e,p=d*n+n,m=Math.pow(s,2)*Math.pow(c,2)*e,x=Math.exp(-d),v=Cl(Math.pow(c,2),s);return(-i(c)+js>0?-1:1)*((p-m)*x)/v}):(i=c=>{const f=Math.exp(-c*e),d=(c-n)*e+1;return-js+f*d},o=c=>{const f=Math.exp(-c*e),d=(n-c)*(e*e);return f*d});const a=5/e,u=tx(i,o,a);if(e=an(e),isNaN(u))return{stiffness:100,damping:10,duration:e};{const c=Math.pow(u,2)*r;return{stiffness:c,damping:s*2*Math.sqrt(r*c),duration:e}}}const ex=12;function tx(e,t,n){let r=n;for(let i=1;i<ex;i++)r=r-e(r)/t(r);return r}function Cl(e,t){return e*Math.sqrt(1-t*t)}const nx=["duration","bounce"],rx=["stiffness","damping","mass"];function gc(e,t){return t.some(n=>e[n]!==void 0)}function ix(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!gc(e,rx)&&gc(e,nx)){const n=q0(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function mh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:a,damping:u,mass:c,duration:f,velocity:d,isResolvedFromDuration:p}=ix({...r,velocity:-pt(r.velocity||0)}),m=d||0,x=u/(2*Math.sqrt(a*c)),v=o-i,w=pt(Math.sqrt(a/c)),y=Math.abs(v)<5;n||(n=y?.01:2),t||(t=y?.005:.5);let h;if(x<1){const g=Cl(w,x);h=S=>{const j=Math.exp(-x*w*S);return o-j*((m+x*w*v)/g*Math.sin(g*S)+v*Math.cos(g*S))}}else if(x===1)h=g=>o-Math.exp(-w*g)*(v+(m+w*v)*g);else{const g=w*Math.sqrt(x*x-1);h=S=>{const j=Math.exp(-x*w*S),E=Math.min(g*S,300);return o-j*((m+x*w*v)*Math.sinh(E)+g*v*Math.cosh(E))/g}}return{calculatedDuration:p&&f||null,next:g=>{const S=h(g);if(p)s.done=g>=f;else{let j=m;g!==0&&(x<1?j=hh(h,g,S):j=0);const E=Math.abs(j)<=n,P=Math.abs(o-S)<=t;s.done=E&&P}return s.value=s.done?o:S,s}}}function yc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:a,max:u,restDelta:c=.5,restSpeed:f}){const d=e[0],p={done:!1,value:d},m=k=>a!==void 0&&k<a||u!==void 0&&k>u,x=k=>a===void 0?u:u===void 0||Math.abs(a-k)<Math.abs(u-k)?a:u;let v=n*t;const w=d+v,y=s===void 0?w:s(w);y!==w&&(v=y-d);const h=k=>-v*Math.exp(-k/r),g=k=>y+h(k),S=k=>{const V=h(k),D=g(k);p.done=Math.abs(V)<=c,p.value=p.done?y:D};let j,E;const P=k=>{m(p.value)&&(j=k,E=mh({keyframes:[p.value,x(p.value)],velocity:hh(g,k,p.value),damping:i,stiffness:o,restDelta:c,restSpeed:f}))};return P(0),{calculatedDuration:null,next:k=>{let V=!1;return!E&&j===void 0&&(V=!0,S(k),P(k)),j!==void 0&&k>j?E.next(k-j):(!V&&S(k),p)}}}const ox=e=>{const t=({timestamp:n})=>e(n);return{start:()=>W.update(t,!0),stop:()=>xt(t),now:()=>pe.isProcessing?pe.timestamp:performance.now()}},xc=2e4;function vc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<xc;)t+=n,r=e.next(t);return t>=xc?1/0:t}const sx={decay:yc,inertia:yc,tween:mo,keyframes:mo,spring:mh};function go({autoplay:e=!0,delay:t=0,driver:n=ox,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:a="loop",onPlay:u,onStop:c,onComplete:f,onUpdate:d,...p}){let m=1,x=!1,v,w;const y=()=>{w=new Promise(b=>{v=b})};y();let h;const g=sx[i]||mo;let S;g!==mo&&typeof r[0]!="number"&&(S=fh([0,100],r,{clamp:!1}),r=[0,100]);const j=g({...p,keyframes:r});let E;a==="mirror"&&(E=g({...p,keyframes:[...r].reverse(),velocity:-(p.velocity||0)}));let P="idle",k=null,V=null,D=null;j.calculatedDuration===null&&o&&(j.calculatedDuration=vc(j));const{calculatedDuration:re}=j;let le=1/0,ge=1/0;re!==null&&(le=re+s,ge=le*(o+1)-s);let ie=0;const St=b=>{if(V===null)return;m>0&&(V=Math.min(V,b)),m<0&&(V=Math.min(b-ge/m,V)),k!==null?ie=k:ie=Math.round(b-V)*m;const U=ie-t*(m>=0?1:-1),Gt=m>=0?U<0:U>ge;ie=Math.max(U,0),P==="finished"&&k===null&&(ie=ge);let qe=ie,xn=j;if(o){const Wo=Math.min(ie,ge)/le;let ii=Math.floor(Wo),Qt=Wo%1;!Qt&&Wo>=1&&(Qt=1),Qt===1&&ii--,ii=Math.min(ii,o+1),!!(ii%2)&&(a==="reverse"?(Qt=1-Qt,s&&(Qt-=s/le)):a==="mirror"&&(xn=E)),qe=_t(0,1,Qt)*le}const Re=Gt?{done:!1,value:r[0]}:xn.next(qe);S&&(Re.value=S(Re.value));let{done:Kt}=Re;!Gt&&re!==null&&(Kt=m>=0?ie>=ge:ie<=0);const _h=k===null&&(P==="finished"||P==="running"&&Kt);return d&&d(Re.value),_h&&T(),Re},Y=()=>{h&&h.stop(),h=void 0},Ne=()=>{P="idle",Y(),v(),y(),V=D=null},T=()=>{P="finished",f&&f(),Y(),v()},M=()=>{if(x)return;h||(h=n(St));const b=h.now();u&&u(),k!==null?V=b-k:(!V||P==="finished")&&(V=b),P==="finished"&&y(),D=V,k=null,P="running",h.start()};e&&M();const O={then(b,U){return w.then(b,U)},get time(){return pt(ie)},set time(b){b=an(b),ie=b,k!==null||!h||m===0?k=b:V=h.now()-b/m},get duration(){const b=j.calculatedDuration===null?vc(j):j.calculatedDuration;return pt(b)},get speed(){return m},set speed(b){b===m||!h||(m=b,O.time=pt(ie))},get state(){return P},play:M,pause:()=>{P="paused",k=ie},stop:()=>{x=!0,P!=="idle"&&(P="idle",c&&c(),Ne())},cancel:()=>{D!==null&&St(D),Ne()},complete:()=>{P="finished"},sample:b=>(V=0,St(b))};return O}function lx(e){let t;return()=>(t===void 0&&(t=e()),t)}const ax=lx(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ux=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ki=10,cx=2e4,dx=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Xp(t.ease);function fx(e,t,{onUpdate:n,onComplete:r,...i}){if(!(ax()&&ux.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,a,u,c=!1;const f=()=>{u=new Promise(g=>{a=g})};f();let{keyframes:d,duration:p=300,ease:m,times:x}=i;if(dx(t,i)){const g=go({...i,repeat:0,delay:0});let S={done:!1,value:d[0]};const j=[];let E=0;for(;!S.done&&E<cx;)S=g.sample(E),j.push(S.value),E+=ki;x=void 0,d=j,p=E-ki,m="linear"}const v=w0(e.owner.current,t,d,{...i,duration:p,ease:m,times:x}),w=()=>{c=!1,v.cancel()},y=()=>{c=!0,W.update(w),a(),f()};return v.onfinish=()=>{c||(e.set(j0(d,i)),r&&r(),y())},{then(g,S){return u.then(g,S)},attachTimeline(g){return v.timeline=g,v.onfinish=null,J},get time(){return pt(v.currentTime||0)},set time(g){v.currentTime=an(g)},get speed(){return v.playbackRate},set speed(g){v.playbackRate=g},get duration(){return pt(p)},play:()=>{s||(v.play(),xt(w))},pause:()=>v.pause(),stop:()=>{if(s=!0,v.playState==="idle")return;const{currentTime:g}=v;if(g){const S=go({...i,autoplay:!1});e.setWithVelocity(S.sample(g-ki).value,S.sample(g).value,ki)}y()},complete:()=>{c||v.finish()},cancel:y}}function px({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:J,pause:J,stop:J,then:o=>(o(),Promise.resolve()),cancel:J,complete:J});return t?go({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const hx={type:"spring",stiffness:500,damping:25,restSpeed:10},mx=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),gx={type:"keyframes",duration:.8},yx={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},xx=(e,{keyframes:t})=>t.length>2?gx:gn.has(e)?e.startsWith("scale")?mx(t[1]):hx:yx,kl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Wt.test(t)||t==="0")&&!t.startsWith("url(")),vx=new Set(["brightness","contrast","saturate","opacity"]);function Sx(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Io)||[];if(!r)return e;const i=n.replace(r,"");let o=vx.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const wx=/([a-z-]*)\(.*?\)/g,Pl={...Wt,getAnimatableNone:e=>{const t=e.match(wx);return t?t.map(Sx).join(" "):e}},jx={...Ap,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:Pl,WebkitFilter:Pl},Ua=e=>jx[e];function gh(e,t){let n=Ua(e);return n!==Pl&&(n=Wt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const yh=e=>/^0[^.\s]+$/.test(e);function Cx(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||yh(e)}function kx(e,t,n,r){const i=kl(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let a;const u=[];for(let c=0;c<o.length;c++)o[c]===null&&(o[c]=c===0?s:o[c-1]),Cx(o[c])&&u.push(c),typeof o[c]=="string"&&o[c]!=="none"&&o[c]!=="0"&&(a=o[c]);if(i&&u.length&&a)for(let c=0;c<u.length;c++){const f=u[c];o[f]=gh(t,a)}return o}function Px({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:a,from:u,elapsed:c,...f}){return!!Object.keys(f).length}function Ha(e,t){return e[t]||e.default||e}const Ex={skipAnimations:!1},$a=(e,t,n,r={})=>i=>{const o=Ha(r,e)||{},s=o.delay||r.delay||0;let{elapsed:a=0}=r;a=a-an(s);const u=kx(t,e,n,o),c=u[0],f=u[u.length-1],d=kl(e,c),p=kl(e,f);let m={keyframes:u,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-a,onUpdate:x=>{t.set(x),o.onUpdate&&o.onUpdate(x)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(Px(o)||(m={...m,...xx(e,m)}),m.duration&&(m.duration=an(m.duration)),m.repeatDelay&&(m.repeatDelay=an(m.repeatDelay)),!d||!p||S0.current||o.type===!1||Ex.skipAnimations)return px(m);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const x=fx(t,e,m);if(x)return x}return go(m)};function yo(e){return!!(Te(e)&&e.add)}const xh=e=>/^\-?\d*\.?\d+$/.test(e);function Xa(e,t){e.indexOf(t)===-1&&e.push(t)}function Ga(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Ka{constructor(){this.subscriptions=[]}add(t){return Xa(this.subscriptions,t),()=>Ga(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Tx=e=>!isNaN(parseFloat(e));class Rx{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=pe;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,W.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>W.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=Tx(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Ka);const r=this.events[t].add(n);return t==="change"?()=>{r(),W.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ph(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Qn(e,t){return new Rx(e,t)}const vh=e=>t=>t.test(e),Lx={test:e=>e==="auto",parse:e=>e},Sh=[yn,A,ot,jt,Oy,Vy,Lx],ar=e=>Sh.find(vh(e)),Ax=[...Sh,ye,Wt],Mx=e=>Ax.find(vh(e));function Dx(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Qn(n))}function bx(e,t){const n=_o(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const a=Yy(o[s]);Dx(e,s,a)}}function Vx(e,t,n){var r,i;const o=Object.keys(t).filter(a=>!e.hasValue(a)),s=o.length;if(s)for(let a=0;a<s;a++){const u=o[a],c=t[u];let f=null;Array.isArray(c)&&(f=c[0]),f===null&&(f=(i=(r=n[u])!==null&&r!==void 0?r:e.readValue(u))!==null&&i!==void 0?i:t[u]),f!=null&&(typeof f=="string"&&(xh(f)||yh(f))?f=parseFloat(f):!Mx(f)&&Wt.test(c)&&(f=gh(u,c)),e.addValue(u,Qn(f,{owner:e})),n[u]===void 0&&(n[u]=f),f!==null&&e.setBaseTarget(u,f))}}function Ox(e,t){return t?(t[e]||t.default||t).from:void 0}function Nx(e,t,n){const r={};for(const i in e){const o=Ox(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function zx({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Bx(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function wh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...a}=e.makeTargetAnimatable(t);const u=e.getValue("willChange");r&&(o=r);const c=[],f=i&&e.animationState&&e.animationState.getState()[i];for(const d in a){const p=e.getValue(d),m=a[d];if(!p||m===void 0||f&&zx(f,d))continue;const x={delay:n,elapsed:0,...Ha(o||{},d)};if(window.HandoffAppearAnimations){const y=e.getProps()[jp];if(y){const h=window.HandoffAppearAnimations(y,d,p,W);h!==null&&(x.elapsed=h,x.isHandoff=!0)}}let v=!x.isHandoff&&!Bx(p,m);if(x.type==="spring"&&(p.getVelocity()||x.velocity)&&(v=!1),p.animation&&(v=!1),v)continue;p.start($a(d,p,m,e.shouldReduceMotion&&gn.has(d)?{type:!1}:x));const w=p.animation;yo(u)&&(u.add(d),w.then(()=>u.remove(d))),c.push(w)}return s&&Promise.all(c).then(()=>{s&&bx(e,s)}),c}function El(e,t,n={}){const r=_o(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(wh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=i;return Ix(e,t,c+u,f,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[u,c]=a==="beforeChildren"?[o,s]:[s,o];return u().then(()=>c())}else return Promise.all([o(),s(n.delay)])}function Ix(e,t,n=0,r=0,i=1,o){const s=[],a=(e.variantChildren.size-1)*r,u=i===1?(c=0)=>c*r:(c=0)=>a-c*r;return Array.from(e.variantChildren).sort(Fx).forEach((c,f)=>{c.notify("AnimationStart",t),s.push(El(c,t,{...o,delay:n+u(f)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(s)}function Fx(e,t){return e.sortNodePosition(t)}function _x(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>El(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=El(e,t,n);else{const i=typeof t=="function"?_o(e,t,n.custom):t;r=Promise.all(wh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const Wx=[...Aa].reverse(),Ux=Aa.length;function Hx(e){return t=>Promise.all(t.map(({animation:n,options:r})=>_x(e,n,r)))}function $x(e){let t=Hx(e);const n=Gx();let r=!0;const i=(u,c)=>{const f=_o(e,c);if(f){const{transition:d,transitionEnd:p,...m}=f;u={...u,...m,...p}}return u};function o(u){t=u(e)}function s(u,c){const f=e.getProps(),d=e.getVariantContext(!0)||{},p=[],m=new Set;let x={},v=1/0;for(let y=0;y<Ux;y++){const h=Wx[y],g=n[h],S=f[h]!==void 0?f[h]:d[h],j=$r(S),E=h===c?g.isActive:null;E===!1&&(v=y);let P=S===d[h]&&S!==f[h]&&j;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),g.protectedKeys={...x},!g.isActive&&E===null||!S&&!g.prevProp||zo(S)||typeof S=="boolean")continue;let V=Xx(g.prevProp,S)||h===c&&g.isActive&&!P&&j||y>v&&j,D=!1;const re=Array.isArray(S)?S:[S];let le=re.reduce(i,{});E===!1&&(le={});const{prevResolvedValues:ge={}}=g,ie={...ge,...le},St=Y=>{V=!0,m.has(Y)&&(D=!0,m.delete(Y)),g.needsAnimating[Y]=!0};for(const Y in ie){const Ne=le[Y],T=ge[Y];if(x.hasOwnProperty(Y))continue;let M=!1;po(Ne)&&po(T)?M=!Hp(Ne,T):M=Ne!==T,M?Ne!==void 0?St(Y):m.add(Y):Ne!==void 0&&m.has(Y)?St(Y):g.protectedKeys[Y]=!0}g.prevProp=S,g.prevResolvedValues=le,g.isActive&&(x={...x,...le}),r&&e.blockInitialAnimation&&(V=!1),V&&(!P||D)&&p.push(...re.map(Y=>({animation:Y,options:{type:h,...u}})))}if(m.size){const y={};m.forEach(h=>{const g=e.getBaseTarget(h);g!==void 0&&(y[h]=g)}),p.push({animation:y})}let w=!!p.length;return r&&(f.initial===!1||f.initial===f.animate)&&!e.manuallyAnimateOnMount&&(w=!1),r=!1,w?t(p):Promise.resolve()}function a(u,c,f){var d;if(n[u].isActive===c)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(m=>{var x;return(x=m.animationState)===null||x===void 0?void 0:x.setActive(u,c)}),n[u].isActive=c;const p=s(f,u);for(const m in n)n[m].protectedKeys={};return p}return{animateChanges:s,setActive:a,setAnimateFunction:o,getState:()=>n}}function Xx(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Hp(t,e):!1}function Yt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Gx(){return{animate:Yt(!0),whileInView:Yt(),whileHover:Yt(),whileTap:Yt(),whileDrag:Yt(),whileFocus:Yt(),exit:Yt()}}class Kx extends Xt{constructor(t){super(t),t.animationState||(t.animationState=$x(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),zo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Qx=0;class Yx extends Xt{constructor(){super(...arguments),this.id=Qx++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Zx={animation:{Feature:Kx},exit:{Feature:Yx}},Sc=(e,t)=>Math.abs(e-t);function Jx(e,t){const n=Sc(e.x,t.x),r=Sc(e.y,t.y);return Math.sqrt(n**2+r**2)}class jh{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=ks(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,m=Jx(d.offset,{x:0,y:0})>=3;if(!p&&!m)return;const{point:x}=d,{timestamp:v}=pe;this.history.push({...x,timestamp:v});const{onStart:w,onMove:y}=this.handlers;p||(w&&w(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,d)},this.handlePointerMove=(d,p)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Cs(p,this.transformPagePoint),W.update(this.updatePoint,!0)},this.handlePointerUp=(d,p)=>{this.end();const{onEnd:m,onSessionEnd:x,resumeAnimation:v}=this.handlers;if(this.dragSnapToOrigin&&v&&v(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const w=ks(d.type==="pointercancel"?this.lastMoveEventInfo:Cs(p,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,w),x&&x(d,w)},!Ip(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=Fo(t),a=Cs(s,this.transformPagePoint),{point:u}=a,{timestamp:c}=pe;this.history=[{...u,timestamp:c}];const{onSessionStart:f}=n;f&&f(t,ks(a,this.history)),this.removeListeners=Bt(ft(this.contextWindow,"pointermove",this.handlePointerMove),ft(this.contextWindow,"pointerup",this.handlePointerUp),ft(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),xt(this.updatePoint)}}function Cs(e,t){return t?{point:t(e.point)}:e}function wc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ks({point:e},t){return{point:e,delta:wc(e,Ch(t)),offset:wc(e,qx(t)),velocity:ev(t,.1)}}function qx(e){return e[0]}function Ch(e){return e[e.length-1]}function ev(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Ch(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>an(t)));)n--;if(!r)return{x:0,y:0};const o=pt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function be(e){return e.max-e.min}function Tl(e,t=0,n=.01){return Math.abs(e-t)<=n}function jc(e,t,n,r=.5){e.origin=r,e.originPoint=X(t.min,t.max,e.origin),e.scale=be(n)/be(t),(Tl(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=X(n.min,n.max,e.origin)-e.originPoint,(Tl(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Er(e,t,n,r){jc(e.x,t.x,n.x,r?r.originX:void 0),jc(e.y,t.y,n.y,r?r.originY:void 0)}function Cc(e,t,n){e.min=n.min+t.min,e.max=e.min+be(t)}function tv(e,t,n){Cc(e.x,t.x,n.x),Cc(e.y,t.y,n.y)}function kc(e,t,n){e.min=t.min-n.min,e.max=e.min+be(t)}function Tr(e,t,n){kc(e.x,t.x,n.x),kc(e.y,t.y,n.y)}function nv(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?X(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?X(n,e,r.max):Math.min(e,n)),e}function Pc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function rv(e,{top:t,left:n,bottom:r,right:i}){return{x:Pc(e.x,n,i),y:Pc(e.y,t,r)}}function Ec(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function iv(e,t){return{x:Ec(e.x,t.x),y:Ec(e.y,t.y)}}function ov(e,t){let n=.5;const r=be(e),i=be(t);return i>r?n=Gr(t.min,t.max-r,e.min):r>i&&(n=Gr(e.min,e.max-i,t.min)),_t(0,1,n)}function sv(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Rl=.35;function lv(e=Rl){return e===!1?e=0:e===!0&&(e=Rl),{x:Tc(e,"left","right"),y:Tc(e,"top","bottom")}}function Tc(e,t,n){return{min:Rc(e,t),max:Rc(e,n)}}function Rc(e,t){return typeof e=="number"?e:e[t]||0}const Lc=()=>({translate:0,scale:1,origin:0,originPoint:0}),Vn=()=>({x:Lc(),y:Lc()}),Ac=()=>({min:0,max:0}),q=()=>({x:Ac(),y:Ac()});function Be(e){return[e("x"),e("y")]}function kh({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function av({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function uv(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ps(e){return e===void 0||e===1}function Ll({scale:e,scaleX:t,scaleY:n}){return!Ps(e)||!Ps(t)||!Ps(n)}function qt(e){return Ll(e)||Ph(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Ph(e){return Mc(e.x)||Mc(e.y)}function Mc(e){return e&&e!=="0%"}function xo(e,t,n){const r=e-n,i=t*r;return n+i}function Dc(e,t,n,r,i){return i!==void 0&&(e=xo(e,i,r)),xo(e,n,r)+t}function Al(e,t=0,n=1,r,i){e.min=Dc(e.min,t,n,r,i),e.max=Dc(e.max,t,n,r,i)}function Eh(e,{x:t,y:n}){Al(e.x,t.translate,t.scale,t.originPoint),Al(e.y,n.translate,n.scale,n.originPoint)}function cv(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let a=0;a<i;a++){o=n[a],s=o.projectionDelta;const u=o.instance;u&&u.style&&u.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&On(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Eh(e,s)),r&&qt(o.latestValues)&&On(e,o.latestValues))}t.x=bc(t.x),t.y=bc(t.y)}function bc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Pt(e,t){e.min=e.min+t,e.max=e.max+t}function Vc(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=X(e.min,e.max,o);Al(e,t[n],t[r],s,t.scale)}const dv=["x","scaleX","originX"],fv=["y","scaleY","originY"];function On(e,t){Vc(e.x,t,dv),Vc(e.y,t,fv)}function Th(e,t){return kh(uv(e.getBoundingClientRect(),t))}function pv(e,t,n){const r=Th(e,n),{scroll:i}=t;return i&&(Pt(r.x,i.offset.x),Pt(r.y,i.offset.y)),r}const Rh=({current:e})=>e?e.ownerDocument.defaultView:null,hv=new WeakMap;class mv{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=q(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=f=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(Fo(f,"page").point)},o=(f,d)=>{const{drag:p,dragPropagation:m,onDragStart:x}=this.getProps();if(p&&!m&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=_p(p),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Be(w=>{let y=this.getAxisMotionValue(w).get()||0;if(ot.test(y)){const{projection:h}=this.visualElement;if(h&&h.layout){const g=h.layout.layoutBox[w];g&&(y=be(g)*(parseFloat(y)/100))}}this.originPoint[w]=y}),x&&W.update(()=>x(f,d),!1,!0);const{animationState:v}=this.visualElement;v&&v.setActive("whileDrag",!0)},s=(f,d)=>{const{dragPropagation:p,dragDirectionLock:m,onDirectionLock:x,onDrag:v}=this.getProps();if(!p&&!this.openGlobalLock)return;const{offset:w}=d;if(m&&this.currentDirection===null){this.currentDirection=gv(w),this.currentDirection!==null&&x&&x(this.currentDirection);return}this.updateAxis("x",d.point,w),this.updateAxis("y",d.point,w),this.visualElement.render(),v&&v(f,d)},a=(f,d)=>this.stop(f,d),u=()=>Be(f=>{var d;return this.getAnimationState(f)==="paused"&&((d=this.getAxisMotionValue(f).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new jh(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:a,resumeAnimation:u},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Rh(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&W.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Pi(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=nv(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&Dn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=rv(i.layoutBox,n):this.constraints=!1,this.elastic=lv(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Be(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=sv(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Dn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=pv(r,i.root,this.visualElement.getTransformPagePoint());let s=iv(i.layout.layoutBox,o);if(n){const a=n(av(s));this.hasMutatedConstraints=!!a,a&&(s=kh(a))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),u=this.constraints||{},c=Be(f=>{if(!Pi(f,n,this.currentDirection))return;let d=u&&u[f]||{};s&&(d={min:0,max:0});const p=i?200:1e6,m=i?40:1e7,x={type:"inertia",velocity:r?t[f]:0,bounceStiffness:p,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...d};return this.startAxisValueAnimation(f,x)});return Promise.all(c).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start($a(t,r,0,n))}stopAnimation(){Be(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Be(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Be(n=>{const{drag:r}=this.getProps();if(!Pi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:a}=i.layout.layoutBox[n];o.set(t[n]-X(s,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Dn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Be(s=>{const a=this.getAxisMotionValue(s);if(a){const u=a.get();i[s]=ov({min:u,max:u},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Be(s=>{if(!Pi(s,t,null))return;const a=this.getAxisMotionValue(s),{min:u,max:c}=this.constraints[s];a.set(X(u,c,i[s]))})}addListeners(){if(!this.visualElement.current)return;hv.set(this.visualElement,this);const t=this.visualElement.current,n=ft(t,"pointerdown",u=>{const{drag:c,dragListener:f=!0}=this.getProps();c&&f&&this.start(u)}),r=()=>{const{dragConstraints:u}=this.getProps();Dn(u)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=ct(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:u,hasLayoutChanged:c})=>{this.isDragging&&c&&(Be(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=u[f].translate,d.set(d.get()+u[f].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Rl,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:a}}}function Pi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function gv(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class yv extends Xt{constructor(t){super(t),this.removeGroupControls=J,this.removeListeners=J,this.controls=new mv(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||J}unmount(){this.removeGroupControls(),this.removeListeners()}}const Oc=e=>(t,n)=>{e&&W.update(()=>e(t,n))};class xv extends Xt{constructor(){super(...arguments),this.removePointerDownListener=J}onPointerDown(t){this.session=new jh(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Rh(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Oc(t),onStart:Oc(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&W.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=ft(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function vv(){const e=L.useContext(Ra);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=L.useId();return L.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Ii={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Nc(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ur={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(A.test(e))e=parseFloat(e);else return e;const n=Nc(e,t.target.x),r=Nc(e,t.target.y);return`${n}% ${r}%`}},Sv={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Wt.parse(e);if(i.length>5)return r;const o=Wt.createTransformer(e),s=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,u=n.y.scale*t.y;i[0+s]/=a,i[1+s]/=u;const c=X(a,u,.5);return typeof i[2+s]=="number"&&(i[2+s]/=c),typeof i[3+s]=="number"&&(i[3+s]/=c),o(i)}};class wv extends zl.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Ty(jv),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ii.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||W.postRender(()=>{const a=s.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Lh(e){const[t,n]=vv(),r=L.useContext(kp);return zl.createElement(wv,{...e,layoutGroup:r,switchLayoutGroup:L.useContext(Pp),isPresent:t,safeToRemove:n})}const jv={borderRadius:{...ur,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ur,borderTopRightRadius:ur,borderBottomLeftRadius:ur,borderBottomRightRadius:ur,boxShadow:Sv},Ah=["TopLeft","TopRight","BottomLeft","BottomRight"],Cv=Ah.length,zc=e=>typeof e=="string"?parseFloat(e):e,Bc=e=>typeof e=="number"||A.test(e);function kv(e,t,n,r,i,o){i?(e.opacity=X(0,n.opacity!==void 0?n.opacity:1,Pv(r)),e.opacityExit=X(t.opacity!==void 0?t.opacity:1,0,Ev(r))):o&&(e.opacity=X(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<Cv;s++){const a=`border${Ah[s]}Radius`;let u=Ic(t,a),c=Ic(n,a);if(u===void 0&&c===void 0)continue;u||(u=0),c||(c=0),u===0||c===0||Bc(u)===Bc(c)?(e[a]=Math.max(X(zc(u),zc(c),r),0),(ot.test(c)||ot.test(u))&&(e[a]+="%")):e[a]=c}(t.rotate||n.rotate)&&(e.rotate=X(t.rotate||0,n.rotate||0,r))}function Ic(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Pv=Mh(0,.5,qp),Ev=Mh(.5,.95,J);function Mh(e,t,n){return r=>r<e?0:r>t?1:n(Gr(e,t,r))}function Fc(e,t){e.min=t.min,e.max=t.max}function ze(e,t){Fc(e.x,t.x),Fc(e.y,t.y)}function _c(e,t,n,r,i){return e-=t,e=xo(e,1/n,r),i!==void 0&&(e=xo(e,1/i,r)),e}function Tv(e,t=0,n=1,r=.5,i,o=e,s=e){if(ot.test(t)&&(t=parseFloat(t),t=X(s.min,s.max,t/100)-s.min),typeof t!="number")return;let a=X(o.min,o.max,r);e===o&&(a-=t),e.min=_c(e.min,t,n,a,i),e.max=_c(e.max,t,n,a,i)}function Wc(e,t,[n,r,i],o,s){Tv(e,t[n],t[r],t[i],t.scale,o,s)}const Rv=["x","scaleX","originX"],Lv=["y","scaleY","originY"];function Uc(e,t,n,r){Wc(e.x,t,Rv,n?n.x:void 0,r?r.x:void 0),Wc(e.y,t,Lv,n?n.y:void 0,r?r.y:void 0)}function Hc(e){return e.translate===0&&e.scale===1}function Dh(e){return Hc(e.x)&&Hc(e.y)}function Av(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function bh(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function $c(e){return be(e.x)/be(e.y)}class Mv{constructor(){this.members=[]}add(t){Xa(this.members,t),t.scheduleRender()}remove(t){if(Ga(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Xc(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:u,rotateX:c,rotateY:f}=n;u&&(r+=`rotate(${u}deg) `),c&&(r+=`rotateX(${c}deg) `),f&&(r+=`rotateY(${f}deg) `)}const s=e.x.scale*t.x,a=e.y.scale*t.y;return(s!==1||a!==1)&&(r+=`scale(${s}, ${a})`),r||"none"}const Dv=(e,t)=>e.depth-t.depth;class bv{constructor(){this.children=[],this.isDirty=!1}add(t){Xa(this.children,t),this.isDirty=!0}remove(t){Ga(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Dv),this.isDirty=!1,this.children.forEach(t)}}function Vv(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(xt(r),e(o-t))};return W.read(r,!0),()=>xt(r)}function Ov(e){window.MotionDebug&&window.MotionDebug.record(e)}function Nv(e){return e instanceof SVGElement&&e.tagName!=="svg"}function zv(e,t,n){const r=Te(e)?e:Qn(e);return r.start($a("",r,t,n)),r.animation}const Gc=["","X","Y","Z"],Bv={visibility:"hidden"},Kc=1e3;let Iv=0;const en={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Vh({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},a=t==null?void 0:t()){this.id=Iv++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,en.totalNodes=en.resolvedTargetDeltas=en.recalculatedProjection=0,this.nodes.forEach(Wv),this.nodes.forEach(Gv),this.nodes.forEach(Kv),this.nodes.forEach(Uv),Ov(en)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let u=0;u<this.path.length;u++)this.path[u].shouldResetTransform=!0;this.root===this&&(this.nodes=new bv)}addEventListener(s,a){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Ka),this.eventHandlers.get(s).add(a)}notifyListeners(s,...a){const u=this.eventHandlers.get(s);u&&u.notify(...a)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Nv(s),this.instance=s;const{layoutId:u,layout:c,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(c||u)&&(this.isLayoutDirty=!0),e){let d;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=Vv(p,250),Ii.hasAnimatedSinceResize&&(Ii.hasAnimatedSinceResize=!1,this.nodes.forEach(Yc))})}u&&this.root.registerSharedNode(u,this),this.options.animate!==!1&&f&&(u||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:p,hasRelativeTargetChanged:m,layout:x})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const v=this.options.transition||f.getDefaultTransition()||qv,{onLayoutAnimationStart:w,onLayoutAnimationComplete:y}=f.getProps(),h=!this.targetLayout||!bh(this.targetLayout,x)||m,g=!p&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||p&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,g);const S={...Ha(v,"layout"),onPlay:w,onComplete:y};(f.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else p||Yc(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=x})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,xt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Qv),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let f=0;f<this.path.length;f++){const d=this.path[f];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:u}=this.options;if(a===void 0&&!u)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Qc);return}this.isUpdating||this.nodes.forEach($v),this.isUpdating=!1,this.nodes.forEach(Xv),this.nodes.forEach(Fv),this.nodes.forEach(_v),this.clearAllSnapshots();const a=performance.now();pe.delta=_t(0,1e3/60,a-pe.timestamp),pe.timestamp=a,pe.isProcessing=!0,ms.update.process(pe),ms.preRender.process(pe),ms.render.process(pe),pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Hv),this.sharedNodes.forEach(Yv)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,W.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){W.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let u=0;u<this.path.length;u++)this.path[u].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=q(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!Dh(this.projectionDelta),u=this.getTransformTemplate(),c=u?u(this.latestValues,""):void 0,f=c!==this.prevTransformTemplateValue;s&&(a||qt(this.latestValues)||f)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const a=this.measurePageBox();let u=this.removeElementScroll(a);return s&&(u=this.removeTransform(u)),e1(u),{animationId:this.root.animationId,measuredBox:a,layoutBox:u,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return q();const a=s.measureViewportBox(),{scroll:u}=this.root;return u&&(Pt(a.x,u.offset.x),Pt(a.y,u.offset.y)),a}removeElementScroll(s){const a=q();ze(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:f,options:d}=c;if(c!==this.root&&f&&d.layoutScroll){if(f.isRoot){ze(a,s);const{scroll:p}=this.root;p&&(Pt(a.x,-p.offset.x),Pt(a.y,-p.offset.y))}Pt(a.x,f.offset.x),Pt(a.y,f.offset.y)}}return a}applyTransform(s,a=!1){const u=q();ze(u,s);for(let c=0;c<this.path.length;c++){const f=this.path[c];!a&&f.options.layoutScroll&&f.scroll&&f!==f.root&&On(u,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),qt(f.latestValues)&&On(u,f.latestValues)}return qt(this.latestValues)&&On(u,this.latestValues),u}removeTransform(s){const a=q();ze(a,s);for(let u=0;u<this.path.length;u++){const c=this.path[u];if(!c.instance||!qt(c.latestValues))continue;Ll(c.latestValues)&&c.updateSnapshot();const f=q(),d=c.measurePageBox();ze(f,d),Uc(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,f)}return qt(this.latestValues)&&Uc(a,this.latestValues),a}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var a;const u=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=u.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=u.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=u.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==u;if(!(s||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:p}=this.options;if(!(!this.layout||!(d||p))){if(this.resolvedRelativeTargetAt=pe.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=q(),this.relativeTargetOrigin=q(),Tr(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=q(),this.targetWithTransforms=q()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),tv(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ze(this.target,this.layout.layoutBox),Eh(this.target,this.targetDelta)):ze(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=q(),this.relativeTargetOrigin=q(),Tr(this.relativeTargetOrigin,this.target,m.target),ze(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}en.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ll(this.parent.latestValues)||Ph(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const a=this.getLead(),u=!!this.resumingFrom||this!==a;let c=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(c=!1),u&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===pe.timestamp&&(c=!1),c)return;const{layout:f,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||d))return;ze(this.layoutCorrected,this.layout.layoutBox);const p=this.treeScale.x,m=this.treeScale.y;cv(this.layoutCorrected,this.treeScale,this.path,u),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:x}=a;if(!x){this.projectionTransform&&(this.projectionDelta=Vn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Vn(),this.projectionDeltaWithTransform=Vn());const v=this.projectionTransform;Er(this.projectionDelta,this.layoutCorrected,x,this.latestValues),this.projectionTransform=Xc(this.projectionDelta,this.treeScale),(this.projectionTransform!==v||this.treeScale.x!==p||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",x)),en.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,a=!1){const u=this.snapshot,c=u?u.latestValues:{},f={...this.latestValues},d=Vn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const p=q(),m=u?u.source:void 0,x=this.layout?this.layout.source:void 0,v=m!==x,w=this.getStack(),y=!w||w.members.length<=1,h=!!(v&&!y&&this.options.crossfade===!0&&!this.path.some(Jv));this.animationProgress=0;let g;this.mixTargetDelta=S=>{const j=S/1e3;Zc(d.x,s.x,j),Zc(d.y,s.y,j),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Tr(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Zv(this.relativeTarget,this.relativeTargetOrigin,p,j),g&&Av(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=q()),ze(g,this.relativeTarget)),v&&(this.animationValues=f,kv(f,c,this.latestValues,j,h,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=j},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(xt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=W.update(()=>{Ii.hasAnimatedSinceResize=!0,this.currentAnimation=zv(0,Kc,{...s,onUpdate:a=>{this.mixTargetDelta(a),s.onUpdate&&s.onUpdate(a)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Kc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:a,target:u,layout:c,latestValues:f}=s;if(!(!a||!u||!c)){if(this!==s&&this.layout&&c&&Oh(this.options.animationType,this.layout.layoutBox,c.layoutBox)){u=this.target||q();const d=be(this.layout.layoutBox.x);u.x.min=s.target.x.min,u.x.max=u.x.min+d;const p=be(this.layout.layoutBox.y);u.y.min=s.target.y.min,u.y.max=u.y.min+p}ze(a,u),On(a,f),Er(this.projectionDeltaWithTransform,this.layoutCorrected,a,f)}}registerSharedNode(s,a){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Mv),this.sharedNodes.get(s).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:a}=this.options;return a?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:a}=this.options;return a?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:a,preserveFollowOpacity:u}={}){const c=this.getStack();c&&c.promote(this,u),s&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let a=!1;const{latestValues:u}=s;if((u.rotate||u.rotateX||u.rotateY||u.rotateZ)&&(a=!0),!a)return;const c={};for(let f=0;f<Gc.length;f++){const d="rotate"+Gc[f];u[d]&&(c[d]=u[d],s.setStaticValue(d,0))}s.render();for(const f in c)s.setStaticValue(f,c[f]);s.scheduleRender()}getProjectionStyles(s){var a,u;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Bv;const c={visibility:""},f=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=Bi(s==null?void 0:s.pointerEvents)||"",c.transform=f?f(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const v={};return this.options.layoutId&&(v.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,v.pointerEvents=Bi(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!qt(this.latestValues)&&(v.transform=f?f({},""):"none",this.hasProjected=!1),v}const p=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=Xc(this.projectionDeltaWithTransform,this.treeScale,p),f&&(c.transform=f(p,c.transform));const{x:m,y:x}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${x.origin*100}% 0`,d.animationValues?c.opacity=d===this?(u=(a=p.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&u!==void 0?u:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:c.opacity=d===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const v in co){if(p[v]===void 0)continue;const{correct:w,applyTo:y}=co[v],h=c.transform==="none"?p[v]:w(p[v],d);if(y){const g=y.length;for(let S=0;S<g;S++)c[y[S]]=h}else c[v]=h}return this.options.layoutId&&(c.pointerEvents=d===this?Bi(s==null?void 0:s.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var a;return(a=s.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Qc),this.root.sharedNodes.clear()}}}function Fv(e){e.updateLayout()}function _v(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?Be(d=>{const p=s?n.measuredBox[d]:n.layoutBox[d],m=be(p);p.min=r[d].min,p.max=p.min+m}):Oh(o,n.layoutBox,r)&&Be(d=>{const p=s?n.measuredBox[d]:n.layoutBox[d],m=be(r[d]);p.max=p.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+m)});const a=Vn();Er(a,r,n.layoutBox);const u=Vn();s?Er(u,e.applyTransform(i,!0),n.measuredBox):Er(u,r,n.layoutBox);const c=!Dh(a);let f=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:p,layout:m}=d;if(p&&m){const x=q();Tr(x,n.layoutBox,p.layoutBox);const v=q();Tr(v,r,m.layoutBox),bh(x,v)||(f=!0),d.options.layoutRoot&&(e.relativeTarget=v,e.relativeTargetOrigin=x,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:u,layoutDelta:a,hasLayoutChanged:c,hasRelativeTargetChanged:f})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Wv(e){en.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Uv(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Hv(e){e.clearSnapshot()}function Qc(e){e.clearMeasurements()}function $v(e){e.isLayoutDirty=!1}function Xv(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Yc(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Gv(e){e.resolveTargetDelta()}function Kv(e){e.calcProjection()}function Qv(e){e.resetRotation()}function Yv(e){e.removeLeadSnapshot()}function Zc(e,t,n){e.translate=X(t.translate,0,n),e.scale=X(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Jc(e,t,n,r){e.min=X(t.min,n.min,r),e.max=X(t.max,n.max,r)}function Zv(e,t,n,r){Jc(e.x,t.x,n.x,r),Jc(e.y,t.y,n.y,r)}function Jv(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const qv={duration:.45,ease:[.4,0,.1,1]},qc=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),ed=qc("applewebkit/")&&!qc("chrome/")?Math.round:J;function td(e){e.min=ed(e.min),e.max=ed(e.max)}function e1(e){td(e.x),td(e.y)}function Oh(e,t,n){return e==="position"||e==="preserve-aspect"&&!Tl($c(t),$c(n),.2)}const t1=Vh({attachResizeListener:(e,t)=>ct(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Es={current:void 0},Nh=Vh({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Es.current){const e=new t1({});e.mount(window),e.setOptions({layoutScroll:!0}),Es.current=e}return Es.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),n1={pan:{Feature:xv},drag:{Feature:yv,ProjectionNode:Nh,MeasureLayout:Lh}},r1=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function i1(e){const t=r1.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Ml(e,t,n=1){const[r,i]=i1(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return xh(s)?parseFloat(s):s}else return Sl(i)?Ml(i,t,n+1):i}function o1(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!Sl(o))return;const s=Ml(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!Sl(o))continue;const s=Ml(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const s1=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),zh=e=>s1.has(e),l1=e=>Object.keys(e).some(zh),nd=e=>e===yn||e===A,rd=(e,t)=>parseFloat(e.split(", ")[t]),id=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return rd(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?rd(o[1],e):0}},a1=new Set(["x","y","z"]),u1=ei.filter(e=>!a1.has(e));function c1(e){const t=[];return u1.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Yn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:id(4,13),y:id(5,14)};Yn.translateX=Yn.x;Yn.translateY=Yn.y;const d1=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,a={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(c=>{a[c]=Yn[c](r,o)}),t.render();const u=t.measureViewportBox();return n.forEach(c=>{const f=t.getValue(c);f&&f.jump(a[c]),e[c]=Yn[c](u,o)}),e},f1=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(zh);let o=[],s=!1;const a=[];if(i.forEach(u=>{const c=e.getValue(u);if(!e.hasValue(u))return;let f=n[u],d=ar(f);const p=t[u];let m;if(po(p)){const x=p.length,v=p[0]===null?1:0;f=p[v],d=ar(f);for(let w=v;w<x&&p[w]!==null;w++)m?Ia(ar(p[w])===m):m=ar(p[w])}else m=ar(p);if(d!==m)if(nd(d)&&nd(m)){const x=c.get();typeof x=="string"&&c.set(parseFloat(x)),typeof p=="string"?t[u]=parseFloat(p):Array.isArray(p)&&m===A&&(t[u]=p.map(parseFloat))}else d!=null&&d.transform&&(m!=null&&m.transform)&&(f===0||p===0)?f===0?c.set(m.transform(f)):t[u]=d.transform(p):(s||(o=c1(e),s=!0),a.push(u),r[u]=r[u]!==void 0?r[u]:t[u],c.jump(p))}),a.length){const u=a.indexOf("height")>=0?window.pageYOffset:null,c=d1(t,e,a);return o.length&&o.forEach(([f,d])=>{e.getValue(f).set(d)}),e.render(),No&&u!==null&&window.scrollTo({top:u}),{target:c,transitionEnd:r}}else return{target:t,transitionEnd:r}};function p1(e,t,n,r){return l1(t)?f1(e,t,n,r):{target:t,transitionEnd:r}}const h1=(e,t,n,r)=>{const i=o1(e,t,r);return t=i.target,r=i.transitionEnd,p1(e,t,n,r)},Dl={current:null},Bh={current:!1};function m1(){if(Bh.current=!0,!!No)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Dl.current=e.matches;e.addListener(t),t()}else Dl.current=!1}function g1(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Te(o))e.addValue(i,o),yo(r)&&r.add(i);else if(Te(s))e.addValue(i,Qn(o,{owner:e})),yo(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(o)}else{const a=e.getStaticValue(i);e.addValue(i,Qn(a!==void 0?a:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const od=new WeakMap,Ih=Object.keys(Xr),y1=Ih.length,sd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],x1=Ma.length;class v1{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>W.render(this.render,!1,!0);const{latestValues:a,renderState:u}=o;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=Bo(n),this.isVariantNode=Cp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...f}=this.scrapeMotionValuesFromProps(n,{});for(const d in f){const p=f[d];a[d]!==void 0&&Te(p)&&(p.set(a[d],!1),yo(c)&&c.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,od.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Bh.current||m1(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Dl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){od.delete(this.current),this.projection&&this.projection.unmount(),xt(this.notifyUpdate),xt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=gn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&W.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,a;for(let u=0;u<y1;u++){const c=Ih[u],{isEnabled:f,Feature:d,ProjectionNode:p,MeasureLayout:m}=Xr[c];p&&(s=p),f(n)&&(!this.features[c]&&d&&(this.features[c]=new d(this)),m&&(a=m))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:u,layout:c,drag:f,dragConstraints:d,layoutScroll:p,layoutRoot:m}=n;this.projection.setOptions({layoutId:u,layout:c,alwaysMeasureLayout:!!f||d&&Dn(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:o,layoutScroll:p,layoutRoot:m})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):q()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<sd.length;r++){const i=sd[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=g1(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<x1;r++){const i=Ma[r],o=this.props[i];($r(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Qn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Ba(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Te(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Ka),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Fh extends v1{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=Nx(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){Vx(this,r,s);const a=h1(this,r,s,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function S1(e){return window.getComputedStyle(e)}class w1 extends Fh{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(gn.has(n)){const r=Ua(n);return r&&r.default||0}else{const r=S1(t),i=(Rp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Th(t,n)}build(t,n,r,i){ba(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return za(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Te(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){Vp(t,n,r,i)}}class j1 extends Fh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(gn.has(n)){const r=Ua(n);return r&&r.default||0}return n=Op.has(n)?n:La(n),t.getAttribute(n)}measureInstanceViewportBox(){return q()}scrapeMotionValuesFromProps(t,n){return zp(t,n)}build(t,n,r,i){Oa(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){Np(t,n,r,i)}mount(t){this.isSVGTag=Na(t.tagName),super.mount(t)}}const C1=(e,t)=>Da(e)?new j1(t,{enableHardwareAcceleration:!1}):new w1(t,{enableHardwareAcceleration:!0}),k1={layout:{ProjectionNode:Nh,MeasureLayout:Lh}},P1={...Zx,...y0,...n1,...k1},Kr=Py((e,t)=>i0(e,t,P1,C1)),We={BEE_DIRECTIONS:["N","NE","E","SE","S","SW","W","NW"],PHEROMONE_LEVELS:[1,2,3,4,5],WEB_STRUCTURE:{center:[2,2],rings:[[[2,2]],[[1,1],[1,2],[1,3],[2,1],[2,3],[3,1],[3,2],[3,3]],[[0,0],[0,1],[0,2],[0,3],[0,4],[1,0],[1,4],[2,0],[2,4],[3,0],[3,4],[4,0],[4,1],[4,2],[4,3],[4,4]]]},letterToBeeAngle:e=>(e.charCodeAt(0)-65)*45%360,letterToPheromone:e=>(e.charCodeAt(0)-65)%5+1,getWebPosition:e=>{const t=We.WEB_STRUCTURE.rings;let n=0;for(let r=0;r<t.length;r++){if(e<n+t[r].length)return{ring:r,position:t[r][e-n],isCenter:r===0};n+=t[r].length}return{ring:2,position:[e%5,Math.floor(e/5)],isCenter:!1}},encryptWithSpiderOnly:(e,t)=>{const n=t*t,r=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(n,"X").slice(0,n);let i=Array.from({length:t},()=>Array(t).fill("")),o=0;for(let s=0;s<t;s++)for(let a=0;a<t;a++)o<r.length&&(i[s][a]=r[o++]);return We.readSpiderWebPattern(i,t)},encryptWithBeeOnly:(e,t)=>{const n=t*t,r=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(n,"X").slice(0,n);let i=[];for(let o=0;o<r.length;o++)i.push({letter:r[o],angle:We.letterToBeeAngle(r[o]),originalIndex:o});return i.sort((o,s)=>o.angle-s.angle),i.map(o=>o.letter).join("")},encryptWithAntOnly:(e,t)=>{const n=t*t,r=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(n,"X").slice(0,n);let i=[];for(let o=0;o<r.length;o++)i.push({letter:r[o],pheromone:We.letterToPheromone(r[o]),originalIndex:o});return i.sort((o,s)=>o.pheromone-s.pheromone||o.originalIndex-s.originalIndex),i.map(o=>o.letter).join("")},readSpiderWebPattern:(e,t)=>{let n="";const r=Math.floor(t/2);let i=Array.from({length:t},()=>Array(t).fill(!1));e[r][r]&&(n+=e[r][r],i[r][r]=!0);for(let o=1;o<=r;o++){let s=[];for(let a=0;a<t;a++)for(let u=0;u<t;u++)Math.max(Math.abs(a-r),Math.abs(u-r))===o&&!i[a][u]&&s.push([a,u]);s.sort((a,u)=>{const[c,f]=a,[d,p]=u,m=Math.atan2(c-r,f-r),x=Math.atan2(d-r,p-r);return m-x});for(let[a,u]of s)e[a][u]&&(n+=e[a][u],i[a][u]=!0)}return n.replace(/X+$/,"")}};function E1({message:e,gridSize:t,cipherType:n}){const[r,i]=L.useState([]),[o,s]=L.useState([]);L.useEffect(()=>{const u=t*t,c=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(u,"X").slice(0,u);let f=Array(u).fill(""),d=[];for(let p=0;p<c.length;p++){const m=c[p],x=Math.floor(p/t),v=p%t,w=x*t+v,y=We.letterToBeeAngle(m),h=We.letterToPheromone(m);f[w]=m,d.push({letter:m,beeAngle:y,pheromoneLevel:h,gridIndex:w,row:x,col:v})}i(f),s(d)},[e,t,n]);const a=u=>{const c=o.find(S=>S.gridIndex===u),f=r[u]&&r[u]!=="",d=Math.floor(u/t),p=u%t,m=Math.floor(t/2),x=d===m&&p===m,v=Math.abs(d-m)+Math.abs(p-m);let w="#fef3c7",y="#f59e0b",h="2px";if((n==="spider"||n==="combined")&&(x?(w="#dc2626",y="#991b1b",h="3px"):v===1&&(w="#16a34a",y="#15803d")),(n==="bee"||n==="combined")&&f&&c&&(w=`rgba(251, 191, 36, ${.3+c.beeAngle/360*.7})`,y="#f59e0b"),(n==="ant"||n==="combined")&&f&&c){const S=c.pheromoneLevel/5;n==="ant"?(w=`rgba(34, 197, 94, ${S})`,y="#16a34a"):n==="combined"&&!x&&v>1&&(w=`rgba(34, 197, 94, ${S})`)}const g=Math.max(30,Math.min(60,300/t));return{width:`${g}px`,height:`${g}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`${h} solid ${y}`,borderRadius:x&&(n==="spider"||n==="combined")?"50%":"8px",backgroundColor:w,color:x&&(n==="spider"||n==="combined")?"white":"#92400e",fontWeight:"bold",fontSize:`${Math.max(10,g/3)}px`,position:"relative"}};return l.jsxs("div",{children:[l.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:r.map((u,c)=>{const f=o.find(d=>d.gridIndex===c);return l.jsxs(Kr.div,{style:a(c),initial:{scale:0,rotate:f?f.beeAngle:0},animate:{scale:1,rotate:0},transition:{delay:c*.1,type:"spring",stiffness:200,damping:10},title:f?`🐝 Angle: ${f.beeAngle}° | 🐜 Pheromone: ${f.pheromoneLevel}`:"",children:[u,f&&l.jsx("div",{style:{position:"absolute",top:"-8px",right:"-8px",width:"16px",height:"16px",borderRadius:"50%",backgroundColor:"#dc2626",color:"white",fontSize:"10px",display:"flex",alignItems:"center",justifyContent:"center"},children:f.pheromoneLevel})]},c)})}),l.jsxs("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#f9fafb",borderRadius:"8px",fontSize:"12px"},children:[l.jsxs("div",{style:{fontWeight:"bold",marginBottom:"8px",color:"#374151"},children:["Active Cipher: ",n==="combined"?"All Three Combined":n==="spider"?"Spider Web Only":n==="bee"?"Bee Dance Only":"Ant Trails Only"]}),(n==="spider"||n==="combined")&&l.jsxs("div",{children:[l.jsx("strong",{children:"🕷️ Spider Web:"})," Red center, green inner ring - reads center outward"]}),(n==="bee"||n==="combined")&&l.jsxs("div",{children:[l.jsx("strong",{children:"🐝 Bee Dance:"})," Rotation angle encodes letter direction (A=0°, B=45°, etc.)"]}),(n==="ant"||n==="combined")&&l.jsxs("div",{children:[l.jsx("strong",{children:"🐜 Ant Trails:"})," Pheromone strength (1-5) shown in opacity and red circles"]})]})]})}function T1({message:e,gridSize:t,cipherType:n}){const[r,i]=L.useState([]),[o,s]=L.useState("");return L.useEffect(()=>{const a=e.toUpperCase().replace(/[^A-Z]/g,""),u=t*t;if(a.length===0){i(Array(u).fill("")),s("");return}let c="",f=Array(u).fill("");if(n==="spider"||n==="combined"){let d=Array.from({length:t},()=>Array(t).fill("")),p=0;const m=Math.floor(t/2);p<a.length&&(d[m][m]=a[p++]);for(let x=1;x<=m;x++){let v=[];for(let w=0;w<t;w++)for(let y=0;y<t;y++)Math.max(Math.abs(w-m),Math.abs(y-m))===x&&v.push([w,y]);v.sort((w,y)=>{const[h,g]=w,[S,j]=y,E=Math.atan2(h-m,g-m),P=Math.atan2(S-m,j-m);return E-P});for(let[w,y]of v)p<a.length&&(d[w][y]=a[p++])}for(let x=0;x<t;x++)for(let v=0;v<t;v++){const w=d[x][v]||"",y=x*t+v;f[y]=w,w&&(c+=w)}}else if(n==="bee"){let d=[];for(let p=0;p<a.length;p++)d.push({letter:a[p],angle:We.letterToBeeAngle(a[p]),encryptedIndex:p});d.sort((p,m)=>p.encryptedIndex-m.encryptedIndex);for(let p=0;p<d.length&&p<u;p++)f[p]=d[p].letter,c+=d[p].letter}else if(n==="ant"){let d=[];for(let p=0;p<a.length;p++)d.push({letter:a[p],pheromone:We.letterToPheromone(a[p]),encryptedIndex:p});d.sort((p,m)=>p.encryptedIndex-m.encryptedIndex);for(let p=0;p<d.length&&p<u;p++)f[p]=d[p].letter,c+=d[p].letter}i(f),s(c.replace(/X+$/,""))},[e,t,n]),l.jsxs("div",{children:[l.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:r.map((a,u)=>{const c=Math.floor(u/t),f=u%t,d=Math.floor(t/2),p=c===d&&f===d,m=Math.abs(c-d)+Math.abs(f-d),x=Math.max(30,Math.min(60,300/t));let v="#dbeafe",w="#3b82f6";return p?(v="#7c3aed",w="#5b21b6"):m===1&&(v="#059669",w="#047857"),l.jsx(Kr.div,{style:{width:`${x}px`,height:`${x}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`2px solid ${w}`,borderRadius:p?"50%":"8px",backgroundColor:v,color:p||m===1?"white":"#1e40af",fontWeight:"bold",fontSize:`${Math.max(10,x/3)}px`},initial:{scale:0,rotate:180},animate:{scale:1,rotate:0},transition:{delay:u*.08,type:"spring",stiffness:150,damping:12},children:a},u)})}),o&&l.jsx(Kr.div,{style:{marginTop:"20px",padding:"15px",backgroundColor:"#dcfce7",border:"2px solid #16a34a",borderRadius:"8px",textAlign:"center"},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2.5},children:l.jsxs("strong",{style:{color:"#15803d"},children:["Decrypted Message: ",o]})})]})}function R1({isVisible:e,onToggle:t}){return e?l.jsxs(Kr.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},style:{backgroundColor:"#f0fdf4",border:"2px solid #16a34a",borderRadius:"12px",padding:"20px",marginBottom:"30px",fontSize:"14px",lineHeight:"1.6"},children:[l.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[l.jsx("h3",{style:{color:"#15803d",margin:0},children:"📝 Manual Pen & Paper Guide"}),l.jsx("button",{onClick:t,style:{padding:"5px 10px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"✕ Close"})]}),l.jsxs("div",{style:{display:"grid",gap:"25px"},children:[l.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[l.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"📋 MATERIALS NEEDED"}),l.jsxs("ul",{style:{marginLeft:"20px"},children:[l.jsx("li",{children:"✏️ Pencil and eraser"}),l.jsx("li",{children:"📄 Graph paper or ruled paper"}),l.jsx("li",{children:"📐 Ruler (optional, for neat grids)"}),l.jsx("li",{children:"🎨 Colored pens/pencils (optional, for visual coding)"})]})]}),l.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[l.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"🔐 MANUAL ENCRYPTION STEPS"}),l.jsxs("div",{style:{marginLeft:"10px"},children:[l.jsx("p",{children:l.jsx("strong",{children:"Step 1: Prepare Your Message"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsxs("p",{style:{margin:0},children:["✍️ ",l.jsx("strong",{children:"Example:"}),' "MEET AT NOON"']}),l.jsxs("p",{style:{margin:0},children:["🧹 ",l.jsx("strong",{children:"Clean:"}),' Remove spaces → "MEETATNOON"']}),l.jsxs("p",{style:{margin:0},children:["📏 ",l.jsx("strong",{children:"Count:"})," 10 letters → Need at least 4×4 grid (16 spaces)"]}),l.jsxs("p",{style:{margin:0},children:["➕ ",l.jsx("strong",{children:"Pad:"}),' "MEETATNOONXXXXXX" (16 letters total)']})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 2: Draw Your Grid"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0"},children:"📐 Draw a 4×4 grid (or your chosen size):"}),l.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",l.jsx("br",{}),"│   │   │   │   │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│   │   │   │   │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│   │   │   │   │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│   │   │   │   │",l.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 3: Fill Grid Row by Row"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0"},children:"✍️ Write letters left to right, top to bottom:"}),l.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",l.jsx("br",{}),"│ M │ E │ E │ T │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ A │ T │ N │ O │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ O │ N │ X │ X │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ X │ X │ X │ X │",l.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 4: Mark the Spider Web Pattern"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0"},children:"🕷️ Number the cells in spider web order (center outward):"}),l.jsxs("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",l.jsx("br",{}),"│ 9 │ 2 │ 3 │10 │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ 8 │ 1 │ 4 │11 │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ 7 │ 6 │ 5 │12 │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│16 │15 │14 │13 │",l.jsx("br",{}),"└───┴───┴───┴───┘"]}),l.jsxs("p",{style:{margin:"10px 0 0 0",fontSize:"12px"},children:["🎯 ",l.jsx("strong",{children:"Pattern:"})," Start at center (1), then spiral outward clockwise"]})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 5: Read in Spider Web Order"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0"},children:"📖 Follow the numbers to read letters:"}),l.jsx("p",{style:{margin:0,fontFamily:"monospace"},children:"1→T, 2→E, 3→E, 4→N, 5→X, 6→N, 7→O, 8→A, 9→M, 10→T, 11→O, 12→X, 13→X, 14→X, 15→X, 16→X"}),l.jsxs("p",{style:{margin:"10px 0 0 0",fontWeight:"bold",color:"#dc2626"},children:["🔐 ",l.jsx("strong",{children:"Encrypted Result:"})," TEENXNOAMTOXXX"]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#dbeafe",padding:"15px",borderRadius:"8px",border:"1px solid #3b82f6"},children:[l.jsx("h4",{style:{color:"#1e40af",marginTop:0},children:"🔓 MANUAL DECRYPTION STEPS"}),l.jsxs("div",{style:{marginLeft:"10px"},children:[l.jsx("p",{children:l.jsx("strong",{children:"Step 1: Prepare the Encrypted Message"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsxs("p",{style:{margin:0},children:["📝 ",l.jsx("strong",{children:"Given:"}),' "TEENXNOAMTOXXX"']}),l.jsxs("p",{style:{margin:0},children:["📏 ",l.jsx("strong",{children:"Count:"})," 14 letters → Use 4×4 grid (16 spaces)"]})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 2: Draw Empty Grid with Spider Numbers"})}),l.jsx("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:l.jsxs("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",l.jsx("br",{}),"│ 9 │ 2 │ 3 │10 │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ 8 │ 1 │ 4 │11 │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ 7 │ 6 │ 5 │12 │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│16 │15 │14 │13 │",l.jsx("br",{}),"└───┴───┴───┴───┘"]})}),l.jsx("p",{children:l.jsx("strong",{children:"Step 3: Place Letters in Spider Web Order"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0"},children:"🕷️ Place each letter according to its spider web number:"}),l.jsx("p",{style:{margin:"0 0 10px 0",fontFamily:"monospace",fontSize:"12px"},children:"T→1, E→2, E→3, N→4, X→5, N→6, O→7, A→8, M→9, T→10, O→11, X→12, X→13, X→14, X→15, X→16"}),l.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",l.jsx("br",{}),"│ M │ E │ E │ T │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ A │ T │ N │ O │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ O │ N │ X │ X │",l.jsx("br",{}),"├───┼───┼───┼───┤",l.jsx("br",{}),"│ X │ X │ X │ X │",l.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 4: Read Row by Row"})}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0"},children:"📖 Read left to right, top to bottom:"}),l.jsxs("p",{style:{margin:0,fontFamily:"monospace"},children:["Row 1: M-E-E-T",l.jsx("br",{}),"Row 2: A-T-N-O",l.jsx("br",{}),"Row 3: O-N-X-X",l.jsx("br",{}),"Row 4: X-X-X-X"]}),l.jsxs("p",{style:{margin:"10px 0 0 0"},children:["🧹 ",l.jsx("strong",{children:"Remove padding X's:"})," MEETATNOON"]}),l.jsxs("p",{style:{margin:"10px 0 0 0",fontWeight:"bold",color:"#16a34a"},children:["🎉 ",l.jsx("strong",{children:"Decrypted Message:"}),' "MEET AT NOON"']})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"1px solid #a855f7"},children:[l.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"⚡ QUICK REFERENCE CARD"}),l.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px"},children:[l.jsxs("div",{children:[l.jsx("p",{children:l.jsx("strong",{children:"🔐 Encryption:"})}),l.jsxs("ol",{style:{fontSize:"12px",marginLeft:"15px"},children:[l.jsx("li",{children:"Clean message (letters only)"}),l.jsx("li",{children:"Fill grid row by row"}),l.jsx("li",{children:"Read in spider web pattern"})]})]}),l.jsxs("div",{children:[l.jsx("p",{children:l.jsx("strong",{children:"🔓 Decryption:"})}),l.jsxs("ol",{style:{fontSize:"12px",marginLeft:"15px"},children:[l.jsx("li",{children:"Place letters in spider web order"}),l.jsx("li",{children:"Read grid row by row"}),l.jsx("li",{children:"Remove padding X's"})]})]})]}),l.jsxs("div",{style:{marginTop:"15px",padding:"10px",backgroundColor:"white",borderRadius:"4px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0",fontWeight:"bold"},children:"🕷️ Spider Web Pattern (for any grid size):"}),l.jsxs("p",{style:{margin:0,fontSize:"12px"},children:["1. Find center of grid",l.jsx("br",{}),'2. Number center as "1"',l.jsx("br",{}),"3. Move outward in rings",l.jsx("br",{}),"4. Within each ring, go clockwise",l.jsx("br",{}),"5. Continue until all cells numbered"]})]})]}),l.jsxs("div",{style:{backgroundColor:"#f0f9ff",padding:"15px",borderRadius:"8px",border:"1px solid #0ea5e9"},children:[l.jsx("h4",{style:{color:"#0369a1",marginTop:0},children:"📊 VISUAL REFERENCE CHARTS"}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("h5",{style:{color:"#f59e0b",margin:"10px 0 10px 0"},children:"🐝 Bee Dance Angle Chart"}),l.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"center"},children:[l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Letter-to-Angle Mapping"}),l.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(8, 35px)",gap:"2px",backgroundColor:"#fef3c7",padding:"8px",borderRadius:"6px"},children:["A","B","C","D","E","F","G","H"].map((n,r)=>l.jsxs("div",{style:{width:"35px",height:"45px",backgroundColor:"#ffffff",border:"1px solid #f59e0b",borderRadius:"4px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",fontSize:"10px"},children:[l.jsx("div",{style:{fontWeight:"bold",fontSize:"12px"},children:n}),l.jsxs("div",{style:{color:"#92400e"},children:[r*45,"°"]})]},n))}),l.jsx("p",{style:{fontSize:"9px",color:"#6b7280",marginTop:"5px"},children:"Pattern repeats: I=0°, J=45°, K=90°, L=135°, M=180°, N=225°, O=270°, P=315°, Q=0°..."})]}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Compass Visualization"}),l.jsxs("div",{style:{position:"relative",width:"100px",height:"100px",backgroundColor:"#fef3c7",borderRadius:"50%",border:"2px solid #f59e0b"},children:[l.jsx("div",{style:{position:"absolute",top:"2px",left:"50%",transform:"translateX(-50%)",fontSize:"8px",fontWeight:"bold"},children:"A(0°)"}),l.jsx("div",{style:{position:"absolute",top:"12px",right:"12px",fontSize:"8px",fontWeight:"bold"},children:"B(45°)"}),l.jsx("div",{style:{position:"absolute",top:"50%",right:"2px",transform:"translateY(-50%)",fontSize:"8px",fontWeight:"bold"},children:"C(90°)"}),l.jsx("div",{style:{position:"absolute",bottom:"12px",right:"12px",fontSize:"8px",fontWeight:"bold"},children:"D(135°)"}),l.jsx("div",{style:{position:"absolute",bottom:"2px",left:"50%",transform:"translateX(-50%)",fontSize:"8px",fontWeight:"bold"},children:"E(180°)"}),l.jsx("div",{style:{position:"absolute",bottom:"12px",left:"12px",fontSize:"8px",fontWeight:"bold"},children:"F(225°)"}),l.jsx("div",{style:{position:"absolute",top:"50%",left:"2px",transform:"translateY(-50%)",fontSize:"8px",fontWeight:"bold"},children:"G(270°)"}),l.jsx("div",{style:{position:"absolute",top:"12px",left:"12px",fontSize:"8px",fontWeight:"bold"},children:"H(315°)"}),l.jsx("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",fontSize:"16px"},children:"🐝"})]})]})]})]}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("h5",{style:{color:"#16a34a",margin:"10px 0 10px 0"},children:"🐜 Ant Pheromone Level Chart"}),l.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"flex-start"},children:[l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Pheromone Groups"}),l.jsx("div",{style:{backgroundColor:"#f0fdf4",padding:"10px",borderRadius:"6px"},children:[{level:1,letters:"A F K P U",color:"#dcfce7"},{level:2,letters:"B G L Q V",color:"#bbf7d0"},{level:3,letters:"C H M R W",color:"#86efac"},{level:4,letters:"D I N S X",color:"#4ade80"},{level:5,letters:"E J O T Y Z",color:"#16a34a"}].map(n=>l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",marginBottom:"5px",fontSize:"11px"},children:[l.jsx("div",{style:{width:"25px",height:"20px",backgroundColor:n.color,border:"1px solid #16a34a",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"10px"},children:n.level}),l.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold"},children:n.letters})]},n.level))})]}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Visual Strength Indicators"}),l.jsx("div",{style:{backgroundColor:"#f0fdf4",padding:"10px",borderRadius:"6px"},children:[1,2,3,4,5].map(n=>l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"5px",fontSize:"11px"},children:[l.jsxs("span",{style:{fontWeight:"bold",width:"15px"},children:["L",n,":"]}),l.jsx("div",{style:{display:"flex",gap:"2px"},children:Array.from({length:5},(r,i)=>l.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:i<n?"#16a34a":"#e5e7eb",borderRadius:"50%"}},i))}),l.jsx("span",{style:{fontSize:"9px",color:"#6b7280"},children:n===1?"Weakest":n===5?"Strongest":""})]},n))})]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#f0f9ff",padding:"15px",borderRadius:"8px",border:"1px solid #0ea5e9"},children:[l.jsx("h4",{style:{color:"#0369a1",marginTop:0},children:"🔧 INDIVIDUAL CIPHER METHODS"}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("h5",{style:{color:"#f59e0b",margin:"10px 0 5px 0"},children:"🐝 BEE DANCE CIPHER (Manual Steps)"}),l.jsxs("ol",{style:{marginLeft:"20px",fontSize:"12px"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Assign angles:"})," Use the chart above - A=0°, B=45°, C=90°, D=135°, E=180°, F=225°, G=270°, H=315°, then repeat"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"List letters with angles:"}),' For "HELLO" → H=315°, E=180°, L=90°, L=90°, O=135°']}),l.jsxs("li",{children:[l.jsx("strong",{children:"Sort by angle:"})," L=90°, L=90°, O=135°, E=180°, H=315°"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Encrypted result:"}),' "LLOEH"']}),l.jsxs("li",{children:[l.jsx("strong",{children:"To decrypt:"})," Reverse the process by restoring original order"]})]})]}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("h5",{style:{color:"#16a34a",margin:"10px 0 5px 0"},children:"🐜 ANT PHEROMONE CIPHER (Manual Steps)"}),l.jsxs("ol",{style:{marginLeft:"20px",fontSize:"12px"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Assign pheromone levels:"})," Use the chart above - Level 1: A,F,K,P,U; Level 2: B,G,L,Q,V; Level 3: C,H,M,R,W; Level 4: D,I,N,S,X; Level 5: E,J,O,T,Y,Z"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"List letters with levels:"}),' For "HELLO" → H=3, E=5, L=2, L=2, O=5']}),l.jsxs("li",{children:[l.jsx("strong",{children:"Sort by pheromone level:"})," L=2, L=2, H=3, E=5, O=5"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Encrypted result:"}),' "LLHEO"']}),l.jsxs("li",{children:[l.jsx("strong",{children:"To decrypt:"})," Reverse the process by restoring original order"]})]})]}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("h5",{style:{color:"#7c3aed",margin:"10px 0 5px 0"},children:"🕷️ SPIDER WEB CIPHER (Manual Steps)"}),l.jsxs("p",{style:{fontSize:"12px",marginLeft:"20px"},children:[l.jsx("strong",{children:"This is the main method shown in the detailed steps above."})," Fill grid row-by-row, then read in spider web pattern (center outward, clockwise in each ring)."]})]}),l.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"8px",border:"2px solid #16a34a"},children:[l.jsx("h5",{style:{color:"#15803d",margin:"0 0 10px 0"},children:"🕷️🐝🐜 COMBINED CIPHER (All Three Together)"}),l.jsxs("div",{style:{marginBottom:"15px"},children:[l.jsx("p",{style:{fontSize:"13px",fontWeight:"bold",margin:"0 0 5px 0"},children:"📋 MATERIALS NEEDED (Enhanced):"}),l.jsxs("ul",{style:{marginLeft:"20px",fontSize:"12px"},children:[l.jsx("li",{children:"📄 Graph paper or ruled paper"}),l.jsx("li",{children:"✏️ Pencil and eraser"}),l.jsxs("li",{children:["🎨 ",l.jsx("strong",{children:"3 different colored pens:"})," Red (spider), Yellow (bee), Green (ant)"]}),l.jsx("li",{children:"📐 Protractor (optional, for bee angles)"}),l.jsx("li",{children:"📊 Reference charts (bee angles & ant pheromone levels)"})]})]}),l.jsxs("div",{style:{marginBottom:"15px"},children:[l.jsx("p",{style:{fontSize:"13px",fontWeight:"bold",margin:"0 0 5px 0"},children:"🔧 STEP-BY-STEP COMBINED PROCESS:"}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[l.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 1: Prepare Message & Reference Charts"}),l.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Message:"}),' "HELLO" → Clean: "HELLOX..." (pad to fill grid)']}),l.jsxs("li",{children:[l.jsx("strong",{children:"Bee Angles:"})," A=0°, B=45°, C=90°, D=135°, E=180°, F=225°, G=270°, H=315°"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Ant Pheromones:"})," A,F,K,P,U=1; B,G,L,Q,V=2; C,H,M,R,W=3; D,I,N,S,X=4; E,J,O,T,Y,Z=5"]})]})]}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[l.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 2: Create Enhanced Grid with Bio-Data"}),l.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[l.jsx("li",{children:'Draw your grid (3×3 for "HELLO")'}),l.jsx("li",{children:"Fill row-by-row: H-E-L / L-O-X / X-X-X"}),l.jsxs("li",{children:[l.jsx("strong",{children:"Add bee angles:"})," H=315°, E=180°, L=90°, L=90°, O=135°"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Add ant pheromones:"})," H=3, E=5, L=2, L=2, O=5"]})]}),l.jsxs("div",{style:{fontFamily:"monospace",fontSize:"10px",backgroundColor:"#f9fafb",padding:"5px",borderRadius:"4px",marginTop:"5px"},children:["Enhanced Grid Example:",l.jsx("br",{}),"┌─────────┬─────────┬─────────┐",l.jsx("br",{}),"│ H(315°,3)│ E(180°,5)│ L(90°,2) │",l.jsx("br",{}),"├─────────┼─────────┼─────────┤",l.jsx("br",{}),"│ L(90°,2) │ O(135°,5)│ X(270°,4)│",l.jsx("br",{}),"├─────────┼─────────┼─────────┤",l.jsx("br",{}),"│ X(270°,4)│ X(270°,4)│ X(270°,4)│",l.jsx("br",{}),"└─────────┴─────────┴─────────┘"]})]}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[l.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 3: Apply Visual Bio-Encoding"}),l.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"🕷️ Spider Web:"})," Mark center with red circle, draw red rings around it"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"🐝 Bee Dance:"})," Draw yellow arrows showing dance angles for each letter"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"🐜 Ant Trails:"})," Use green dots (1-5) to show pheromone strength"]})]})]}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[l.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 4: Read in Spider Web Pattern"}),l.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[l.jsx("li",{children:"Follow spider web numbering: Center(1) → Ring 1 clockwise → Ring 2 clockwise"}),l.jsx("li",{children:"For 3×3 grid: O(center) → H,E,L,L(ring1) → X,X,X,X(ring2)"}),l.jsxs("li",{children:[l.jsx("strong",{children:"🔐 Final Result:"}),' "OHELLXXXX"']})]})]}),l.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px"},children:[l.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 5: Document Bio-Patterns"}),l.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Record bee sequence:"})," 135°→315°→180°→90°→90°→270°→270°→270°→270°"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Record ant sequence:"})," 5→3→5→2→2→4→4→4→4"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Note:"})," These patterns provide additional verification and visual encoding"]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"10px",borderRadius:"6px"},children:[l.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0",color:"#92400e"},children:"🎯 DECRYPTION PROCESS:"}),l.jsxs("ol",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[l.jsx("li",{children:"Place encrypted letters back in spider web order"}),l.jsx("li",{children:"Verify using bee angles and ant pheromone patterns"}),l.jsx("li",{children:"Read grid row-by-row to get original message"}),l.jsx("li",{children:`Remove padding X's to reveal: "HELLO"`})]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#fef2f2",padding:"15px",borderRadius:"8px",border:"1px solid #ef4444"},children:[l.jsx("h4",{style:{color:"#dc2626",marginTop:0},children:"🎭 PRESENTATION & DEMO TIPS"}),l.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[l.jsxs("li",{children:["🎨 ",l.jsx("strong",{children:"Use colors:"})," Different colors for each ring of the spider web"]}),l.jsxs("li",{children:["📏 ",l.jsx("strong",{children:"Large grids:"})," Use poster board or whiteboard for audience visibility"]}),l.jsxs("li",{children:["👥 ",l.jsx("strong",{children:"Audience participation:"})," Have volunteers help fill the grid"]}),l.jsxs("li",{children:["🔄 ",l.jsx("strong",{children:"Show both ways:"})," Encrypt a message, then decrypt it back"]}),l.jsxs("li",{children:["🌟 ",l.jsx("strong",{children:"Bio-connection:"})," Explain the spider, bee, and ant inspiration"]}),l.jsxs("li",{children:["📱 ",l.jsx("strong",{children:"Compare:"})," Show manual vs. digital tool results"]}),l.jsxs("li",{children:["🎯 ",l.jsx("strong",{children:"Start simple:"}),' Use 3×3 grid with short words like "HELLO"']}),l.jsxs("li",{children:["🔀 ",l.jsx("strong",{children:"Try different ciphers:"})," Show how each bio-inspired method works differently"]}),l.jsxs("li",{children:["📊 ",l.jsx("strong",{children:"Compare results:"})," Encrypt same message with spider, bee, and ant methods"]})]})]})]})]}):l.jsx("div",{style:{textAlign:"center",marginBottom:"20px"},children:l.jsx("button",{onClick:t,style:{padding:"10px 20px",backgroundColor:"#059669",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold"},children:"📝 Pen & Paper Guide (For Demos)"})})}function L1({isVisible:e,onToggle:t}){return e?l.jsxs(Kr.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},style:{backgroundColor:"#f8fafc",border:"2px solid #e2e8f0",borderRadius:"12px",padding:"20px",marginBottom:"30px",fontSize:"14px",lineHeight:"1.6"},children:[l.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[l.jsx("h3",{style:{color:"#1e293b",margin:0},children:"🧠 How the Bio-Inspired Cipher Works"}),l.jsx("button",{onClick:t,style:{padding:"5px 10px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"✕ Close"})]}),l.jsxs("div",{style:{display:"grid",gap:"20px"},children:[l.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[l.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"🔐 ENCRYPTION PROCESS"}),l.jsxs("div",{style:{marginLeft:"10px"},children:[l.jsx("p",{children:l.jsx("strong",{children:"Step 1: Message Preparation"})}),l.jsxs("ul",{style:{marginLeft:"20px"},children:[l.jsx("li",{children:"Your message is cleaned (only letters A-Z kept)"}),l.jsx("li",{children:"Padded with 'X' characters to fill the grid completely"}),l.jsx("li",{children:'Example: "HELLO" → "HELLOX..." for a 5×5 grid (25 characters)'})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 2: Grid Filling (Like Ants Building)"})}),l.jsxs("ul",{style:{marginLeft:"20px"},children:[l.jsxs("li",{children:["Letters are placed in the grid ",l.jsx("em",{children:"row by row"}),", left to right"]}),l.jsx("li",{children:"Just like ants systematically building their nest"}),l.jsx("li",{children:"Each letter gets bio-inspired properties:"}),l.jsxs("li",{style:{marginLeft:"20px"},children:["🐝 ",l.jsx("strong",{children:"Bee Angle:"})," Each letter has a dance direction (A=0°, B=45°, etc.)"]}),l.jsxs("li",{style:{marginLeft:"20px"},children:["🐜 ",l.jsx("strong",{children:"Pheromone Level:"})," Strength from 1-5 based on the letter"]})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 3: Spider Web Reading Pattern"})}),l.jsxs("ul",{style:{marginLeft:"20px"},children:[l.jsxs("li",{children:["🕷️ ",l.jsx("strong",{children:"Start from center:"})," Like a spider beginning its web"]}),l.jsxs("li",{children:["📍 ",l.jsx("strong",{children:"Expand in rings:"})," Read outward in concentric circles"]}),l.jsxs("li",{children:["🔄 ",l.jsx("strong",{children:"Clockwise spiral:"})," Within each ring, go clockwise"]}),l.jsx("li",{children:"This creates the encrypted message by reading in spider-web order!"})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#dbeafe",padding:"15px",borderRadius:"8px",border:"1px solid #3b82f6"},children:[l.jsx("h4",{style:{color:"#1e40af",marginTop:0},children:"🔓 DECRYPTION PROCESS"}),l.jsxs("div",{style:{marginLeft:"10px"},children:[l.jsx("p",{children:l.jsx("strong",{children:"Step 1: Reverse Spider Web Placement"})}),l.jsxs("ul",{style:{marginLeft:"20px"},children:[l.jsx("li",{children:"Take the encrypted message and place it back using spider web pattern"}),l.jsxs("li",{children:["🕷️ ",l.jsx("strong",{children:"First character:"})," Goes to center of the web"]}),l.jsxs("li",{children:["📍 ",l.jsx("strong",{children:"Next characters:"})," Fill rings outward (center → ring 1 → ring 2...)"]}),l.jsxs("li",{children:["🔄 ",l.jsx("strong",{children:"Clockwise order:"})," Within each ring, place clockwise"]})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 2: Row-by-Row Reading"})}),l.jsxs("ul",{style:{marginLeft:"20px"},children:[l.jsx("li",{children:"Once the grid is filled using spider pattern, read it normally"}),l.jsxs("li",{children:["📖 ",l.jsx("strong",{children:"Left to right, top to bottom"})," (like reading a book)"]}),l.jsx("li",{children:"This reverses the encryption and reveals the original message!"})]}),l.jsx("p",{children:l.jsx("strong",{children:"Step 3: Clean Up"})}),l.jsxs("ul",{style:{marginLeft:"20px"},children:[l.jsx("li",{children:"Remove trailing 'X' characters that were padding"}),l.jsxs("li",{children:["🎉 ",l.jsx("strong",{children:"Result:"})," Your original message is revealed!"]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"8px",border:"1px solid #16a34a"},children:[l.jsx("h4",{style:{color:"#15803d",marginTop:0},children:"� VISUAL CIPHER DIAGRAMS"}),l.jsxs("div",{style:{marginBottom:"25px"},children:[l.jsx("h5",{style:{color:"#dc2626",margin:"0 0 10px 0"},children:"🕷️ Spider Web Pattern Visualization"}),l.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"center"},children:[l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Step 1: Fill Grid Row-by-Row"}),l.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 40px)",gap:"2px",backgroundColor:"#fef2f2",padding:"10px",borderRadius:"6px"},children:["H","E","L","L","O","X","X","X","X"].map((n,r)=>l.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:"#ffffff",border:"2px solid #dc2626",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"14px"},children:n},r))})]}),l.jsx("div",{style:{fontSize:"24px",color:"#dc2626"},children:"→"}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Step 2: Spider Web Reading Order"}),l.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 40px)",gap:"2px",backgroundColor:"#fef2f2",padding:"10px",borderRadius:"6px"},children:[3,1,4,2,"🕷️",5,8,7,6].map((n,r)=>l.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:r===4?"#dc2626":"#ffffff",border:"2px solid #dc2626",borderRadius:r===4?"50%":"4px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:r===4?"16px":"12px",color:r===4?"white":"#dc2626"},children:n},r))})]}),l.jsx("div",{style:{fontSize:"24px",color:"#dc2626"},children:"→"}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Result: OHELXXXXX"}),l.jsx("div",{style:{backgroundColor:"#dc2626",color:"white",padding:"10px",borderRadius:"6px",fontFamily:"monospace",fontSize:"16px",fontWeight:"bold",textAlign:"center"},children:"O H E L X X X X X"})]})]})]}),l.jsxs("div",{style:{marginBottom:"25px"},children:[l.jsx("h5",{style:{color:"#f59e0b",margin:"0 0 10px 0"},children:"🐝 Bee Dance Angle Visualization"}),l.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"center"},children:[l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Letter Angles"}),l.jsx("div",{style:{backgroundColor:"#fef3c7",padding:"10px",borderRadius:"6px"},children:["H=315°","E=180°","L=90°","L=90°","O=135°"].map((n,r)=>l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",marginBottom:"5px",fontSize:"11px"},children:[l.jsx("div",{style:{width:"20px",height:"20px",backgroundColor:"#f59e0b",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontWeight:"bold",fontSize:"10px"},children:n.charAt(0)}),l.jsx("span",{style:{fontFamily:"monospace"},children:n})]},r))})]}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Compass Directions"}),l.jsxs("div",{style:{position:"relative",width:"120px",height:"120px",backgroundColor:"#fef3c7",borderRadius:"50%",border:"3px solid #f59e0b"},children:[l.jsx("div",{style:{position:"absolute",top:"5px",left:"50%",transform:"translateX(-50%)",fontSize:"10px",fontWeight:"bold"},children:"0°"}),l.jsx("div",{style:{position:"absolute",top:"15px",right:"15px",fontSize:"10px",fontWeight:"bold"},children:"45°"}),l.jsx("div",{style:{position:"absolute",top:"50%",right:"5px",transform:"translateY(-50%)",fontSize:"10px",fontWeight:"bold"},children:"90°"}),l.jsx("div",{style:{position:"absolute",bottom:"15px",right:"15px",fontSize:"10px",fontWeight:"bold"},children:"135°"}),l.jsx("div",{style:{position:"absolute",bottom:"5px",left:"50%",transform:"translateX(-50%)",fontSize:"10px",fontWeight:"bold"},children:"180°"}),l.jsx("div",{style:{position:"absolute",bottom:"15px",left:"15px",fontSize:"10px",fontWeight:"bold"},children:"225°"}),l.jsx("div",{style:{position:"absolute",top:"50%",left:"5px",transform:"translateY(-50%)",fontSize:"10px",fontWeight:"bold"},children:"270°"}),l.jsx("div",{style:{position:"absolute",top:"15px",left:"15px",fontSize:"10px",fontWeight:"bold"},children:"315°"}),l.jsx("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",fontSize:"20px"},children:"🐝"})]})]}),l.jsx("div",{style:{fontSize:"24px",color:"#f59e0b"},children:"→"}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Sorted by Angle"}),l.jsxs("div",{style:{backgroundColor:"#f59e0b",color:"white",padding:"10px",borderRadius:"6px"},children:[l.jsx("div",{style:{fontFamily:"monospace",fontSize:"14px",fontWeight:"bold",textAlign:"center"},children:"L L O E H"}),l.jsx("div",{style:{fontSize:"10px",textAlign:"center",marginTop:"5px"},children:"90° 90° 135° 180° 315°"})]})]})]})]}),l.jsxs("div",{style:{marginBottom:"15px"},children:[l.jsx("h5",{style:{color:"#16a34a",margin:"0 0 10px 0"},children:"🐜 Ant Pheromone Trail Visualization"}),l.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"center"},children:[l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Pheromone Levels"}),l.jsx("div",{style:{backgroundColor:"#f0fdf4",padding:"10px",borderRadius:"6px"},children:[{letter:"H",level:3,group:"C,H,M,R,W"},{letter:"E",level:5,group:"E,J,O,T,Y,Z"},{letter:"L",level:2,group:"B,G,L,Q,V"},{letter:"L",level:2,group:"B,G,L,Q,V"},{letter:"O",level:5,group:"E,J,O,T,Y,Z"}].map((n,r)=>l.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"4px",fontSize:"10px"},children:[l.jsx("div",{style:{width:"18px",height:"18px",backgroundColor:"#16a34a",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontWeight:"bold",fontSize:"9px"},children:n.letter}),l.jsx("div",{style:{display:"flex",gap:"2px"},children:Array.from({length:5},(i,o)=>l.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:o<n.level?"#16a34a":"#e5e7eb",borderRadius:"50%"}},o))}),l.jsxs("span",{style:{fontSize:"9px",color:"#6b7280"},children:["Level ",n.level]})]},r))})]}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Pheromone Groups"}),l.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"10px",borderRadius:"6px",fontSize:"10px"},children:[l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"Level 1:"})," A,F,K,P,U"]}),l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"Level 2:"})," B,G,L,Q,V"]}),l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"Level 3:"})," C,H,M,R,W"]}),l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"Level 4:"})," D,I,N,S,X"]}),l.jsxs("div",{children:[l.jsx("strong",{children:"Level 5:"})," E,J,O,T,Y,Z"]})]})]}),l.jsx("div",{style:{fontSize:"24px",color:"#16a34a"},children:"→"}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"12px",margin:"0 0 8px 0",fontWeight:"bold"},children:"Sorted by Strength"}),l.jsxs("div",{style:{backgroundColor:"#16a34a",color:"white",padding:"10px",borderRadius:"6px"},children:[l.jsx("div",{style:{fontFamily:"monospace",fontSize:"14px",fontWeight:"bold",textAlign:"center"},children:"L L H E O"}),l.jsx("div",{style:{fontSize:"10px",textAlign:"center",marginTop:"5px"},children:"2 2 3 5 5"})]})]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"2px solid #a855f7"},children:[l.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"🕷️🐝🐜 COMBINED CIPHER VISUAL PROCESS"}),l.jsxs("div",{style:{marginLeft:"10px"},children:[l.jsxs("p",{children:[l.jsx("strong",{children:"Message:"}),' "HELLO" (using 3×3 grid with all three bio-patterns)']}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("h6",{style:{color:"#7c3aed",margin:"0 0 10px 0"},children:"Step 1: Enhanced Grid with Bio-Data"}),l.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"center"},children:[l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 5px 0",fontWeight:"bold"},children:"Basic Grid (Row-by-Row Fill)"}),l.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 50px)",gap:"2px",backgroundColor:"#f8fafc",padding:"8px",borderRadius:"6px"},children:["H","E","L","L","O","X","X","X","X"].map((n,r)=>l.jsx("div",{style:{width:"50px",height:"50px",backgroundColor:"#ffffff",border:"2px solid #a855f7",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"16px",position:"relative"},children:n},r))})]}),l.jsx("div",{style:{fontSize:"20px",color:"#7c3aed"},children:"+"}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 5px 0",fontWeight:"bold"},children:"Bio-Data Enhancement"}),l.jsxs("div",{style:{backgroundColor:"#f8fafc",padding:"10px",borderRadius:"6px",fontSize:"10px"},children:[l.jsxs("div",{style:{marginBottom:"5px"},children:[l.jsx("span",{style:{color:"#f59e0b"},children:"🐝 Bee Angles:"})," H=315°, E=180°, L=90°, O=135°"]}),l.jsxs("div",{children:[l.jsx("span",{style:{color:"#16a34a"},children:"🐜 Pheromones:"})," H=3, E=5, L=2, O=5"]})]})]}),l.jsx("div",{style:{fontSize:"20px",color:"#7c3aed"},children:"="}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 5px 0",fontWeight:"bold"},children:"Enhanced Grid"}),l.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 60px)",gap:"2px",backgroundColor:"#f8fafc",padding:"8px",borderRadius:"6px"},children:[{letter:"H",angle:315,pheromone:3},{letter:"E",angle:180,pheromone:5},{letter:"L",angle:90,pheromone:2},{letter:"L",angle:90,pheromone:2},{letter:"O",angle:135,pheromone:5},{letter:"X",angle:270,pheromone:4},{letter:"X",angle:270,pheromone:4},{letter:"X",angle:270,pheromone:4},{letter:"X",angle:270,pheromone:4}].map((n,r)=>l.jsxs("div",{style:{width:"60px",height:"60px",backgroundColor:"#ffffff",border:"2px solid #a855f7",borderRadius:"6px",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:"14px",position:"relative"},children:[l.jsx("div",{style:{fontSize:"16px"},children:n.letter}),l.jsxs("div",{style:{fontSize:"8px",color:"#f59e0b"},children:[n.angle,"°"]}),l.jsx("div",{style:{display:"flex",gap:"1px",marginTop:"2px"},children:Array.from({length:n.pheromone},(i,o)=>l.jsx("div",{style:{width:"4px",height:"4px",backgroundColor:"#16a34a",borderRadius:"50%"}},o))})]},r))})]})]})]}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("h6",{style:{color:"#7c3aed",margin:"0 0 10px 0"},children:"Step 2: Apply Spider Web Reading Pattern"}),l.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"center"},children:[l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 5px 0",fontWeight:"bold"},children:"Spider Web Order"}),l.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(3, 40px)",gap:"2px",backgroundColor:"#fef2f2",padding:"8px",borderRadius:"6px"},children:[3,1,4,2,"🕷️",5,8,7,6].map((n,r)=>l.jsx("div",{style:{width:"40px",height:"40px",backgroundColor:r===4?"#dc2626":"#ffffff",border:"2px solid #dc2626",borderRadius:r===4?"50%":"4px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold",fontSize:r===4?"14px":"11px",color:r===4?"white":"#dc2626"},children:n},r))})]}),l.jsx("div",{style:{fontSize:"20px",color:"#7c3aed"},children:"→"}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 5px 0",fontWeight:"bold"},children:"Reading Sequence"}),l.jsxs("div",{style:{backgroundColor:"#f8fafc",padding:"10px",borderRadius:"6px",fontSize:"10px"},children:[l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"1→"})," O(135°,5) [Center]"]}),l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"2→"})," L(90°,2) [Ring 1]"]}),l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"3→"})," H(315°,3) [Ring 1]"]}),l.jsxs("div",{style:{marginBottom:"3px"},children:[l.jsx("strong",{children:"4→"})," L(90°,2) [Ring 1]"]}),l.jsxs("div",{children:[l.jsx("strong",{children:"5→"})," X(270°,4) [Ring 1]"]})]})]}),l.jsx("div",{style:{fontSize:"20px",color:"#7c3aed"},children:"→"}),l.jsxs("div",{children:[l.jsx("p",{style:{fontSize:"11px",margin:"0 0 5px 0",fontWeight:"bold"},children:"Final Result"}),l.jsxs("div",{style:{backgroundColor:"#7c3aed",color:"white",padding:"10px",borderRadius:"6px"},children:[l.jsx("div",{style:{fontFamily:"monospace",fontSize:"16px",fontWeight:"bold",textAlign:"center"},children:"O L H L X"}),l.jsx("div",{style:{fontSize:"9px",textAlign:"center",marginTop:"5px",opacity:.8},children:"+ remaining X's"})]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#f8fafc",padding:"12px",borderRadius:"6px"},children:[l.jsx("h6",{style:{color:"#7c3aed",margin:"0 0 10px 0"},children:"Step 3: Triple Bio-Pattern Summary"}),l.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"10px"},children:[l.jsxs("div",{style:{backgroundColor:"#fef2f2",padding:"8px",borderRadius:"4px"},children:[l.jsx("div",{style:{fontSize:"11px",fontWeight:"bold",color:"#dc2626",marginBottom:"5px"},children:"�️ Spider Web Pattern"}),l.jsx("div",{style:{fontSize:"10px",fontFamily:"monospace"},children:"Primary: OLHLXXXX"}),l.jsx("div",{style:{fontSize:"9px",color:"#6b7280"},children:"Center-outward reading"})]}),l.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"8px",borderRadius:"4px"},children:[l.jsx("div",{style:{fontSize:"11px",fontWeight:"bold",color:"#92400e",marginBottom:"5px"},children:"🐝 Bee Dance Angles"}),l.jsx("div",{style:{fontSize:"10px",fontFamily:"monospace"},children:"135°→90°→315°→90°→270°"}),l.jsx("div",{style:{fontSize:"9px",color:"#6b7280"},children:"Directional encoding"})]}),l.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"8px",borderRadius:"4px"},children:[l.jsx("div",{style:{fontSize:"11px",fontWeight:"bold",color:"#16a34a",marginBottom:"5px"},children:"🐜 Ant Pheromones"}),l.jsx("div",{style:{fontSize:"10px",fontFamily:"monospace"},children:"5→2→3→2→4"}),l.jsx("div",{style:{fontSize:"9px",color:"#6b7280"},children:"Strength encoding"})]})]})]})]})]}),l.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"1px solid #a855f7"},children:[l.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"🌿 INDIVIDUAL CIPHER METHODS"}),l.jsxs("div",{style:{marginLeft:"10px"},children:[l.jsxs("div",{style:{marginBottom:"15px"},children:[l.jsx("p",{children:l.jsx("strong",{children:"🕷️ Spider Web Cipher:"})}),l.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Encryption:"})," Fill grid row-by-row, then read in spider web pattern (center → rings clockwise)"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Decryption:"})," Place encrypted letters in spider web order, then read row-by-row"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Inspiration:"})," Spiders build webs from center outward in organized spiral patterns"]})]})]}),l.jsxs("div",{style:{marginBottom:"15px"},children:[l.jsx("p",{children:l.jsx("strong",{children:"🐝 Bee Dance Cipher:"})}),l.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Encryption:"}),' Sort letters by their "dance angles" (A=0°, B=45°, C=90°, etc.)']}),l.jsxs("li",{children:[l.jsx("strong",{children:"Decryption:"})," Reverse the angle-based sorting to restore original order"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Inspiration:"})," Bees communicate direction and distance through waggle dance angles"]})]})]}),l.jsxs("div",{style:{marginBottom:"15px"},children:[l.jsx("p",{children:l.jsx("strong",{children:"🐜 Ant Pheromone Cipher:"})}),l.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Encryption:"}),' Sort letters by "pheromone strength" (A,F,K,P,U=1; B,G,L,Q,V=2; etc.)']}),l.jsxs("li",{children:[l.jsx("strong",{children:"Decryption:"})," Reverse the pheromone-based sorting to restore original order"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Inspiration:"})," Ants leave chemical trails with varying strengths to guide colony members"]})]})]}),l.jsxs("div",{children:[l.jsx("p",{children:l.jsx("strong",{children:"🕷️🐝🐜 Combined Cipher (All Three Together):"})}),l.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Step 1 - Spider Web:"})," Use spider web pattern as the primary encryption method"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Step 2 - Bee Dance:"})," Apply bee angle transformations to each letter during placement"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Step 3 - Ant Pheromones:"})," Use pheromone levels to determine visual encoding and priority"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Result:"})," Triple-layered bio-inspired encryption with visual cues from all three methods"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Security:"})," Multiple bio-inspired patterns create a more complex and secure cipher"]})]})]}),l.jsxs("div",{style:{backgroundColor:"#f0f9ff",padding:"10px",borderRadius:"6px",marginTop:"15px"},children:[l.jsx("p",{style:{margin:"0 0 10px 0",fontWeight:"bold",color:"#0369a1"},children:"🔄 Combined Cipher Process:"}),l.jsxs("ol",{style:{marginLeft:"20px",fontSize:"12px",margin:0},children:[l.jsxs("li",{children:[l.jsx("strong",{children:"Fill grid row-by-row"})," with your message (like ants building systematically)"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Calculate bee angles and ant pheromones"})," for each letter for visual encoding"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Read in spider web pattern"})," (center outward, clockwise) for final encryption"]}),l.jsxs("li",{children:[l.jsx("strong",{children:"Visual display"})," shows all three bio-patterns working together"]})]})]}),l.jsx("p",{style:{fontStyle:"italic",color:"#6b46c1",marginTop:"15px"},children:"Each cipher can be used independently or combined for enhanced security. Nature's communication methods inspire beautiful and secure encryption patterns!"})]})]})]})]}):l.jsx("div",{style:{textAlign:"center",marginBottom:"20px"},children:l.jsx("button",{onClick:t,style:{padding:"10px 20px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold"},children:"📚 How Does This Cipher Work?"})})}function A1(){const[e,t]=L.useState("MEETATNOON"),[n,r]=L.useState("encrypt"),[i,o]=L.useState(5),[s,a]=L.useState("combined"),[u,c]=L.useState(!1),[f,d]=L.useState(!1),p=()=>{if(n!=="encrypt")return"";switch(s){case"spider":return We.encryptWithSpiderOnly(e,i);case"bee":return We.encryptWithBeeOnly(e,i);case"ant":return We.encryptWithAntOnly(e,i);case"combined":default:return We.encryptWithSpiderOnly(e,i)}};return l.jsxs("div",{style:{backgroundColor:"#ffffff",border:"2px solid #e5e7eb",borderRadius:"16px",padding:"30px",boxShadow:"0 10px 25px rgba(0,0,0,0.1)"},children:[l.jsx("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#374151",marginBottom:"20px",textAlign:"center"},children:"�️🐝🐜 Bio-Inspired Cipher: Nature's Communication Secrets"}),l.jsxs("div",{style:{backgroundColor:"#f0f9ff",border:"1px solid #0ea5e9",borderRadius:"8px",padding:"15px",marginBottom:"20px",fontSize:"14px",textAlign:"center"},children:[l.jsx("strong",{children:"Inspired by Nature:"})," Spider web construction 🕷️ + Bee waggle dance 🐝 + Ant pheromone trails 🐜"]}),l.jsx(R1,{isVisible:f,onToggle:()=>d(!f)}),l.jsx(L1,{isVisible:u,onToggle:()=>c(!u)}),l.jsxs("div",{style:{marginBottom:"20px"},children:[l.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"Enter your message:"}),l.jsx("input",{type:"text",value:e,onChange:m=>t(m.target.value),placeholder:"Enter your message here...",style:{width:"100%",padding:"12px 16px",border:"2px solid #d1d5db",borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s"},onFocus:m=>m.target.style.borderColor="#3b82f6",onBlur:m=>m.target.style.borderColor="#d1d5db"})]}),l.jsxs("div",{style:{marginBottom:"20px",textAlign:"center"},children:[l.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"🕸️ Spider Web Size (Grid Dimensions):"}),l.jsx("div",{style:{display:"flex",gap:"8px",justifyContent:"center",flexWrap:"wrap"},children:[3,4,5,6,7,8].map(m=>l.jsxs("button",{onClick:()=>o(m),style:{padding:"8px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:i===m?"#8b5cf6":"#f3f4f6",borderColor:i===m?"#7c3aed":"#d1d5db",color:i===m?"white":"#374151",transform:i===m?"scale(1.05)":"scale(1)"},children:[m,"×",m]},m))}),l.jsxs("p",{style:{fontSize:"12px",color:"#6b7280",marginTop:"8px"},children:["Larger grids can hold more characters (",i,"×",i," = ",i*i," characters)"]})]}),l.jsxs("div",{style:{marginBottom:"20px",textAlign:"center"},children:[l.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"🌿 Choose Your Bio-Inspired Cipher:"}),l.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"8px",justifyContent:"center",maxWidth:"800px",margin:"0 auto"},children:[l.jsx("button",{onClick:()=>a("combined"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="combined"?"#8b5cf6":"#f3f4f6",borderColor:s==="combined"?"#7c3aed":"#d1d5db",color:s==="combined"?"white":"#374151",transform:s==="combined"?"scale(1.02)":"scale(1)"},children:"🕷️🐝🐜 All Combined"}),l.jsx("button",{onClick:()=>a("spider"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="spider"?"#dc2626":"#fef2f2",borderColor:s==="spider"?"#991b1b":"#fecaca",color:s==="spider"?"white":"#dc2626",transform:s==="spider"?"scale(1.02)":"scale(1)"},children:"🕷️ Spider Web Only"}),l.jsx("button",{onClick:()=>a("bee"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="bee"?"#f59e0b":"#fef3c7",borderColor:s==="bee"?"#d97706":"#fde68a",color:s==="bee"?"white":"#92400e",transform:s==="bee"?"scale(1.02)":"scale(1)"},children:"🐝 Bee Dance Only"}),l.jsx("button",{onClick:()=>a("ant"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="ant"?"#16a34a":"#f0fdf4",borderColor:s==="ant"?"#15803d":"#bbf7d0",color:s==="ant"?"white":"#16a34a",transform:s==="ant"?"scale(1.02)":"scale(1)"},children:"🐜 Ant Trails Only"})]}),l.jsxs("p",{style:{fontSize:"12px",color:"#6b7280",marginTop:"8px"},children:[s==="combined"&&"Uses spider web pattern with bee and ant visual encoding",s==="spider"&&"Encrypts by reading grid in spider web pattern (center outward)",s==="bee"&&"Encrypts by sorting letters based on bee dance angles",s==="ant"&&"Encrypts by sorting letters based on ant pheromone strength"]})]}),l.jsxs("div",{style:{display:"flex",gap:"12px",marginBottom:"30px",justifyContent:"center"},children:[l.jsx("button",{onClick:()=>r("encrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="encrypt"?"#f59e0b":"#fef3c7",color:n==="encrypt"?"white":"#92400e",transform:n==="encrypt"?"scale(1.05)":"scale(1)"},children:"🕷️ Encrypt"}),l.jsx("button",{onClick:()=>r("decrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="decrypt"?"#3b82f6":"#dbeafe",color:n==="decrypt"?"white":"#1e40af",transform:n==="decrypt"?"scale(1.05)":"scale(1)"},children:"🔓 Decrypt"})]}),n==="encrypt"?l.jsxs("div",{children:[l.jsxs("h3",{style:{textAlign:"center",color:"#92400e",marginBottom:"10px"},children:[s==="combined"&&"🕷️ Spider Web Construction + 🐝 Bee Dance + 🐜 Ant Trails",s==="spider"&&"🕷️ Spider Web Construction",s==="bee"&&"🐝 Bee Waggle Dance Encryption",s==="ant"&&"🐜 Ant Pheromone Trail Encryption"]}),l.jsx(E1,{message:e,gridSize:i,cipherType:s}),e&&l.jsx("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#fef3c7",border:"2px solid #f59e0b",borderRadius:"8px",textAlign:"center"},children:l.jsxs("strong",{style:{color:"#92400e"},children:["🔐 Bio-Encrypted: ",p()]})})]}):l.jsxs("div",{children:[l.jsx("h3",{style:{textAlign:"center",color:"#1e40af",marginBottom:"10px"},children:"🔍 Bio-Pattern Analysis & Decryption"}),l.jsx(T1,{message:e,gridSize:i,cipherType:s})]})]})}function M1(){return l.jsx("div",{style:{minHeight:"100vh",backgroundColor:"#fffbee",padding:"20px"},children:l.jsxs("div",{style:{maxWidth:"800px",margin:"0 auto"},children:[l.jsx("h1",{style:{textAlign:"center",color:"#92400e",fontSize:"2.5rem",marginBottom:"1rem"},children:"🕷️🐝🐜 Nature's Cipher Laboratory"}),l.jsx("p",{style:{textAlign:"center",color:"#6b7280",fontSize:"1.2rem",marginBottom:"2rem",fontStyle:"italic"},children:"Encryption inspired by spider webs, bee dances, and ant trails"}),l.jsx(A1,{})]})})}Ts.createRoot(document.getElementById("root")).render(l.jsx(zl.StrictMode,{children:l.jsx(M1,{})}));
