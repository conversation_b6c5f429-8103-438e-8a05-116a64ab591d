(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Wh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var af={exports:{}},xo={},uf={exports:{}},_={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qr=Symbol.for("react.element"),$h=Symbol.for("react.portal"),Hh=Symbol.for("react.fragment"),Xh=Symbol.for("react.strict_mode"),bh=Symbol.for("react.profiler"),Gh=Symbol.for("react.provider"),Kh=Symbol.for("react.context"),Qh=Symbol.for("react.forward_ref"),Yh=Symbol.for("react.suspense"),Zh=Symbol.for("react.memo"),Jh=Symbol.for("react.lazy"),Qa=Symbol.iterator;function qh(e){return e===null||typeof e!="object"?null:(e=Qa&&e[Qa]||e["@@iterator"],typeof e=="function"?e:null)}var cf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ff=Object.assign,df={};function Yn(e,t,n){this.props=e,this.context=t,this.refs=df,this.updater=n||cf}Yn.prototype.isReactComponent={};Yn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Yn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function pf(){}pf.prototype=Yn.prototype;function Vl(e,t,n){this.props=e,this.context=t,this.refs=df,this.updater=n||cf}var Nl=Vl.prototype=new pf;Nl.constructor=Vl;ff(Nl,Yn.prototype);Nl.isPureReactComponent=!0;var Ya=Array.isArray,hf=Object.prototype.hasOwnProperty,Ol={current:null},mf={key:!0,ref:!0,__self:!0,__source:!0};function gf(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)hf.call(t,r)&&!mf.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Qr,type:e,key:o,ref:s,props:i,_owner:Ol.current}}function em(e,t){return{$$typeof:Qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function _l(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qr}function tm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Za=/\/+/g;function $o(e,t){return typeof e=="object"&&e!==null&&e.key!=null?tm(""+e.key):t.toString(36)}function Ei(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Qr:case $h:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+$o(s,0):r,Ya(i)?(n="",e!=null&&(n=e.replace(Za,"$&/")+"/"),Ei(i,t,n,"",function(u){return u})):i!=null&&(_l(i)&&(i=em(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Za,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Ya(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+$o(o,l);s+=Ei(o,t,n,a,i)}else if(a=qh(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+$o(o,l++),s+=Ei(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function oi(e,t,n){if(e==null)return e;var r=[],i=0;return Ei(e,r,"","",function(o){return t.call(n,o,i++)}),r}function nm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var we={current:null},ji={transition:null},rm={ReactCurrentDispatcher:we,ReactCurrentBatchConfig:ji,ReactCurrentOwner:Ol};function yf(){throw Error("act(...) is not supported in production builds of React.")}_.Children={map:oi,forEach:function(e,t,n){oi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return oi(e,function(){t++}),t},toArray:function(e){return oi(e,function(t){return t})||[]},only:function(e){if(!_l(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};_.Component=Yn;_.Fragment=Hh;_.Profiler=bh;_.PureComponent=Vl;_.StrictMode=Xh;_.Suspense=Yh;_.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rm;_.act=yf;_.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=ff({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Ol.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)hf.call(t,a)&&!mf.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:Qr,type:e.type,key:i,ref:o,props:r,_owner:s}};_.createContext=function(e){return e={$$typeof:Kh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Gh,_context:e},e.Consumer=e};_.createElement=gf;_.createFactory=function(e){var t=gf.bind(null,e);return t.type=e,t};_.createRef=function(){return{current:null}};_.forwardRef=function(e){return{$$typeof:Qh,render:e}};_.isValidElement=_l;_.lazy=function(e){return{$$typeof:Jh,_payload:{_status:-1,_result:e},_init:nm}};_.memo=function(e,t){return{$$typeof:Zh,type:e,compare:t===void 0?null:t}};_.startTransition=function(e){var t=ji.transition;ji.transition={};try{e()}finally{ji.transition=t}};_.unstable_act=yf;_.useCallback=function(e,t){return we.current.useCallback(e,t)};_.useContext=function(e){return we.current.useContext(e)};_.useDebugValue=function(){};_.useDeferredValue=function(e){return we.current.useDeferredValue(e)};_.useEffect=function(e,t){return we.current.useEffect(e,t)};_.useId=function(){return we.current.useId()};_.useImperativeHandle=function(e,t,n){return we.current.useImperativeHandle(e,t,n)};_.useInsertionEffect=function(e,t){return we.current.useInsertionEffect(e,t)};_.useLayoutEffect=function(e,t){return we.current.useLayoutEffect(e,t)};_.useMemo=function(e,t){return we.current.useMemo(e,t)};_.useReducer=function(e,t,n){return we.current.useReducer(e,t,n)};_.useRef=function(e){return we.current.useRef(e)};_.useState=function(e){return we.current.useState(e)};_.useSyncExternalStore=function(e,t,n){return we.current.useSyncExternalStore(e,t,n)};_.useTransition=function(){return we.current.useTransition()};_.version="18.3.1";uf.exports=_;var L=uf.exports;const Fl=Wh(L);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var im=L,om=Symbol.for("react.element"),sm=Symbol.for("react.fragment"),lm=Object.prototype.hasOwnProperty,am=im.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,um={key:!0,ref:!0,__self:!0,__source:!0};function vf(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)lm.call(t,r)&&!um.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:om,type:e,key:o,ref:s,props:i,_owner:am.current}}xo.Fragment=sm;xo.jsx=vf;xo.jsxs=vf;af.exports=xo;var d=af.exports,js={},xf={exports:{}},Ne={},wf={exports:{}},Sf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(j,D){var O=j.length;j.push(D);e:for(;0<O;){var N=O-1>>>1,$=j[N];if(0<i($,D))j[N]=D,j[O]=$,O=N;else break e}}function n(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var D=j[0],O=j.pop();if(O!==D){j[0]=O;e:for(var N=0,$=j.length,bt=$>>>1;N<bt;){var Je=2*(N+1)-1,yn=j[Je],Re=Je+1,Gt=j[Re];if(0>i(yn,O))Re<$&&0>i(Gt,yn)?(j[N]=Gt,j[Re]=O,N=Re):(j[N]=yn,j[Je]=O,N=Je);else if(Re<$&&0>i(Gt,O))j[N]=Gt,j[Re]=O,N=Re;else break e}}return D}function i(j,D){var O=j.sortIndex-D.sortIndex;return O!==0?O:j.id-D.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],f=1,c=null,p=3,g=!1,v=!1,x=!1,C=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(j){for(var D=n(u);D!==null;){if(D.callback===null)r(u);else if(D.startTime<=j)r(u),D.sortIndex=D.expirationTime,t(a,D);else break;D=n(u)}}function w(j){if(x=!1,m(j),!v)if(n(a)!==null)v=!0,Y(S);else{var D=n(u);D!==null&&_e(w,D.startTime-j)}}function S(j,D){v=!1,x&&(x=!1,y(P),P=-1),g=!0;var O=p;try{for(m(D),c=n(a);c!==null&&(!(c.expirationTime>D)||j&&!q());){var N=c.callback;if(typeof N=="function"){c.callback=null,p=c.priorityLevel;var $=N(c.expirationTime<=D);D=e.unstable_now(),typeof $=="function"?c.callback=$:c===n(a)&&r(a),m(D)}else r(a);c=n(a)}if(c!==null)var bt=!0;else{var Je=n(u);Je!==null&&_e(w,Je.startTime-D),bt=!1}return bt}finally{c=null,p=O,g=!1}}var E=!1,T=null,P=-1,A=5,V=-1;function q(){return!(e.unstable_now()-V<A)}function le(){if(T!==null){var j=e.unstable_now();V=j;var D=!0;try{D=T(!0,j)}finally{D?ge():(E=!1,T=null)}}else E=!1}var ge;if(typeof h=="function")ge=function(){h(le)};else if(typeof MessageChannel<"u"){var ie=new MessageChannel,xt=ie.port2;ie.port1.onmessage=le,ge=function(){xt.postMessage(null)}}else ge=function(){C(le,0)};function Y(j){T=j,E||(E=!0,ge())}function _e(j,D){P=C(function(){j(e.unstable_now())},D)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(j){j.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,Y(S))},e.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<j?Math.floor(1e3/j):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(j){switch(p){case 1:case 2:case 3:var D=3;break;default:D=p}var O=p;p=D;try{return j()}finally{p=O}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(j,D){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var O=p;p=j;try{return D()}finally{p=O}},e.unstable_scheduleCallback=function(j,D,O){var N=e.unstable_now();switch(typeof O=="object"&&O!==null?(O=O.delay,O=typeof O=="number"&&0<O?N+O:N):O=N,j){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=O+$,j={id:f++,callback:D,priorityLevel:j,startTime:O,expirationTime:$,sortIndex:-1},O>N?(j.sortIndex=O,t(u,j),n(a)===null&&j===n(u)&&(x?(y(P),P=-1):x=!0,_e(w,O-N))):(j.sortIndex=$,t(a,j),v||g||(v=!0,Y(S))),j},e.unstable_shouldYield=q,e.unstable_wrapCallback=function(j){var D=p;return function(){var O=p;p=D;try{return j.apply(this,arguments)}finally{p=O}}}})(Sf);wf.exports=Sf;var cm=wf.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fm=L,De=cm;function k(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Cf=new Set,jr={};function pn(e,t){Un(e,t),Un(e+"Capture",t)}function Un(e,t){for(jr[e]=t,e=0;e<t.length;e++)Cf.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Rs=Object.prototype.hasOwnProperty,dm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ja={},qa={};function pm(e){return Rs.call(qa,e)?!0:Rs.call(Ja,e)?!1:dm.test(e)?qa[e]=!0:(Ja[e]=!0,!1)}function hm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function mm(e,t,n,r){if(t===null||typeof t>"u"||hm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var Il=/[\-:]([a-z])/g;function Bl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Il,Bl);ce[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Il,Bl);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Il,Bl);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function zl(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(mm(t,n,i,r)&&(n=null),r||i===null?pm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var vt=fm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,si=Symbol.for("react.element"),xn=Symbol.for("react.portal"),wn=Symbol.for("react.fragment"),Ul=Symbol.for("react.strict_mode"),Ls=Symbol.for("react.profiler"),kf=Symbol.for("react.provider"),Pf=Symbol.for("react.context"),Wl=Symbol.for("react.forward_ref"),Ms=Symbol.for("react.suspense"),As=Symbol.for("react.suspense_list"),$l=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),Tf=Symbol.for("react.offscreen"),eu=Symbol.iterator;function qn(e){return e===null||typeof e!="object"?null:(e=eu&&e[eu]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,Ho;function ur(e){if(Ho===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ho=t&&t[1]||""}return`
`+Ho+e}var Xo=!1;function bo(e,t){if(!e||Xo)return"";Xo=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Xo=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ur(e):""}function gm(e){switch(e.tag){case 5:return ur(e.type);case 16:return ur("Lazy");case 13:return ur("Suspense");case 19:return ur("SuspenseList");case 0:case 2:case 15:return e=bo(e.type,!1),e;case 11:return e=bo(e.type.render,!1),e;case 1:return e=bo(e.type,!0),e;default:return""}}function Ds(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case wn:return"Fragment";case xn:return"Portal";case Ls:return"Profiler";case Ul:return"StrictMode";case Ms:return"Suspense";case As:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Pf:return(e.displayName||"Context")+".Consumer";case kf:return(e._context.displayName||"Context")+".Provider";case Wl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case $l:return t=e.displayName||null,t!==null?t:Ds(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return Ds(e(t))}catch{}}return null}function ym(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ds(t);case 8:return t===Ul?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function It(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Ef(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vm(e){var t=Ef(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function li(e){e._valueTracker||(e._valueTracker=vm(e))}function jf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Ef(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function zi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Vs(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=It(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Rf(e,t){t=t.checked,t!=null&&zl(e,"checked",t,!1)}function Ns(e,t){Rf(e,t);var n=It(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Os(e,t.type,n):t.hasOwnProperty("defaultValue")&&Os(e,t.type,It(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Os(e,t,n){(t!=="number"||zi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var cr=Array.isArray;function On(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+It(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function _s(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(k(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ru(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(k(92));if(cr(n)){if(1<n.length)throw Error(k(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:It(n)}}function Lf(e,t){var n=It(t.value),r=It(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function iu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Mf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Fs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Mf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ai,Af=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ai=ai||document.createElement("div"),ai.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ai.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Rr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var hr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},xm=["Webkit","ms","Moz","O"];Object.keys(hr).forEach(function(e){xm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),hr[t]=hr[e]})});function Df(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||hr.hasOwnProperty(e)&&hr[e]?(""+t).trim():t+"px"}function Vf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Df(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var wm=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Is(e,t){if(t){if(wm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(k(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(k(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(k(61))}if(t.style!=null&&typeof t.style!="object")throw Error(k(62))}}function Bs(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zs=null;function Hl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Us=null,_n=null,Fn=null;function ou(e){if(e=Jr(e)){if(typeof Us!="function")throw Error(k(280));var t=e.stateNode;t&&(t=Po(t),Us(e.stateNode,e.type,t))}}function Nf(e){_n?Fn?Fn.push(e):Fn=[e]:_n=e}function Of(){if(_n){var e=_n,t=Fn;if(Fn=_n=null,ou(e),t)for(e=0;e<t.length;e++)ou(t[e])}}function _f(e,t){return e(t)}function Ff(){}var Go=!1;function If(e,t,n){if(Go)return e(t,n);Go=!0;try{return _f(e,t,n)}finally{Go=!1,(_n!==null||Fn!==null)&&(Ff(),Of())}}function Lr(e,t){var n=e.stateNode;if(n===null)return null;var r=Po(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(k(231,t,typeof n));return n}var Ws=!1;if(pt)try{var er={};Object.defineProperty(er,"passive",{get:function(){Ws=!0}}),window.addEventListener("test",er,er),window.removeEventListener("test",er,er)}catch{Ws=!1}function Sm(e,t,n,r,i,o,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var mr=!1,Ui=null,Wi=!1,$s=null,Cm={onError:function(e){mr=!0,Ui=e}};function km(e,t,n,r,i,o,s,l,a){mr=!1,Ui=null,Sm.apply(Cm,arguments)}function Pm(e,t,n,r,i,o,s,l,a){if(km.apply(this,arguments),mr){if(mr){var u=Ui;mr=!1,Ui=null}else throw Error(k(198));Wi||(Wi=!0,$s=u)}}function hn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Bf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function su(e){if(hn(e)!==e)throw Error(k(188))}function Tm(e){var t=e.alternate;if(!t){if(t=hn(e),t===null)throw Error(k(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return su(i),e;if(o===r)return su(i),t;o=o.sibling}throw Error(k(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(k(189))}}if(n.alternate!==r)throw Error(k(190))}if(n.tag!==3)throw Error(k(188));return n.stateNode.current===n?e:t}function zf(e){return e=Tm(e),e!==null?Uf(e):null}function Uf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Uf(e);if(t!==null)return t;e=e.sibling}return null}var Wf=De.unstable_scheduleCallback,lu=De.unstable_cancelCallback,Em=De.unstable_shouldYield,jm=De.unstable_requestPaint,Z=De.unstable_now,Rm=De.unstable_getCurrentPriorityLevel,Xl=De.unstable_ImmediatePriority,$f=De.unstable_UserBlockingPriority,$i=De.unstable_NormalPriority,Lm=De.unstable_LowPriority,Hf=De.unstable_IdlePriority,wo=null,nt=null;function Mm(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(wo,e,void 0,(e.current.flags&128)===128)}catch{}}var Qe=Math.clz32?Math.clz32:Vm,Am=Math.log,Dm=Math.LN2;function Vm(e){return e>>>=0,e===0?32:31-(Am(e)/Dm|0)|0}var ui=64,ci=4194304;function fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Hi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=fr(l):(o&=s,o!==0&&(r=fr(o)))}else s=n&~i,s!==0?r=fr(s):o!==0&&(r=fr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Qe(t),i=1<<n,r|=e[n],t&=~i;return r}function Nm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Om(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Qe(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=Nm(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function Hs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Xf(){var e=ui;return ui<<=1,!(ui&4194240)&&(ui=64),e}function Ko(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Yr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Qe(t),e[t]=n}function _m(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Qe(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function bl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Qe(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var I=0;function bf(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Gf,Gl,Kf,Qf,Yf,Xs=!1,fi=[],Lt=null,Mt=null,At=null,Mr=new Map,Ar=new Map,Tt=[],Fm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function au(e,t){switch(e){case"focusin":case"focusout":Lt=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":At=null;break;case"pointerover":case"pointerout":Mr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ar.delete(t.pointerId)}}function tr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Jr(t),t!==null&&Gl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Im(e,t,n,r,i){switch(t){case"focusin":return Lt=tr(Lt,e,t,n,r,i),!0;case"dragenter":return Mt=tr(Mt,e,t,n,r,i),!0;case"mouseover":return At=tr(At,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Mr.set(o,tr(Mr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Ar.set(o,tr(Ar.get(o)||null,e,t,n,r,i)),!0}return!1}function Zf(e){var t=en(e.target);if(t!==null){var n=hn(t);if(n!==null){if(t=n.tag,t===13){if(t=Bf(n),t!==null){e.blockedOn=t,Yf(e.priority,function(){Kf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ri(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=bs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);zs=r,n.target.dispatchEvent(r),zs=null}else return t=Jr(n),t!==null&&Gl(t),e.blockedOn=n,!1;t.shift()}return!0}function uu(e,t,n){Ri(e)&&n.delete(t)}function Bm(){Xs=!1,Lt!==null&&Ri(Lt)&&(Lt=null),Mt!==null&&Ri(Mt)&&(Mt=null),At!==null&&Ri(At)&&(At=null),Mr.forEach(uu),Ar.forEach(uu)}function nr(e,t){e.blockedOn===t&&(e.blockedOn=null,Xs||(Xs=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,Bm)))}function Dr(e){function t(i){return nr(i,e)}if(0<fi.length){nr(fi[0],e);for(var n=1;n<fi.length;n++){var r=fi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Lt!==null&&nr(Lt,e),Mt!==null&&nr(Mt,e),At!==null&&nr(At,e),Mr.forEach(t),Ar.forEach(t),n=0;n<Tt.length;n++)r=Tt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&(n=Tt[0],n.blockedOn===null);)Zf(n),n.blockedOn===null&&Tt.shift()}var In=vt.ReactCurrentBatchConfig,Xi=!0;function zm(e,t,n,r){var i=I,o=In.transition;In.transition=null;try{I=1,Kl(e,t,n,r)}finally{I=i,In.transition=o}}function Um(e,t,n,r){var i=I,o=In.transition;In.transition=null;try{I=4,Kl(e,t,n,r)}finally{I=i,In.transition=o}}function Kl(e,t,n,r){if(Xi){var i=bs(e,t,n,r);if(i===null)is(e,t,r,bi,n),au(e,r);else if(Im(i,e,t,n,r))r.stopPropagation();else if(au(e,r),t&4&&-1<Fm.indexOf(e)){for(;i!==null;){var o=Jr(i);if(o!==null&&Gf(o),o=bs(e,t,n,r),o===null&&is(e,t,r,bi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else is(e,t,r,null,n)}}var bi=null;function bs(e,t,n,r){if(bi=null,e=Hl(r),e=en(e),e!==null)if(t=hn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Bf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return bi=e,null}function Jf(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Rm()){case Xl:return 1;case $f:return 4;case $i:case Lm:return 16;case Hf:return 536870912;default:return 16}default:return 16}}var jt=null,Ql=null,Li=null;function qf(){if(Li)return Li;var e,t=Ql,n=t.length,r,i="value"in jt?jt.value:jt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Li=i.slice(e,1<r?1-r:void 0)}function Mi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function di(){return!0}function cu(){return!1}function Oe(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?di:cu,this.isPropagationStopped=cu,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=di)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=di)},persist:function(){},isPersistent:di}),t}var Zn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yl=Oe(Zn),Zr=K({},Zn,{view:0,detail:0}),Wm=Oe(Zr),Qo,Yo,rr,So=K({},Zr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Zl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==rr&&(rr&&e.type==="mousemove"?(Qo=e.screenX-rr.screenX,Yo=e.screenY-rr.screenY):Yo=Qo=0,rr=e),Qo)},movementY:function(e){return"movementY"in e?e.movementY:Yo}}),fu=Oe(So),$m=K({},So,{dataTransfer:0}),Hm=Oe($m),Xm=K({},Zr,{relatedTarget:0}),Zo=Oe(Xm),bm=K({},Zn,{animationName:0,elapsedTime:0,pseudoElement:0}),Gm=Oe(bm),Km=K({},Zn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qm=Oe(Km),Ym=K({},Zn,{data:0}),du=Oe(Ym),Zm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function eg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qm[e])?!!t[e]:!1}function Zl(){return eg}var tg=K({},Zr,{key:function(e){if(e.key){var t=Zm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Mi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Zl,charCode:function(e){return e.type==="keypress"?Mi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Mi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ng=Oe(tg),rg=K({},So,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=Oe(rg),ig=K({},Zr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Zl}),og=Oe(ig),sg=K({},Zn,{propertyName:0,elapsedTime:0,pseudoElement:0}),lg=Oe(sg),ag=K({},So,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ug=Oe(ag),cg=[9,13,27,32],Jl=pt&&"CompositionEvent"in window,gr=null;pt&&"documentMode"in document&&(gr=document.documentMode);var fg=pt&&"TextEvent"in window&&!gr,ed=pt&&(!Jl||gr&&8<gr&&11>=gr),hu=" ",mu=!1;function td(e,t){switch(e){case"keyup":return cg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sn=!1;function dg(e,t){switch(e){case"compositionend":return nd(t);case"keypress":return t.which!==32?null:(mu=!0,hu);case"textInput":return e=t.data,e===hu&&mu?null:e;default:return null}}function pg(e,t){if(Sn)return e==="compositionend"||!Jl&&td(e,t)?(e=qf(),Li=Ql=jt=null,Sn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ed&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function rd(e,t,n,r){Nf(r),t=Gi(t,"onChange"),0<t.length&&(n=new Yl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var yr=null,Vr=null;function mg(e){hd(e,0)}function Co(e){var t=Pn(e);if(jf(t))return e}function gg(e,t){if(e==="change")return t}var id=!1;if(pt){var Jo;if(pt){var qo="oninput"in document;if(!qo){var yu=document.createElement("div");yu.setAttribute("oninput","return;"),qo=typeof yu.oninput=="function"}Jo=qo}else Jo=!1;id=Jo&&(!document.documentMode||9<document.documentMode)}function vu(){yr&&(yr.detachEvent("onpropertychange",od),Vr=yr=null)}function od(e){if(e.propertyName==="value"&&Co(Vr)){var t=[];rd(t,Vr,e,Hl(e)),If(mg,t)}}function yg(e,t,n){e==="focusin"?(vu(),yr=t,Vr=n,yr.attachEvent("onpropertychange",od)):e==="focusout"&&vu()}function vg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Co(Vr)}function xg(e,t){if(e==="click")return Co(t)}function wg(e,t){if(e==="input"||e==="change")return Co(t)}function Sg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:Sg;function Nr(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Rs.call(t,i)||!Ze(e[i],t[i]))return!1}return!0}function xu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wu(e,t){var n=xu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=xu(n)}}function sd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function ld(){for(var e=window,t=zi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=zi(e.document)}return t}function ql(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Cg(e){var t=ld(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sd(n.ownerDocument.documentElement,n)){if(r!==null&&ql(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=wu(n,o);var s=wu(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var kg=pt&&"documentMode"in document&&11>=document.documentMode,Cn=null,Gs=null,vr=null,Ks=!1;function Su(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ks||Cn==null||Cn!==zi(r)||(r=Cn,"selectionStart"in r&&ql(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vr&&Nr(vr,r)||(vr=r,r=Gi(Gs,"onSelect"),0<r.length&&(t=new Yl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cn)))}function pi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kn={animationend:pi("Animation","AnimationEnd"),animationiteration:pi("Animation","AnimationIteration"),animationstart:pi("Animation","AnimationStart"),transitionend:pi("Transition","TransitionEnd")},es={},ad={};pt&&(ad=document.createElement("div").style,"AnimationEvent"in window||(delete kn.animationend.animation,delete kn.animationiteration.animation,delete kn.animationstart.animation),"TransitionEvent"in window||delete kn.transitionend.transition);function ko(e){if(es[e])return es[e];if(!kn[e])return e;var t=kn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ad)return es[e]=t[n];return e}var ud=ko("animationend"),cd=ko("animationiteration"),fd=ko("animationstart"),dd=ko("transitionend"),pd=new Map,Cu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Wt(e,t){pd.set(e,t),pn(t,[e])}for(var ts=0;ts<Cu.length;ts++){var ns=Cu[ts],Pg=ns.toLowerCase(),Tg=ns[0].toUpperCase()+ns.slice(1);Wt(Pg,"on"+Tg)}Wt(ud,"onAnimationEnd");Wt(cd,"onAnimationIteration");Wt(fd,"onAnimationStart");Wt("dblclick","onDoubleClick");Wt("focusin","onFocus");Wt("focusout","onBlur");Wt(dd,"onTransitionEnd");Un("onMouseEnter",["mouseout","mouseover"]);Un("onMouseLeave",["mouseout","mouseover"]);Un("onPointerEnter",["pointerout","pointerover"]);Un("onPointerLeave",["pointerout","pointerover"]);pn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));pn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));pn("onBeforeInput",["compositionend","keypress","textInput","paste"]);pn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));pn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Eg=new Set("cancel close invalid load scroll toggle".split(" ").concat(dr));function ku(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Pm(r,t,void 0,e),e.currentTarget=null}function hd(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;ku(i,l,u),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;ku(i,l,u),o=a}}}if(Wi)throw e=$s,Wi=!1,$s=null,e}function z(e,t){var n=t[qs];n===void 0&&(n=t[qs]=new Set);var r=e+"__bubble";n.has(r)||(md(t,e,2,!1),n.add(r))}function rs(e,t,n){var r=0;t&&(r|=4),md(n,e,r,t)}var hi="_reactListening"+Math.random().toString(36).slice(2);function Or(e){if(!e[hi]){e[hi]=!0,Cf.forEach(function(n){n!=="selectionchange"&&(Eg.has(n)||rs(n,!1,e),rs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[hi]||(t[hi]=!0,rs("selectionchange",!1,t))}}function md(e,t,n,r){switch(Jf(t)){case 1:var i=zm;break;case 4:i=Um;break;default:i=Kl}n=i.bind(null,t,n,e),i=void 0,!Ws||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function is(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=en(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}If(function(){var u=o,f=Hl(n),c=[];e:{var p=pd.get(e);if(p!==void 0){var g=Yl,v=e;switch(e){case"keypress":if(Mi(n)===0)break e;case"keydown":case"keyup":g=ng;break;case"focusin":v="focus",g=Zo;break;case"focusout":v="blur",g=Zo;break;case"beforeblur":case"afterblur":g=Zo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Hm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=og;break;case ud:case cd:case fd:g=Gm;break;case dd:g=lg;break;case"scroll":g=Wm;break;case"wheel":g=ug;break;case"copy":case"cut":case"paste":g=Qm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=pu}var x=(t&4)!==0,C=!x&&e==="scroll",y=x?p!==null?p+"Capture":null:p;x=[];for(var h=u,m;h!==null;){m=h;var w=m.stateNode;if(m.tag===5&&w!==null&&(m=w,y!==null&&(w=Lr(h,y),w!=null&&x.push(_r(h,w,m)))),C)break;h=h.return}0<x.length&&(p=new g(p,v,null,n,f),c.push({event:p,listeners:x}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",p&&n!==zs&&(v=n.relatedTarget||n.fromElement)&&(en(v)||v[ht]))break e;if((g||p)&&(p=f.window===f?f:(p=f.ownerDocument)?p.defaultView||p.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?en(v):null,v!==null&&(C=hn(v),v!==C||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(x=fu,w="onMouseLeave",y="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=pu,w="onPointerLeave",y="onPointerEnter",h="pointer"),C=g==null?p:Pn(g),m=v==null?p:Pn(v),p=new x(w,h+"leave",g,n,f),p.target=C,p.relatedTarget=m,w=null,en(f)===u&&(x=new x(y,h+"enter",v,n,f),x.target=m,x.relatedTarget=C,w=x),C=w,g&&v)t:{for(x=g,y=v,h=0,m=x;m;m=vn(m))h++;for(m=0,w=y;w;w=vn(w))m++;for(;0<h-m;)x=vn(x),h--;for(;0<m-h;)y=vn(y),m--;for(;h--;){if(x===y||y!==null&&x===y.alternate)break t;x=vn(x),y=vn(y)}x=null}else x=null;g!==null&&Pu(c,p,g,x,!1),v!==null&&C!==null&&Pu(c,C,v,x,!0)}}e:{if(p=u?Pn(u):window,g=p.nodeName&&p.nodeName.toLowerCase(),g==="select"||g==="input"&&p.type==="file")var S=gg;else if(gu(p))if(id)S=wg;else{S=vg;var E=yg}else(g=p.nodeName)&&g.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(S=xg);if(S&&(S=S(e,u))){rd(c,S,n,f);break e}E&&E(e,p,u),e==="focusout"&&(E=p._wrapperState)&&E.controlled&&p.type==="number"&&Os(p,"number",p.value)}switch(E=u?Pn(u):window,e){case"focusin":(gu(E)||E.contentEditable==="true")&&(Cn=E,Gs=u,vr=null);break;case"focusout":vr=Gs=Cn=null;break;case"mousedown":Ks=!0;break;case"contextmenu":case"mouseup":case"dragend":Ks=!1,Su(c,n,f);break;case"selectionchange":if(kg)break;case"keydown":case"keyup":Su(c,n,f)}var T;if(Jl)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Sn?td(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(ed&&n.locale!=="ko"&&(Sn||P!=="onCompositionStart"?P==="onCompositionEnd"&&Sn&&(T=qf()):(jt=f,Ql="value"in jt?jt.value:jt.textContent,Sn=!0)),E=Gi(u,P),0<E.length&&(P=new du(P,e,null,n,f),c.push({event:P,listeners:E}),T?P.data=T:(T=nd(n),T!==null&&(P.data=T)))),(T=fg?dg(e,n):pg(e,n))&&(u=Gi(u,"onBeforeInput"),0<u.length&&(f=new du("onBeforeInput","beforeinput",null,n,f),c.push({event:f,listeners:u}),f.data=T))}hd(c,t)})}function _r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Lr(e,n),o!=null&&r.unshift(_r(e,o,i)),o=Lr(e,t),o!=null&&r.push(_r(e,o,i))),e=e.return}return r}function vn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Pu(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Lr(n,o),a!=null&&s.unshift(_r(n,a,l))):i||(a=Lr(n,o),a!=null&&s.push(_r(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var jg=/\r\n?/g,Rg=/\u0000|\uFFFD/g;function Tu(e){return(typeof e=="string"?e:""+e).replace(jg,`
`).replace(Rg,"")}function mi(e,t,n){if(t=Tu(t),Tu(e)!==t&&n)throw Error(k(425))}function Ki(){}var Qs=null,Ys=null;function Zs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Js=typeof setTimeout=="function"?setTimeout:void 0,Lg=typeof clearTimeout=="function"?clearTimeout:void 0,Eu=typeof Promise=="function"?Promise:void 0,Mg=typeof queueMicrotask=="function"?queueMicrotask:typeof Eu<"u"?function(e){return Eu.resolve(null).then(e).catch(Ag)}:Js;function Ag(e){setTimeout(function(){throw e})}function os(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Dr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Dr(t)}function Dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ju(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Jn=Math.random().toString(36).slice(2),tt="__reactFiber$"+Jn,Fr="__reactProps$"+Jn,ht="__reactContainer$"+Jn,qs="__reactEvents$"+Jn,Dg="__reactListeners$"+Jn,Vg="__reactHandles$"+Jn;function en(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ht]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ju(e);e!==null;){if(n=e[tt])return n;e=ju(e)}return t}e=n,n=e.parentNode}return null}function Jr(e){return e=e[tt]||e[ht],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Pn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(k(33))}function Po(e){return e[Fr]||null}var el=[],Tn=-1;function $t(e){return{current:e}}function U(e){0>Tn||(e.current=el[Tn],el[Tn]=null,Tn--)}function B(e,t){Tn++,el[Tn]=e.current,e.current=t}var Bt={},me=$t(Bt),Pe=$t(!1),an=Bt;function Wn(e,t){var n=e.type.contextTypes;if(!n)return Bt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Te(e){return e=e.childContextTypes,e!=null}function Qi(){U(Pe),U(me)}function Ru(e,t,n){if(me.current!==Bt)throw Error(k(168));B(me,t),B(Pe,n)}function gd(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(k(108,ym(e)||"Unknown",i));return K({},n,r)}function Yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Bt,an=me.current,B(me,e),B(Pe,Pe.current),!0}function Lu(e,t,n){var r=e.stateNode;if(!r)throw Error(k(169));n?(e=gd(e,t,an),r.__reactInternalMemoizedMergedChildContext=e,U(Pe),U(me),B(me,e)):U(Pe),B(Pe,n)}var st=null,To=!1,ss=!1;function yd(e){st===null?st=[e]:st.push(e)}function Ng(e){To=!0,yd(e)}function Ht(){if(!ss&&st!==null){ss=!0;var e=0,t=I;try{var n=st;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}st=null,To=!1}catch(i){throw st!==null&&(st=st.slice(e+1)),Wf(Xl,Ht),i}finally{I=t,ss=!1}}return null}var En=[],jn=0,Zi=null,Ji=0,Be=[],ze=0,un=null,lt=1,at="";function Yt(e,t){En[jn++]=Ji,En[jn++]=Zi,Zi=e,Ji=t}function vd(e,t,n){Be[ze++]=lt,Be[ze++]=at,Be[ze++]=un,un=e;var r=lt;e=at;var i=32-Qe(r)-1;r&=~(1<<i),n+=1;var o=32-Qe(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,lt=1<<32-Qe(t)+i|n<<i|r,at=o+e}else lt=1<<o|n<<i|r,at=e}function ea(e){e.return!==null&&(Yt(e,1),vd(e,1,0))}function ta(e){for(;e===Zi;)Zi=En[--jn],En[jn]=null,Ji=En[--jn],En[jn]=null;for(;e===un;)un=Be[--ze],Be[ze]=null,at=Be[--ze],Be[ze]=null,lt=Be[--ze],Be[ze]=null}var Ae=null,Me=null,H=!1,Ke=null;function xd(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Mu(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ae=e,Me=Dt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ae=e,Me=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=un!==null?{id:lt,overflow:at}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ae=e,Me=null,!0):!1;default:return!1}}function tl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nl(e){if(H){var t=Me;if(t){var n=t;if(!Mu(e,t)){if(tl(e))throw Error(k(418));t=Dt(n.nextSibling);var r=Ae;t&&Mu(e,t)?xd(r,n):(e.flags=e.flags&-4097|2,H=!1,Ae=e)}}else{if(tl(e))throw Error(k(418));e.flags=e.flags&-4097|2,H=!1,Ae=e}}}function Au(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ae=e}function gi(e){if(e!==Ae)return!1;if(!H)return Au(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Zs(e.type,e.memoizedProps)),t&&(t=Me)){if(tl(e))throw wd(),Error(k(418));for(;t;)xd(e,t),t=Dt(t.nextSibling)}if(Au(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(k(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Me=Dt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Me=null}}else Me=Ae?Dt(e.stateNode.nextSibling):null;return!0}function wd(){for(var e=Me;e;)e=Dt(e.nextSibling)}function $n(){Me=Ae=null,H=!1}function na(e){Ke===null?Ke=[e]:Ke.push(e)}var Og=vt.ReactCurrentBatchConfig;function ir(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(k(309));var r=n.stateNode}if(!r)throw Error(k(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(k(284));if(!n._owner)throw Error(k(290,e))}return e}function yi(e,t){throw e=Object.prototype.toString.call(t),Error(k(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Du(e){var t=e._init;return t(e._payload)}function Sd(e){function t(y,h){if(e){var m=y.deletions;m===null?(y.deletions=[h],y.flags|=16):m.push(h)}}function n(y,h){if(!e)return null;for(;h!==null;)t(y,h),h=h.sibling;return null}function r(y,h){for(y=new Map;h!==null;)h.key!==null?y.set(h.key,h):y.set(h.index,h),h=h.sibling;return y}function i(y,h){return y=_t(y,h),y.index=0,y.sibling=null,y}function o(y,h,m){return y.index=m,e?(m=y.alternate,m!==null?(m=m.index,m<h?(y.flags|=2,h):m):(y.flags|=2,h)):(y.flags|=1048576,h)}function s(y){return e&&y.alternate===null&&(y.flags|=2),y}function l(y,h,m,w){return h===null||h.tag!==6?(h=ps(m,y.mode,w),h.return=y,h):(h=i(h,m),h.return=y,h)}function a(y,h,m,w){var S=m.type;return S===wn?f(y,h,m.props.children,w,m.key):h!==null&&(h.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ct&&Du(S)===h.type)?(w=i(h,m.props),w.ref=ir(y,h,m),w.return=y,w):(w=Fi(m.type,m.key,m.props,null,y.mode,w),w.ref=ir(y,h,m),w.return=y,w)}function u(y,h,m,w){return h===null||h.tag!==4||h.stateNode.containerInfo!==m.containerInfo||h.stateNode.implementation!==m.implementation?(h=hs(m,y.mode,w),h.return=y,h):(h=i(h,m.children||[]),h.return=y,h)}function f(y,h,m,w,S){return h===null||h.tag!==7?(h=sn(m,y.mode,w,S),h.return=y,h):(h=i(h,m),h.return=y,h)}function c(y,h,m){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ps(""+h,y.mode,m),h.return=y,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case si:return m=Fi(h.type,h.key,h.props,null,y.mode,m),m.ref=ir(y,null,h),m.return=y,m;case xn:return h=hs(h,y.mode,m),h.return=y,h;case Ct:var w=h._init;return c(y,w(h._payload),m)}if(cr(h)||qn(h))return h=sn(h,y.mode,m,null),h.return=y,h;yi(y,h)}return null}function p(y,h,m,w){var S=h!==null?h.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return S!==null?null:l(y,h,""+m,w);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case si:return m.key===S?a(y,h,m,w):null;case xn:return m.key===S?u(y,h,m,w):null;case Ct:return S=m._init,p(y,h,S(m._payload),w)}if(cr(m)||qn(m))return S!==null?null:f(y,h,m,w,null);yi(y,m)}return null}function g(y,h,m,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return y=y.get(m)||null,l(h,y,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case si:return y=y.get(w.key===null?m:w.key)||null,a(h,y,w,S);case xn:return y=y.get(w.key===null?m:w.key)||null,u(h,y,w,S);case Ct:var E=w._init;return g(y,h,m,E(w._payload),S)}if(cr(w)||qn(w))return y=y.get(m)||null,f(h,y,w,S,null);yi(h,w)}return null}function v(y,h,m,w){for(var S=null,E=null,T=h,P=h=0,A=null;T!==null&&P<m.length;P++){T.index>P?(A=T,T=null):A=T.sibling;var V=p(y,T,m[P],w);if(V===null){T===null&&(T=A);break}e&&T&&V.alternate===null&&t(y,T),h=o(V,h,P),E===null?S=V:E.sibling=V,E=V,T=A}if(P===m.length)return n(y,T),H&&Yt(y,P),S;if(T===null){for(;P<m.length;P++)T=c(y,m[P],w),T!==null&&(h=o(T,h,P),E===null?S=T:E.sibling=T,E=T);return H&&Yt(y,P),S}for(T=r(y,T);P<m.length;P++)A=g(T,y,P,m[P],w),A!==null&&(e&&A.alternate!==null&&T.delete(A.key===null?P:A.key),h=o(A,h,P),E===null?S=A:E.sibling=A,E=A);return e&&T.forEach(function(q){return t(y,q)}),H&&Yt(y,P),S}function x(y,h,m,w){var S=qn(m);if(typeof S!="function")throw Error(k(150));if(m=S.call(m),m==null)throw Error(k(151));for(var E=S=null,T=h,P=h=0,A=null,V=m.next();T!==null&&!V.done;P++,V=m.next()){T.index>P?(A=T,T=null):A=T.sibling;var q=p(y,T,V.value,w);if(q===null){T===null&&(T=A);break}e&&T&&q.alternate===null&&t(y,T),h=o(q,h,P),E===null?S=q:E.sibling=q,E=q,T=A}if(V.done)return n(y,T),H&&Yt(y,P),S;if(T===null){for(;!V.done;P++,V=m.next())V=c(y,V.value,w),V!==null&&(h=o(V,h,P),E===null?S=V:E.sibling=V,E=V);return H&&Yt(y,P),S}for(T=r(y,T);!V.done;P++,V=m.next())V=g(T,y,P,V.value,w),V!==null&&(e&&V.alternate!==null&&T.delete(V.key===null?P:V.key),h=o(V,h,P),E===null?S=V:E.sibling=V,E=V);return e&&T.forEach(function(le){return t(y,le)}),H&&Yt(y,P),S}function C(y,h,m,w){if(typeof m=="object"&&m!==null&&m.type===wn&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case si:e:{for(var S=m.key,E=h;E!==null;){if(E.key===S){if(S=m.type,S===wn){if(E.tag===7){n(y,E.sibling),h=i(E,m.props.children),h.return=y,y=h;break e}}else if(E.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Ct&&Du(S)===E.type){n(y,E.sibling),h=i(E,m.props),h.ref=ir(y,E,m),h.return=y,y=h;break e}n(y,E);break}else t(y,E);E=E.sibling}m.type===wn?(h=sn(m.props.children,y.mode,w,m.key),h.return=y,y=h):(w=Fi(m.type,m.key,m.props,null,y.mode,w),w.ref=ir(y,h,m),w.return=y,y=w)}return s(y);case xn:e:{for(E=m.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===m.containerInfo&&h.stateNode.implementation===m.implementation){n(y,h.sibling),h=i(h,m.children||[]),h.return=y,y=h;break e}else{n(y,h);break}else t(y,h);h=h.sibling}h=hs(m,y.mode,w),h.return=y,y=h}return s(y);case Ct:return E=m._init,C(y,h,E(m._payload),w)}if(cr(m))return v(y,h,m,w);if(qn(m))return x(y,h,m,w);yi(y,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,h!==null&&h.tag===6?(n(y,h.sibling),h=i(h,m),h.return=y,y=h):(n(y,h),h=ps(m,y.mode,w),h.return=y,y=h),s(y)):n(y,h)}return C}var Hn=Sd(!0),Cd=Sd(!1),qi=$t(null),eo=null,Rn=null,ra=null;function ia(){ra=Rn=eo=null}function oa(e){var t=qi.current;U(qi),e._currentValue=t}function rl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Bn(e,t){eo=e,ra=Rn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ke=!0),e.firstContext=null)}function $e(e){var t=e._currentValue;if(ra!==e)if(e={context:e,memoizedValue:t,next:null},Rn===null){if(eo===null)throw Error(k(308));Rn=e,eo.dependencies={lanes:0,firstContext:e}}else Rn=Rn.next=e;return t}var tn=null;function sa(e){tn===null?tn=[e]:tn.push(e)}function kd(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,sa(t)):(n.next=i.next,i.next=n),t.interleaved=n,mt(e,r)}function mt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kt=!1;function la(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Pd(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ct(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,F&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,mt(e,n)}return i=r.interleaved,i===null?(t.next=t,sa(r)):(t.next=i.next,i.next=t),r.interleaved=t,mt(e,n)}function Ai(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,bl(e,n)}}function Vu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function to(e,t,n,r){var i=e.updateQueue;kt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?o=u:s.next=u,s=a;var f=e.alternate;f!==null&&(f=f.updateQueue,l=f.lastBaseUpdate,l!==s&&(l===null?f.firstBaseUpdate=u:l.next=u,f.lastBaseUpdate=a))}if(o!==null){var c=i.baseState;s=0,f=u=a=null,l=o;do{var p=l.lane,g=l.eventTime;if((r&p)===p){f!==null&&(f=f.next={eventTime:g,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var v=e,x=l;switch(p=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){c=v.call(g,c,p);break e}c=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,p=typeof v=="function"?v.call(g,c,p):v,p==null)break e;c=K({},c,p);break e;case 2:kt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[l]:p.push(l))}else g={eventTime:g,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},f===null?(u=f=g,a=c):f=f.next=g,s|=p;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;p=l,l=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(f===null&&(a=c),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);fn|=s,e.lanes=s,e.memoizedState=c}}function Nu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(k(191,i));i.call(r)}}}var qr={},rt=$t(qr),Ir=$t(qr),Br=$t(qr);function nn(e){if(e===qr)throw Error(k(174));return e}function aa(e,t){switch(B(Br,t),B(Ir,e),B(rt,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Fs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Fs(t,e)}U(rt),B(rt,t)}function Xn(){U(rt),U(Ir),U(Br)}function Td(e){nn(Br.current);var t=nn(rt.current),n=Fs(t,e.type);t!==n&&(B(Ir,e),B(rt,n))}function ua(e){Ir.current===e&&(U(rt),U(Ir))}var X=$t(0);function no(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ls=[];function ca(){for(var e=0;e<ls.length;e++)ls[e]._workInProgressVersionPrimary=null;ls.length=0}var Di=vt.ReactCurrentDispatcher,as=vt.ReactCurrentBatchConfig,cn=0,G=null,ne=null,oe=null,ro=!1,xr=!1,zr=0,_g=0;function fe(){throw Error(k(321))}function fa(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function da(e,t,n,r,i,o){if(cn=o,G=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Di.current=e===null||e.memoizedState===null?zg:Ug,e=n(r,i),xr){o=0;do{if(xr=!1,zr=0,25<=o)throw Error(k(301));o+=1,oe=ne=null,t.updateQueue=null,Di.current=Wg,e=n(r,i)}while(xr)}if(Di.current=io,t=ne!==null&&ne.next!==null,cn=0,oe=ne=G=null,ro=!1,t)throw Error(k(300));return e}function pa(){var e=zr!==0;return zr=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?G.memoizedState=oe=e:oe=oe.next=e,oe}function He(){if(ne===null){var e=G.alternate;e=e!==null?e.memoizedState:null}else e=ne.next;var t=oe===null?G.memoizedState:oe.next;if(t!==null)oe=t,ne=e;else{if(e===null)throw Error(k(310));ne=e,e={memoizedState:ne.memoizedState,baseState:ne.baseState,baseQueue:ne.baseQueue,queue:ne.queue,next:null},oe===null?G.memoizedState=oe=e:oe=oe.next=e}return oe}function Ur(e,t){return typeof t=="function"?t(e):t}function us(e){var t=He(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=ne,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,u=o;do{var f=u.lane;if((cn&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var c={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=c,s=r):a=a.next=c,G.lanes|=f,fn|=f}u=u.next}while(u!==null&&u!==o);a===null?s=r:a.next=l,Ze(r,t.memoizedState)||(ke=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,G.lanes|=o,fn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function cs(e){var t=He(),n=t.queue;if(n===null)throw Error(k(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Ze(o,t.memoizedState)||(ke=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ed(){}function jd(e,t){var n=G,r=He(),i=t(),o=!Ze(r.memoizedState,i);if(o&&(r.memoizedState=i,ke=!0),r=r.queue,ha(Md.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,Wr(9,Ld.bind(null,n,r,i,t),void 0,null),se===null)throw Error(k(349));cn&30||Rd(n,t,i)}return i}function Rd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Ld(e,t,n,r){t.value=n,t.getSnapshot=r,Ad(t)&&Dd(e)}function Md(e,t,n){return n(function(){Ad(t)&&Dd(e)})}function Ad(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function Dd(e){var t=mt(e,1);t!==null&&Ye(t,e,1,-1)}function Ou(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ur,lastRenderedState:e},t.queue=e,e=e.dispatch=Bg.bind(null,G,e),[t.memoizedState,e]}function Wr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Vd(){return He().memoizedState}function Vi(e,t,n,r){var i=et();G.flags|=e,i.memoizedState=Wr(1|t,n,void 0,r===void 0?null:r)}function Eo(e,t,n,r){var i=He();r=r===void 0?null:r;var o=void 0;if(ne!==null){var s=ne.memoizedState;if(o=s.destroy,r!==null&&fa(r,s.deps)){i.memoizedState=Wr(t,n,o,r);return}}G.flags|=e,i.memoizedState=Wr(1|t,n,o,r)}function _u(e,t){return Vi(8390656,8,e,t)}function ha(e,t){return Eo(2048,8,e,t)}function Nd(e,t){return Eo(4,2,e,t)}function Od(e,t){return Eo(4,4,e,t)}function _d(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Fd(e,t,n){return n=n!=null?n.concat([e]):null,Eo(4,4,_d.bind(null,t,e),n)}function ma(){}function Id(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Bd(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&fa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function zd(e,t,n){return cn&21?(Ze(n,t)||(n=Xf(),G.lanes|=n,fn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ke=!0),e.memoizedState=n)}function Fg(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=as.transition;as.transition={};try{e(!1),t()}finally{I=n,as.transition=r}}function Ud(){return He().memoizedState}function Ig(e,t,n){var r=Ot(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Wd(e))$d(t,n);else if(n=kd(e,t,n,r),n!==null){var i=xe();Ye(n,e,r,i),Hd(n,t,r)}}function Bg(e,t,n){var r=Ot(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Wd(e))$d(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,Ze(l,s)){var a=t.interleaved;a===null?(i.next=i,sa(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=kd(e,t,i,r),n!==null&&(i=xe(),Ye(n,e,r,i),Hd(n,t,r))}}function Wd(e){var t=e.alternate;return e===G||t!==null&&t===G}function $d(e,t){xr=ro=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hd(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,bl(e,n)}}var io={readContext:$e,useCallback:fe,useContext:fe,useEffect:fe,useImperativeHandle:fe,useInsertionEffect:fe,useLayoutEffect:fe,useMemo:fe,useReducer:fe,useRef:fe,useState:fe,useDebugValue:fe,useDeferredValue:fe,useTransition:fe,useMutableSource:fe,useSyncExternalStore:fe,useId:fe,unstable_isNewReconciler:!1},zg={readContext:$e,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:$e,useEffect:_u,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Vi(4194308,4,_d.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Vi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Vi(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ig.bind(null,G,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:Ou,useDebugValue:ma,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=Ou(!1),t=e[0];return e=Fg.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=G,i=et();if(H){if(n===void 0)throw Error(k(407));n=n()}else{if(n=t(),se===null)throw Error(k(349));cn&30||Rd(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,_u(Md.bind(null,r,o,e),[e]),r.flags|=2048,Wr(9,Ld.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=et(),t=se.identifierPrefix;if(H){var n=at,r=lt;n=(r&~(1<<32-Qe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=zr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=_g++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Ug={readContext:$e,useCallback:Id,useContext:$e,useEffect:ha,useImperativeHandle:Fd,useInsertionEffect:Nd,useLayoutEffect:Od,useMemo:Bd,useReducer:us,useRef:Vd,useState:function(){return us(Ur)},useDebugValue:ma,useDeferredValue:function(e){var t=He();return zd(t,ne.memoizedState,e)},useTransition:function(){var e=us(Ur)[0],t=He().memoizedState;return[e,t]},useMutableSource:Ed,useSyncExternalStore:jd,useId:Ud,unstable_isNewReconciler:!1},Wg={readContext:$e,useCallback:Id,useContext:$e,useEffect:ha,useImperativeHandle:Fd,useInsertionEffect:Nd,useLayoutEffect:Od,useMemo:Bd,useReducer:cs,useRef:Vd,useState:function(){return cs(Ur)},useDebugValue:ma,useDeferredValue:function(e){var t=He();return ne===null?t.memoizedState=e:zd(t,ne.memoizedState,e)},useTransition:function(){var e=cs(Ur)[0],t=He().memoizedState;return[e,t]},useMutableSource:Ed,useSyncExternalStore:jd,useId:Ud,unstable_isNewReconciler:!1};function be(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function il(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var jo={isMounted:function(e){return(e=e._reactInternals)?hn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Ot(e),o=ct(r,i);o.payload=t,n!=null&&(o.callback=n),t=Vt(e,o,i),t!==null&&(Ye(t,e,i,r),Ai(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Ot(e),o=ct(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Vt(e,o,i),t!==null&&(Ye(t,e,i,r),Ai(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=Ot(e),i=ct(n,r);i.tag=2,t!=null&&(i.callback=t),t=Vt(e,i,r),t!==null&&(Ye(t,e,r,n),Ai(t,e,r))}};function Fu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Nr(n,r)||!Nr(i,o):!0}function Xd(e,t,n){var r=!1,i=Bt,o=t.contextType;return typeof o=="object"&&o!==null?o=$e(o):(i=Te(t)?an:me.current,r=t.contextTypes,o=(r=r!=null)?Wn(e,i):Bt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=jo,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function Iu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&jo.enqueueReplaceState(t,t.state,null)}function ol(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},la(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=$e(o):(o=Te(t)?an:me.current,i.context=Wn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(il(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&jo.enqueueReplaceState(i,i.state,null),to(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function bn(e,t){try{var n="",r=t;do n+=gm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function fs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function sl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var $g=typeof WeakMap=="function"?WeakMap:Map;function bd(e,t,n){n=ct(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){so||(so=!0,gl=r),sl(e,t)},n}function Gd(e,t,n){n=ct(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){sl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){sl(e,t),typeof r!="function"&&(Nt===null?Nt=new Set([this]):Nt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Bu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new $g;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=ry.bind(null,e,t,n),t.then(e,e))}function zu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Uu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ct(-1,1),t.tag=2,Vt(n,t,1))),n.lanes|=1),e)}var Hg=vt.ReactCurrentOwner,ke=!1;function ve(e,t,n,r){t.child=e===null?Cd(t,null,n,r):Hn(t,e.child,n,r)}function Wu(e,t,n,r,i){n=n.render;var o=t.ref;return Bn(t,i),r=da(e,t,n,r,o,i),n=pa(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(H&&n&&ea(t),t.flags|=1,ve(e,t,r,i),t.child)}function $u(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!ka(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Kd(e,t,o,r,i)):(e=Fi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Nr,n(s,r)&&e.ref===t.ref)return gt(e,t,i)}return t.flags|=1,e=_t(o,r),e.ref=t.ref,e.return=t,t.child=e}function Kd(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Nr(o,r)&&e.ref===t.ref)if(ke=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(ke=!0);else return t.lanes=e.lanes,gt(e,t,i)}return ll(e,t,n,r,i)}function Qd(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Mn,Le),Le|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Mn,Le),Le|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,B(Mn,Le),Le|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,B(Mn,Le),Le|=r;return ve(e,t,i,n),t.child}function Yd(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ll(e,t,n,r,i){var o=Te(n)?an:me.current;return o=Wn(t,o),Bn(t,i),n=da(e,t,n,r,o,i),r=pa(),e!==null&&!ke?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(H&&r&&ea(t),t.flags|=1,ve(e,t,n,i),t.child)}function Hu(e,t,n,r,i){if(Te(n)){var o=!0;Yi(t)}else o=!1;if(Bn(t,i),t.stateNode===null)Ni(e,t),Xd(t,n,r),ol(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=$e(u):(u=Te(n)?an:me.current,u=Wn(t,u));var f=n.getDerivedStateFromProps,c=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";c||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Iu(t,s,r,u),kt=!1;var p=t.memoizedState;s.state=p,to(t,r,s,i),a=t.memoizedState,l!==r||p!==a||Pe.current||kt?(typeof f=="function"&&(il(t,n,f,r),a=t.memoizedState),(l=kt||Fu(t,n,l,r,p,a,u))?(c||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,Pd(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:be(t.type,l),s.props=u,c=t.pendingProps,p=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=$e(a):(a=Te(n)?an:me.current,a=Wn(t,a));var g=n.getDerivedStateFromProps;(f=typeof g=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==c||p!==a)&&Iu(t,s,r,a),kt=!1,p=t.memoizedState,s.state=p,to(t,r,s,i);var v=t.memoizedState;l!==c||p!==v||Pe.current||kt?(typeof g=="function"&&(il(t,n,g,r),v=t.memoizedState),(u=kt||Fu(t,n,u,r,p,v,a)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return al(e,t,n,r,o,i)}function al(e,t,n,r,i,o){Yd(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Lu(t,n,!1),gt(e,t,o);r=t.stateNode,Hg.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Hn(t,e.child,null,o),t.child=Hn(t,null,l,o)):ve(e,t,l,o),t.memoizedState=r.state,i&&Lu(t,n,!0),t.child}function Zd(e){var t=e.stateNode;t.pendingContext?Ru(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ru(e,t.context,!1),aa(e,t.containerInfo)}function Xu(e,t,n,r,i){return $n(),na(i),t.flags|=256,ve(e,t,n,r),t.child}var ul={dehydrated:null,treeContext:null,retryLane:0};function cl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Jd(e,t,n){var r=t.pendingProps,i=X.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),B(X,i&1),e===null)return nl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Mo(s,r,0,null),e=sn(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=cl(n),t.memoizedState=ul,e):ga(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Xg(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=_t(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=_t(l,o):(o=sn(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?cl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=ul,r}return o=e.child,e=o.sibling,r=_t(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ga(e,t){return t=Mo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function vi(e,t,n,r){return r!==null&&na(r),Hn(t,e.child,null,n),e=ga(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Xg(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=fs(Error(k(422))),vi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Mo({mode:"visible",children:r.children},i,0,null),o=sn(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&Hn(t,e.child,null,s),t.child.memoizedState=cl(s),t.memoizedState=ul,o);if(!(t.mode&1))return vi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(k(419)),r=fs(o,r,void 0),vi(e,t,s,r)}if(l=(s&e.childLanes)!==0,ke||l){if(r=se,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,mt(e,i),Ye(r,e,i,-1))}return Ca(),r=fs(Error(k(421))),vi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=iy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Me=Dt(i.nextSibling),Ae=t,H=!0,Ke=null,e!==null&&(Be[ze++]=lt,Be[ze++]=at,Be[ze++]=un,lt=e.id,at=e.overflow,un=t),t=ga(t,r.children),t.flags|=4096,t)}function bu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),rl(e.return,t,n)}function ds(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function qd(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(ve(e,t,r.children,n),r=X.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&bu(e,n,t);else if(e.tag===19)bu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(X,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&no(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),ds(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&no(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}ds(t,!0,n,null,o);break;case"together":ds(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ni(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),fn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(k(153));if(t.child!==null){for(e=t.child,n=_t(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=_t(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function bg(e,t,n){switch(t.tag){case 3:Zd(t),$n();break;case 5:Td(t);break;case 1:Te(t.type)&&Yi(t);break;case 4:aa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;B(qi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(X,X.current&1),t.flags|=128,null):n&t.child.childLanes?Jd(e,t,n):(B(X,X.current&1),e=gt(e,t,n),e!==null?e.sibling:null);B(X,X.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qd(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(X,X.current),r)break;return null;case 22:case 23:return t.lanes=0,Qd(e,t,n)}return gt(e,t,n)}var ep,fl,tp,np;ep=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};fl=function(){};tp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,nn(rt.current);var o=null;switch(n){case"input":i=Vs(e,i),r=Vs(e,r),o=[];break;case"select":i=K({},i,{value:void 0}),r=K({},r,{value:void 0}),o=[];break;case"textarea":i=_s(e,i),r=_s(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ki)}Is(n,r);var s;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(jr.hasOwnProperty(u)?o||(o=[]):(o=o||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(jr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&z("scroll",e),o||l===a||(o=[])):(o=o||[]).push(u,a))}n&&(o=o||[]).push("style",n);var u=o;(t.updateQueue=u)&&(t.flags|=4)}};np=function(e,t,n,r){n!==r&&(t.flags|=4)};function or(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function de(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Gg(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return de(t),null;case 1:return Te(t.type)&&Qi(),de(t),null;case 3:return r=t.stateNode,Xn(),U(Pe),U(me),ca(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(gi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ke!==null&&(xl(Ke),Ke=null))),fl(e,t),de(t),null;case 5:ua(t);var i=nn(Br.current);if(n=t.type,e!==null&&t.stateNode!=null)tp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(k(166));return de(t),null}if(e=nn(rt.current),gi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[tt]=t,r[Fr]=o,e=(t.mode&1)!==0,n){case"dialog":z("cancel",r),z("close",r);break;case"iframe":case"object":case"embed":z("load",r);break;case"video":case"audio":for(i=0;i<dr.length;i++)z(dr[i],r);break;case"source":z("error",r);break;case"img":case"image":case"link":z("error",r),z("load",r);break;case"details":z("toggle",r);break;case"input":tu(r,o),z("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},z("invalid",r);break;case"textarea":ru(r,o),z("invalid",r)}Is(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&mi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&mi(r.textContent,l,e),i=["children",""+l]):jr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&z("scroll",r)}switch(n){case"input":li(r),nu(r,o,!0);break;case"textarea":li(r),iu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ki)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Mf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[tt]=t,e[Fr]=r,ep(e,t,!1,!1),t.stateNode=e;e:{switch(s=Bs(n,r),n){case"dialog":z("cancel",e),z("close",e),i=r;break;case"iframe":case"object":case"embed":z("load",e),i=r;break;case"video":case"audio":for(i=0;i<dr.length;i++)z(dr[i],e);i=r;break;case"source":z("error",e),i=r;break;case"img":case"image":case"link":z("error",e),z("load",e),i=r;break;case"details":z("toggle",e),i=r;break;case"input":tu(e,r),i=Vs(e,r),z("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=K({},r,{value:void 0}),z("invalid",e);break;case"textarea":ru(e,r),i=_s(e,r),z("invalid",e);break;default:i=r}Is(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?Vf(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Af(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Rr(e,a):typeof a=="number"&&Rr(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(jr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&z("scroll",e):a!=null&&zl(e,o,a,s))}switch(n){case"input":li(e),nu(e,r,!1);break;case"textarea":li(e),iu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+It(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?On(e,!!r.multiple,o,!1):r.defaultValue!=null&&On(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ki)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return de(t),null;case 6:if(e&&t.stateNode!=null)np(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(k(166));if(n=nn(Br.current),nn(rt.current),gi(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(o=r.nodeValue!==n)&&(e=Ae,e!==null))switch(e.tag){case 3:mi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&mi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return de(t),null;case 13:if(U(X),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Me!==null&&t.mode&1&&!(t.flags&128))wd(),$n(),t.flags|=98560,o=!1;else if(o=gi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(k(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(k(317));o[tt]=t}else $n(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;de(t),o=!1}else Ke!==null&&(xl(Ke),Ke=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||X.current&1?re===0&&(re=3):Ca())),t.updateQueue!==null&&(t.flags|=4),de(t),null);case 4:return Xn(),fl(e,t),e===null&&Or(t.stateNode.containerInfo),de(t),null;case 10:return oa(t.type._context),de(t),null;case 17:return Te(t.type)&&Qi(),de(t),null;case 19:if(U(X),o=t.memoizedState,o===null)return de(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)or(o,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=no(e),s!==null){for(t.flags|=128,or(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(X,X.current&1|2),t.child}e=e.sibling}o.tail!==null&&Z()>Gn&&(t.flags|=128,r=!0,or(o,!1),t.lanes=4194304)}else{if(!r)if(e=no(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),or(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return de(t),null}else 2*Z()-o.renderingStartTime>Gn&&n!==1073741824&&(t.flags|=128,r=!0,or(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Z(),t.sibling=null,n=X.current,B(X,r?n&1|2:n&1),t):(de(t),null);case 22:case 23:return Sa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Le&1073741824&&(de(t),t.subtreeFlags&6&&(t.flags|=8192)):de(t),null;case 24:return null;case 25:return null}throw Error(k(156,t.tag))}function Kg(e,t){switch(ta(t),t.tag){case 1:return Te(t.type)&&Qi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Xn(),U(Pe),U(me),ca(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ua(t),null;case 13:if(U(X),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(k(340));$n()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return U(X),null;case 4:return Xn(),null;case 10:return oa(t.type._context),null;case 22:case 23:return Sa(),null;case 24:return null;default:return null}}var xi=!1,he=!1,Qg=typeof WeakSet=="function"?WeakSet:Set,R=null;function Ln(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function dl(e,t,n){try{n()}catch(r){Q(e,t,r)}}var Gu=!1;function Yg(e,t){if(Qs=Xi,e=ld(),ql(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,f=0,c=e,p=null;t:for(;;){for(var g;c!==n||i!==0&&c.nodeType!==3||(l=s+i),c!==o||r!==0&&c.nodeType!==3||(a=s+r),c.nodeType===3&&(s+=c.nodeValue.length),(g=c.firstChild)!==null;)p=c,c=g;for(;;){if(c===e)break t;if(p===n&&++u===i&&(l=s),p===o&&++f===r&&(a=s),(g=c.nextSibling)!==null)break;c=p,p=c.parentNode}c=g}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ys={focusedElem:e,selectionRange:n},Xi=!1,R=t;R!==null;)if(t=R,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,R=e;else for(;R!==null;){t=R;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,C=v.memoizedState,y=t.stateNode,h=y.getSnapshotBeforeUpdate(t.elementType===t.type?x:be(t.type,x),C);y.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(k(163))}}catch(w){Q(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,R=e;break}R=t.return}return v=Gu,Gu=!1,v}function wr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&dl(t,n,o)}i=i.next}while(i!==r)}}function Ro(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function rp(e){var t=e.alternate;t!==null&&(e.alternate=null,rp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[Fr],delete t[qs],delete t[Dg],delete t[Vg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ip(e){return e.tag===5||e.tag===3||e.tag===4}function Ku(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ip(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ki));else if(r!==4&&(e=e.child,e!==null))for(hl(e,t,n),e=e.sibling;e!==null;)hl(e,t,n),e=e.sibling}function ml(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ml(e,t,n),e=e.sibling;e!==null;)ml(e,t,n),e=e.sibling}var ae=null,Ge=!1;function wt(e,t,n){for(n=n.child;n!==null;)op(e,t,n),n=n.sibling}function op(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(wo,n)}catch{}switch(n.tag){case 5:he||Ln(n,t);case 6:var r=ae,i=Ge;ae=null,wt(e,t,n),ae=r,Ge=i,ae!==null&&(Ge?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Ge?(e=ae,n=n.stateNode,e.nodeType===8?os(e.parentNode,n):e.nodeType===1&&os(e,n),Dr(e)):os(ae,n.stateNode));break;case 4:r=ae,i=Ge,ae=n.stateNode.containerInfo,Ge=!0,wt(e,t,n),ae=r,Ge=i;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&dl(n,t,s),i=i.next}while(i!==r)}wt(e,t,n);break;case 1:if(!he&&(Ln(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Q(n,t,l)}wt(e,t,n);break;case 21:wt(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,wt(e,t,n),he=r):wt(e,t,n);break;default:wt(e,t,n)}}function Qu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Qg),t.forEach(function(r){var i=oy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Xe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ae=l.stateNode,Ge=!1;break e;case 3:ae=l.stateNode.containerInfo,Ge=!0;break e;case 4:ae=l.stateNode.containerInfo,Ge=!0;break e}l=l.return}if(ae===null)throw Error(k(160));op(o,s,i),ae=null,Ge=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Q(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sp(t,e),t=t.sibling}function sp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Xe(t,e),qe(e),r&4){try{wr(3,e,e.return),Ro(3,e)}catch(x){Q(e,e.return,x)}try{wr(5,e,e.return)}catch(x){Q(e,e.return,x)}}break;case 1:Xe(t,e),qe(e),r&512&&n!==null&&Ln(n,n.return);break;case 5:if(Xe(t,e),qe(e),r&512&&n!==null&&Ln(n,n.return),e.flags&32){var i=e.stateNode;try{Rr(i,"")}catch(x){Q(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&Rf(i,o),Bs(l,s);var u=Bs(l,o);for(s=0;s<a.length;s+=2){var f=a[s],c=a[s+1];f==="style"?Vf(i,c):f==="dangerouslySetInnerHTML"?Af(i,c):f==="children"?Rr(i,c):zl(i,f,c,u)}switch(l){case"input":Ns(i,o);break;case"textarea":Lf(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var g=o.value;g!=null?On(i,!!o.multiple,g,!1):p!==!!o.multiple&&(o.defaultValue!=null?On(i,!!o.multiple,o.defaultValue,!0):On(i,!!o.multiple,o.multiple?[]:"",!1))}i[Fr]=o}catch(x){Q(e,e.return,x)}}break;case 6:if(Xe(t,e),qe(e),r&4){if(e.stateNode===null)throw Error(k(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(x){Q(e,e.return,x)}}break;case 3:if(Xe(t,e),qe(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Dr(t.containerInfo)}catch(x){Q(e,e.return,x)}break;case 4:Xe(t,e),qe(e);break;case 13:Xe(t,e),qe(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(xa=Z())),r&4&&Qu(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(he=(u=he)||f,Xe(t,e),he=u):Xe(t,e),qe(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(R=e,f=e.child;f!==null;){for(c=R=f;R!==null;){switch(p=R,g=p.child,p.tag){case 0:case 11:case 14:case 15:wr(4,p,p.return);break;case 1:Ln(p,p.return);var v=p.stateNode;if(typeof v.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){Q(r,n,x)}}break;case 5:Ln(p,p.return);break;case 22:if(p.memoizedState!==null){Zu(c);continue}}g!==null?(g.return=p,R=g):Zu(c)}f=f.sibling}e:for(f=null,c=e;;){if(c.tag===5){if(f===null){f=c;try{i=c.stateNode,u?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=c.stateNode,a=c.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Df("display",s))}catch(x){Q(e,e.return,x)}}}else if(c.tag===6){if(f===null)try{c.stateNode.nodeValue=u?"":c.memoizedProps}catch(x){Q(e,e.return,x)}}else if((c.tag!==22&&c.tag!==23||c.memoizedState===null||c===e)&&c.child!==null){c.child.return=c,c=c.child;continue}if(c===e)break e;for(;c.sibling===null;){if(c.return===null||c.return===e)break e;f===c&&(f=null),c=c.return}f===c&&(f=null),c.sibling.return=c.return,c=c.sibling}}break;case 19:Xe(t,e),qe(e),r&4&&Qu(e);break;case 21:break;default:Xe(t,e),qe(e)}}function qe(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ip(n)){var r=n;break e}n=n.return}throw Error(k(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Rr(i,""),r.flags&=-33);var o=Ku(e);ml(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Ku(e);hl(e,l,s);break;default:throw Error(k(161))}}catch(a){Q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zg(e,t,n){R=e,lp(e)}function lp(e,t,n){for(var r=(e.mode&1)!==0;R!==null;){var i=R,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||xi;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||he;l=xi;var u=he;if(xi=s,(he=a)&&!u)for(R=i;R!==null;)s=R,a=s.child,s.tag===22&&s.memoizedState!==null?Ju(i):a!==null?(a.return=s,R=a):Ju(i);for(;o!==null;)R=o,lp(o),o=o.sibling;R=i,xi=l,he=u}Yu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,R=o):Yu(e)}}function Yu(e){for(;R!==null;){var t=R;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Ro(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:be(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Nu(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Nu(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var c=f.dehydrated;c!==null&&Dr(c)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(k(163))}he||t.flags&512&&pl(t)}catch(p){Q(t,t.return,p)}}if(t===e){R=null;break}if(n=t.sibling,n!==null){n.return=t.return,R=n;break}R=t.return}}function Zu(e){for(;R!==null;){var t=R;if(t===e){R=null;break}var n=t.sibling;if(n!==null){n.return=t.return,R=n;break}R=t.return}}function Ju(e){for(;R!==null;){var t=R;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ro(4,t)}catch(a){Q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Q(t,i,a)}}var o=t.return;try{pl(t)}catch(a){Q(t,o,a)}break;case 5:var s=t.return;try{pl(t)}catch(a){Q(t,s,a)}}}catch(a){Q(t,t.return,a)}if(t===e){R=null;break}var l=t.sibling;if(l!==null){l.return=t.return,R=l;break}R=t.return}}var Jg=Math.ceil,oo=vt.ReactCurrentDispatcher,ya=vt.ReactCurrentOwner,We=vt.ReactCurrentBatchConfig,F=0,se=null,te=null,ue=0,Le=0,Mn=$t(0),re=0,$r=null,fn=0,Lo=0,va=0,Sr=null,Ce=null,xa=0,Gn=1/0,ot=null,so=!1,gl=null,Nt=null,wi=!1,Rt=null,lo=0,Cr=0,yl=null,Oi=-1,_i=0;function xe(){return F&6?Z():Oi!==-1?Oi:Oi=Z()}function Ot(e){return e.mode&1?F&2&&ue!==0?ue&-ue:Og.transition!==null?(_i===0&&(_i=Xf()),_i):(e=I,e!==0||(e=window.event,e=e===void 0?16:Jf(e.type)),e):1}function Ye(e,t,n,r){if(50<Cr)throw Cr=0,yl=null,Error(k(185));Yr(e,n,r),(!(F&2)||e!==se)&&(e===se&&(!(F&2)&&(Lo|=n),re===4&&Et(e,ue)),Ee(e,r),n===1&&F===0&&!(t.mode&1)&&(Gn=Z()+500,To&&Ht()))}function Ee(e,t){var n=e.callbackNode;Om(e,t);var r=Hi(e,e===se?ue:0);if(r===0)n!==null&&lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lu(n),t===1)e.tag===0?Ng(qu.bind(null,e)):yd(qu.bind(null,e)),Mg(function(){!(F&6)&&Ht()}),n=null;else{switch(bf(r)){case 1:n=Xl;break;case 4:n=$f;break;case 16:n=$i;break;case 536870912:n=Hf;break;default:n=$i}n=mp(n,ap.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ap(e,t){if(Oi=-1,_i=0,F&6)throw Error(k(327));var n=e.callbackNode;if(zn()&&e.callbackNode!==n)return null;var r=Hi(e,e===se?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ao(e,r);else{t=r;var i=F;F|=2;var o=cp();(se!==e||ue!==t)&&(ot=null,Gn=Z()+500,on(e,t));do try{ty();break}catch(l){up(e,l)}while(!0);ia(),oo.current=o,F=i,te!==null?t=0:(se=null,ue=0,t=re)}if(t!==0){if(t===2&&(i=Hs(e),i!==0&&(r=i,t=vl(e,i))),t===1)throw n=$r,on(e,0),Et(e,r),Ee(e,Z()),n;if(t===6)Et(e,r);else{if(i=e.current.alternate,!(r&30)&&!qg(i)&&(t=ao(e,r),t===2&&(o=Hs(e),o!==0&&(r=o,t=vl(e,o))),t===1))throw n=$r,on(e,0),Et(e,r),Ee(e,Z()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(k(345));case 2:Zt(e,Ce,ot);break;case 3:if(Et(e,r),(r&130023424)===r&&(t=xa+500-Z(),10<t)){if(Hi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Js(Zt.bind(null,e,Ce,ot),t);break}Zt(e,Ce,ot);break;case 4:if(Et(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Qe(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=Z()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Jg(r/1960))-r,10<r){e.timeoutHandle=Js(Zt.bind(null,e,Ce,ot),r);break}Zt(e,Ce,ot);break;case 5:Zt(e,Ce,ot);break;default:throw Error(k(329))}}}return Ee(e,Z()),e.callbackNode===n?ap.bind(null,e):null}function vl(e,t){var n=Sr;return e.current.memoizedState.isDehydrated&&(on(e,t).flags|=256),e=ao(e,t),e!==2&&(t=Ce,Ce=n,t!==null&&xl(t)),e}function xl(e){Ce===null?Ce=e:Ce.push.apply(Ce,e)}function qg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Ze(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Et(e,t){for(t&=~va,t&=~Lo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Qe(t),r=1<<n;e[n]=-1,t&=~r}}function qu(e){if(F&6)throw Error(k(327));zn();var t=Hi(e,0);if(!(t&1))return Ee(e,Z()),null;var n=ao(e,t);if(e.tag!==0&&n===2){var r=Hs(e);r!==0&&(t=r,n=vl(e,r))}if(n===1)throw n=$r,on(e,0),Et(e,t),Ee(e,Z()),n;if(n===6)throw Error(k(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Zt(e,Ce,ot),Ee(e,Z()),null}function wa(e,t){var n=F;F|=1;try{return e(t)}finally{F=n,F===0&&(Gn=Z()+500,To&&Ht())}}function dn(e){Rt!==null&&Rt.tag===0&&!(F&6)&&zn();var t=F;F|=1;var n=We.transition,r=I;try{if(We.transition=null,I=1,e)return e()}finally{I=r,We.transition=n,F=t,!(F&6)&&Ht()}}function Sa(){Le=Mn.current,U(Mn)}function on(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Lg(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(ta(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Qi();break;case 3:Xn(),U(Pe),U(me),ca();break;case 5:ua(r);break;case 4:Xn();break;case 13:U(X);break;case 19:U(X);break;case 10:oa(r.type._context);break;case 22:case 23:Sa()}n=n.return}if(se=e,te=e=_t(e.current,null),ue=Le=t,re=0,$r=null,va=Lo=fn=0,Ce=Sr=null,tn!==null){for(t=0;t<tn.length;t++)if(n=tn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}tn=null}return e}function up(e,t){do{var n=te;try{if(ia(),Di.current=io,ro){for(var r=G.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ro=!1}if(cn=0,oe=ne=G=null,xr=!1,zr=0,ya.current=null,n===null||n.return===null){re=1,$r=t,te=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=l,c=f.tag;if(!(f.mode&1)&&(c===0||c===11||c===15)){var p=f.alternate;p?(f.updateQueue=p.updateQueue,f.memoizedState=p.memoizedState,f.lanes=p.lanes):(f.updateQueue=null,f.memoizedState=null)}var g=zu(s);if(g!==null){g.flags&=-257,Uu(g,s,l,o,t),g.mode&1&&Bu(o,u,t),t=g,a=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(a),t.updateQueue=x}else v.add(a);break e}else{if(!(t&1)){Bu(o,u,t),Ca();break e}a=Error(k(426))}}else if(H&&l.mode&1){var C=zu(s);if(C!==null){!(C.flags&65536)&&(C.flags|=256),Uu(C,s,l,o,t),na(bn(a,l));break e}}o=a=bn(a,l),re!==4&&(re=2),Sr===null?Sr=[o]:Sr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var y=bd(o,a,t);Vu(o,y);break e;case 1:l=a;var h=o.type,m=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Nt===null||!Nt.has(m)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=Gd(o,l,t);Vu(o,w);break e}}o=o.return}while(o!==null)}dp(n)}catch(S){t=S,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function cp(){var e=oo.current;return oo.current=io,e===null?io:e}function Ca(){(re===0||re===3||re===2)&&(re=4),se===null||!(fn&268435455)&&!(Lo&268435455)||Et(se,ue)}function ao(e,t){var n=F;F|=2;var r=cp();(se!==e||ue!==t)&&(ot=null,on(e,t));do try{ey();break}catch(i){up(e,i)}while(!0);if(ia(),F=n,oo.current=r,te!==null)throw Error(k(261));return se=null,ue=0,re}function ey(){for(;te!==null;)fp(te)}function ty(){for(;te!==null&&!Em();)fp(te)}function fp(e){var t=hp(e.alternate,e,Le);e.memoizedProps=e.pendingProps,t===null?dp(e):te=t,ya.current=null}function dp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Kg(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,te=null;return}}else if(n=Gg(n,t,Le),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);re===0&&(re=5)}function Zt(e,t,n){var r=I,i=We.transition;try{We.transition=null,I=1,ny(e,t,n,r)}finally{We.transition=i,I=r}return null}function ny(e,t,n,r){do zn();while(Rt!==null);if(F&6)throw Error(k(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(k(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(_m(e,o),e===se&&(te=se=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||wi||(wi=!0,mp($i,function(){return zn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=We.transition,We.transition=null;var s=I;I=1;var l=F;F|=4,ya.current=null,Yg(e,n),sp(n,e),Cg(Ys),Xi=!!Qs,Ys=Qs=null,e.current=n,Zg(n),jm(),F=l,I=s,We.transition=o}else e.current=n;if(wi&&(wi=!1,Rt=e,lo=i),o=e.pendingLanes,o===0&&(Nt=null),Mm(n.stateNode),Ee(e,Z()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(so)throw so=!1,e=gl,gl=null,e;return lo&1&&e.tag!==0&&zn(),o=e.pendingLanes,o&1?e===yl?Cr++:(Cr=0,yl=e):Cr=0,Ht(),null}function zn(){if(Rt!==null){var e=bf(lo),t=We.transition,n=I;try{if(We.transition=null,I=16>e?16:e,Rt===null)var r=!1;else{if(e=Rt,Rt=null,lo=0,F&6)throw Error(k(331));var i=F;for(F|=4,R=e.current;R!==null;){var o=R,s=o.child;if(R.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(R=u;R!==null;){var f=R;switch(f.tag){case 0:case 11:case 15:wr(8,f,o)}var c=f.child;if(c!==null)c.return=f,R=c;else for(;R!==null;){f=R;var p=f.sibling,g=f.return;if(rp(f),f===u){R=null;break}if(p!==null){p.return=g,R=p;break}R=g}}}var v=o.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var C=x.sibling;x.sibling=null,x=C}while(x!==null)}}R=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,R=s;else e:for(;R!==null;){if(o=R,o.flags&2048)switch(o.tag){case 0:case 11:case 15:wr(9,o,o.return)}var y=o.sibling;if(y!==null){y.return=o.return,R=y;break e}R=o.return}}var h=e.current;for(R=h;R!==null;){s=R;var m=s.child;if(s.subtreeFlags&2064&&m!==null)m.return=s,R=m;else e:for(s=h;R!==null;){if(l=R,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Ro(9,l)}}catch(S){Q(l,l.return,S)}if(l===s){R=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,R=w;break e}R=l.return}}if(F=i,Ht(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(wo,e)}catch{}r=!0}return r}finally{I=n,We.transition=t}}return!1}function ec(e,t,n){t=bn(n,t),t=bd(e,t,1),e=Vt(e,t,1),t=xe(),e!==null&&(Yr(e,1,t),Ee(e,t))}function Q(e,t,n){if(e.tag===3)ec(e,e,n);else for(;t!==null;){if(t.tag===3){ec(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Nt===null||!Nt.has(r))){e=bn(n,e),e=Gd(t,e,1),t=Vt(t,e,1),e=xe(),t!==null&&(Yr(t,1,e),Ee(t,e));break}}t=t.return}}function ry(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(ue&n)===n&&(re===4||re===3&&(ue&130023424)===ue&&500>Z()-xa?on(e,0):va|=n),Ee(e,t)}function pp(e,t){t===0&&(e.mode&1?(t=ci,ci<<=1,!(ci&130023424)&&(ci=4194304)):t=1);var n=xe();e=mt(e,t),e!==null&&(Yr(e,t,n),Ee(e,n))}function iy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),pp(e,n)}function oy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(k(314))}r!==null&&r.delete(t),pp(e,n)}var hp;hp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Pe.current)ke=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ke=!1,bg(e,t,n);ke=!!(e.flags&131072)}else ke=!1,H&&t.flags&1048576&&vd(t,Ji,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ni(e,t),e=t.pendingProps;var i=Wn(t,me.current);Bn(t,n),i=da(null,t,r,e,i,n);var o=pa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Te(r)?(o=!0,Yi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,la(t),i.updater=jo,t.stateNode=i,i._reactInternals=t,ol(t,r,e,n),t=al(null,t,r,!0,o,n)):(t.tag=0,H&&o&&ea(t),ve(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ni(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ly(r),e=be(r,e),i){case 0:t=ll(null,t,r,e,n);break e;case 1:t=Hu(null,t,r,e,n);break e;case 11:t=Wu(null,t,r,e,n);break e;case 14:t=$u(null,t,r,be(r.type,e),n);break e}throw Error(k(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),ll(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),Hu(e,t,r,i,n);case 3:e:{if(Zd(t),e===null)throw Error(k(387));r=t.pendingProps,o=t.memoizedState,i=o.element,Pd(e,t),to(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=bn(Error(k(423)),t),t=Xu(e,t,r,n,i);break e}else if(r!==i){i=bn(Error(k(424)),t),t=Xu(e,t,r,n,i);break e}else for(Me=Dt(t.stateNode.containerInfo.firstChild),Ae=t,H=!0,Ke=null,n=Cd(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if($n(),r===i){t=gt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Td(t),e===null&&nl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Zs(r,i)?s=null:o!==null&&Zs(r,o)&&(t.flags|=32),Yd(e,t),ve(e,t,s,n),t.child;case 6:return e===null&&nl(t),null;case 13:return Jd(e,t,n);case 4:return aa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Hn(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),Wu(e,t,r,i,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,B(qi,r._currentValue),r._currentValue=s,o!==null)if(Ze(o.value,s)){if(o.children===i.children&&!Pe.current){t=gt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=ct(-1,n&-n),a.tag=2;var u=o.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),rl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(k(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),rl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ve(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Bn(t,n),i=$e(i),r=r(i),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,i=be(r,t.pendingProps),i=be(r.type,i),$u(e,t,r,i,n);case 15:return Kd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:be(r,i),Ni(e,t),t.tag=1,Te(r)?(e=!0,Yi(t)):e=!1,Bn(t,n),Xd(t,r,i),ol(t,r,i,n),al(null,t,r,!0,e,n);case 19:return qd(e,t,n);case 22:return Qd(e,t,n)}throw Error(k(156,t.tag))};function mp(e,t){return Wf(e,t)}function sy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new sy(e,t,n,r)}function ka(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ly(e){if(typeof e=="function")return ka(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Wl)return 11;if(e===$l)return 14}return 2}function _t(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fi(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")ka(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case wn:return sn(n.children,i,o,t);case Ul:s=8,i|=8;break;case Ls:return e=Ue(12,n,t,i|2),e.elementType=Ls,e.lanes=o,e;case Ms:return e=Ue(13,n,t,i),e.elementType=Ms,e.lanes=o,e;case As:return e=Ue(19,n,t,i),e.elementType=As,e.lanes=o,e;case Tf:return Mo(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case kf:s=10;break e;case Pf:s=9;break e;case Wl:s=11;break e;case $l:s=14;break e;case Ct:s=16,r=null;break e}throw Error(k(130,e==null?e:typeof e,""))}return t=Ue(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function sn(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Mo(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=Tf,e.lanes=n,e.stateNode={isHidden:!1},e}function ps(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function hs(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ay(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ko(0),this.expirationTimes=Ko(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ko(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Pa(e,t,n,r,i,o,s,l,a){return e=new ay(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=Ue(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},la(o),e}function uy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gp(e){if(!e)return Bt;e=e._reactInternals;e:{if(hn(e)!==e||e.tag!==1)throw Error(k(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Te(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(k(171))}if(e.tag===1){var n=e.type;if(Te(n))return gd(e,n,t)}return t}function yp(e,t,n,r,i,o,s,l,a){return e=Pa(n,r,!0,e,i,o,s,l,a),e.context=gp(null),n=e.current,r=xe(),i=Ot(n),o=ct(r,i),o.callback=t??null,Vt(n,o,i),e.current.lanes=i,Yr(e,i,r),Ee(e,r),e}function Ao(e,t,n,r){var i=t.current,o=xe(),s=Ot(i);return n=gp(n),t.context===null?t.context=n:t.pendingContext=n,t=ct(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Vt(i,t,s),e!==null&&(Ye(e,i,s,o),Ai(e,i,s)),s}function uo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ta(e,t){tc(e,t),(e=e.alternate)&&tc(e,t)}function cy(){return null}var vp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ea(e){this._internalRoot=e}Do.prototype.render=Ea.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(k(409));Ao(e,t,null,null)};Do.prototype.unmount=Ea.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;dn(function(){Ao(null,e,null,null)}),t[ht]=null}};function Do(e){this._internalRoot=e}Do.prototype.unstable_scheduleHydration=function(e){if(e){var t=Qf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Tt.length&&t!==0&&t<Tt[n].priority;n++);Tt.splice(n,0,e),n===0&&Zf(e)}};function ja(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Vo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function nc(){}function fy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var u=uo(s);o.call(u)}}var s=yp(t,r,e,0,null,!1,!1,"",nc);return e._reactRootContainer=s,e[ht]=s.current,Or(e.nodeType===8?e.parentNode:e),dn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=uo(a);l.call(u)}}var a=Pa(e,0,!1,null,null,!1,!1,"",nc);return e._reactRootContainer=a,e[ht]=a.current,Or(e.nodeType===8?e.parentNode:e),dn(function(){Ao(t,a,n,r)}),a}function No(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=uo(s);l.call(a)}}Ao(t,s,e,i)}else s=fy(n,t,e,i,r);return uo(s)}Gf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fr(t.pendingLanes);n!==0&&(bl(t,n|1),Ee(t,Z()),!(F&6)&&(Gn=Z()+500,Ht()))}break;case 13:dn(function(){var r=mt(e,1);if(r!==null){var i=xe();Ye(r,e,1,i)}}),Ta(e,1)}};Gl=function(e){if(e.tag===13){var t=mt(e,134217728);if(t!==null){var n=xe();Ye(t,e,134217728,n)}Ta(e,134217728)}};Kf=function(e){if(e.tag===13){var t=Ot(e),n=mt(e,t);if(n!==null){var r=xe();Ye(n,e,t,r)}Ta(e,t)}};Qf=function(){return I};Yf=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};Us=function(e,t,n){switch(t){case"input":if(Ns(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Po(r);if(!i)throw Error(k(90));jf(r),Ns(r,i)}}}break;case"textarea":Lf(e,n);break;case"select":t=n.value,t!=null&&On(e,!!n.multiple,t,!1)}};_f=wa;Ff=dn;var dy={usingClientEntryPoint:!1,Events:[Jr,Pn,Po,Nf,Of,wa]},sr={findFiberByHostInstance:en,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},py={bundleType:sr.bundleType,version:sr.version,rendererPackageName:sr.rendererPackageName,rendererConfig:sr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=zf(e),e===null?null:e.stateNode},findFiberByHostInstance:sr.findFiberByHostInstance||cy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Si=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Si.isDisabled&&Si.supportsFiber)try{wo=Si.inject(py),nt=Si}catch{}}Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=dy;Ne.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ja(t))throw Error(k(200));return uy(e,t,null,n)};Ne.createRoot=function(e,t){if(!ja(e))throw Error(k(299));var n=!1,r="",i=vp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Pa(e,1,!1,null,null,n,!1,r,i),e[ht]=t.current,Or(e.nodeType===8?e.parentNode:e),new Ea(t)};Ne.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(k(188)):(e=Object.keys(e).join(","),Error(k(268,e)));return e=zf(t),e=e===null?null:e.stateNode,e};Ne.flushSync=function(e){return dn(e)};Ne.hydrate=function(e,t,n){if(!Vo(t))throw Error(k(200));return No(null,e,t,!0,n)};Ne.hydrateRoot=function(e,t,n){if(!ja(e))throw Error(k(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=vp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=yp(t,null,e,1,n??null,i,!1,o,s),e[ht]=t.current,Or(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Do(t)};Ne.render=function(e,t,n){if(!Vo(t))throw Error(k(200));return No(null,e,t,!1,n)};Ne.unmountComponentAtNode=function(e){if(!Vo(e))throw Error(k(40));return e._reactRootContainer?(dn(function(){No(null,null,e,!1,function(){e._reactRootContainer=null,e[ht]=null})}),!0):!1};Ne.unstable_batchedUpdates=wa;Ne.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Vo(n))throw Error(k(200));if(e==null||e._reactInternals===void 0)throw Error(k(38));return No(e,t,n,!1,r)};Ne.version="18.3.1-next-f1338f8080-20240426";function xp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(xp)}catch(e){console.error(e)}}xp(),xf.exports=Ne;var hy=xf.exports,rc=hy;js.createRoot=rc.createRoot,js.hydrateRoot=rc.hydrateRoot;const wp=L.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),Oo=L.createContext({}),Ra=L.createContext(null),_o=typeof document<"u",my=_o?L.useLayoutEffect:L.useEffect,Sp=L.createContext({strict:!1}),La=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),gy="framerAppearId",Cp="data-"+La(gy);function yy(e,t,n,r){const{visualElement:i}=L.useContext(Oo),o=L.useContext(Sp),s=L.useContext(Ra),l=L.useContext(wp).reducedMotion,a=L.useRef();r=r||o.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const u=a.current;L.useInsertionEffect(()=>{u&&u.update(n,s)});const f=L.useRef(!!(n[Cp]&&!window.HandoffComplete));return my(()=>{u&&(u.render(),f.current&&u.animationState&&u.animationState.animateChanges())}),L.useEffect(()=>{u&&(u.updateFeatures(),!f.current&&u.animationState&&u.animationState.animateChanges(),f.current&&(f.current=!1,window.HandoffComplete=!0))}),u}function An(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function vy(e,t,n){return L.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):An(n)&&(n.current=r))},[t])}function Hr(e){return typeof e=="string"||Array.isArray(e)}function Fo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Ma=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Aa=["initial",...Ma];function Io(e){return Fo(e.animate)||Aa.some(t=>Hr(e[t]))}function kp(e){return!!(Io(e)||e.variants)}function xy(e,t){if(Io(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Hr(n)?n:void 0,animate:Hr(r)?r:void 0}}return e.inherit!==!1?t:{}}function wy(e){const{initial:t,animate:n}=xy(e,L.useContext(Oo));return L.useMemo(()=>({initial:t,animate:n}),[ic(t),ic(n)])}function ic(e){return Array.isArray(e)?e.join(" "):e}const oc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Xr={};for(const e in oc)Xr[e]={isEnabled:t=>oc[e].some(n=>!!t[n])};function Sy(e){for(const t in e)Xr[t]={...Xr[t],...e[t]}}const Pp=L.createContext({}),Tp=L.createContext({}),Cy=Symbol.for("motionComponentSymbol");function ky({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Sy(e);function o(l,a){let u;const f={...L.useContext(wp),...l,layoutId:Py(l)},{isStatic:c}=f,p=wy(l),g=r(l,c);if(!c&&_o){p.visualElement=yy(i,g,f,t);const v=L.useContext(Tp),x=L.useContext(Sp).strict;p.visualElement&&(u=p.visualElement.loadFeatures(f,x,e,v))}return L.createElement(Oo.Provider,{value:p},u&&p.visualElement?L.createElement(u,{visualElement:p.visualElement,...f}):null,n(i,l,vy(g,p.visualElement,a),g,c,p.visualElement))}const s=L.forwardRef(o);return s[Cy]=i,s}function Py({layoutId:e}){const t=L.useContext(Pp).id;return t&&e!==void 0?t+"-"+e:e}function Ty(e){function t(r,i={}){return ky(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Ey=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Da(e){return typeof e!="string"||e.includes("-")?!1:!!(Ey.indexOf(e)>-1||/[A-Z]/.test(e))}const co={};function jy(e){Object.assign(co,e)}const ei=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],mn=new Set(ei);function Ep(e,{layout:t,layoutId:n}){return mn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!co[e]||e==="opacity")}const je=e=>!!(e&&e.getVelocity),Ry={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ly=ei.length;function My(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Ly;s++){const l=ei[s];if(e[l]!==void 0){const a=Ry[l]||l;o+=`${a}(${e[l]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const jp=e=>t=>typeof t=="string"&&t.startsWith(e),Rp=jp("--"),wl=jp("var(--"),Ay=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Dy=(e,t)=>t&&typeof e=="number"?t.transform(e):e,zt=(e,t,n)=>Math.min(Math.max(n,e),t),gn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},kr={...gn,transform:e=>zt(0,1,e)},Ci={...gn,default:1},Pr=e=>Math.round(e*1e5)/1e5,Bo=/(-)?([\d]*\.?[\d])+/g,Lp=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Vy=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ti(e){return typeof e=="string"}const ni=e=>({test:t=>ti(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),St=ni("deg"),it=ni("%"),M=ni("px"),Ny=ni("vh"),Oy=ni("vw"),sc={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},lc={...gn,transform:Math.round},Mp={borderWidth:M,borderTopWidth:M,borderRightWidth:M,borderBottomWidth:M,borderLeftWidth:M,borderRadius:M,radius:M,borderTopLeftRadius:M,borderTopRightRadius:M,borderBottomRightRadius:M,borderBottomLeftRadius:M,width:M,maxWidth:M,height:M,maxHeight:M,size:M,top:M,right:M,bottom:M,left:M,padding:M,paddingTop:M,paddingRight:M,paddingBottom:M,paddingLeft:M,margin:M,marginTop:M,marginRight:M,marginBottom:M,marginLeft:M,rotate:St,rotateX:St,rotateY:St,rotateZ:St,scale:Ci,scaleX:Ci,scaleY:Ci,scaleZ:Ci,skew:St,skewX:St,skewY:St,distance:M,translateX:M,translateY:M,translateZ:M,x:M,y:M,z:M,perspective:M,transformPerspective:M,opacity:kr,originX:sc,originY:sc,originZ:M,zIndex:lc,fillOpacity:kr,strokeOpacity:kr,numOctaves:lc};function Va(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:l}=e;let a=!1,u=!1,f=!0;for(const c in t){const p=t[c];if(Rp(c)){o[c]=p;continue}const g=Mp[c],v=Dy(p,g);if(mn.has(c)){if(a=!0,s[c]=v,!f)continue;p!==(g.default||0)&&(f=!1)}else c.startsWith("origin")?(u=!0,l[c]=v):i[c]=v}if(t.transform||(a||r?i.transform=My(e.transform,n,f,r):i.transform&&(i.transform="none")),u){const{originX:c="50%",originY:p="50%",originZ:g=0}=l;i.transformOrigin=`${c} ${p} ${g}`}}const Na=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ap(e,t,n){for(const r in t)!je(t[r])&&!Ep(r,n)&&(e[r]=t[r])}function _y({transformTemplate:e},t,n){return L.useMemo(()=>{const r=Na();return Va(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function Fy(e,t,n){const r=e.style||{},i={};return Ap(i,r,e),Object.assign(i,_y(e,t,n)),e.transformValues?e.transformValues(i):i}function Iy(e,t,n){const r={},i=Fy(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const By=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function fo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||By.has(e)}let Dp=e=>!fo(e);function zy(e){e&&(Dp=t=>t.startsWith("on")?!fo(t):e(t))}try{zy(require("@emotion/is-prop-valid").default)}catch{}function Uy(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Dp(i)||n===!0&&fo(i)||!t&&!fo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function ac(e,t,n){return typeof e=="string"?e:M.transform(t+n*e)}function Wy(e,t,n){const r=ac(t,e.x,e.width),i=ac(n,e.y,e.height);return`${r} ${i}`}const $y={offset:"stroke-dashoffset",array:"stroke-dasharray"},Hy={offset:"strokeDashoffset",array:"strokeDasharray"};function Xy(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?$y:Hy;e[o.offset]=M.transform(-r);const s=M.transform(t),l=M.transform(n);e[o.array]=`${s} ${l}`}function Oa(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...u},f,c,p){if(Va(e,u,f,p),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:g,style:v,dimensions:x}=e;g.transform&&(x&&(v.transform=g.transform),delete g.transform),x&&(i!==void 0||o!==void 0||v.transform)&&(v.transformOrigin=Wy(x,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(g.x=t),n!==void 0&&(g.y=n),r!==void 0&&(g.scale=r),s!==void 0&&Xy(g,s,l,a,!1)}const Vp=()=>({...Na(),attrs:{}}),_a=e=>typeof e=="string"&&e.toLowerCase()==="svg";function by(e,t,n,r){const i=L.useMemo(()=>{const o=Vp();return Oa(o,t,{enableHardwareAcceleration:!1},_a(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Ap(o,e.style,e),i.style={...o,...i.style}}return i}function Gy(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(Da(n)?by:Iy)(r,o,s,n),f={...Uy(r,typeof n=="string",e),...a,ref:i},{children:c}=r,p=L.useMemo(()=>je(c)?c.get():c,[c]);return L.createElement(n,{...f,children:p})}}function Np(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Op=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function _p(e,t,n,r){Np(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Op.has(i)?i:La(i),t.attrs[i])}function Fa(e,t){const{style:n}=e,r={};for(const i in n)(je(n[i])||t.style&&je(t.style[i])||Ep(i,e))&&(r[i]=n[i]);return r}function Fp(e,t){const n=Fa(e,t);for(const r in e)if(je(e[r])||je(t[r])){const i=ei.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Ia(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Ky(e){const t=L.useRef(null);return t.current===null&&(t.current=e()),t.current}const po=e=>Array.isArray(e),Qy=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Yy=e=>po(e)?e[e.length-1]||0:e;function Ii(e){const t=je(e)?e.get():e;return Qy(t)?t.toValue():t}function Zy({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Jy(r,i,o,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const Ip=e=>(t,n)=>{const r=L.useContext(Oo),i=L.useContext(Ra),o=()=>Zy(e,t,r,i);return n?o():Ky(o)};function Jy(e,t,n,r){const i={},o=r(e,{});for(const p in o)i[p]=Ii(o[p]);let{initial:s,animate:l}=e;const a=Io(e),u=kp(e);t&&u&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let f=n?n.initial===!1:!1;f=f||s===!1;const c=f?l:s;return c&&typeof c!="boolean"&&!Fo(c)&&(Array.isArray(c)?c:[c]).forEach(g=>{const v=Ia(e,g);if(!v)return;const{transitionEnd:x,transition:C,...y}=v;for(const h in y){let m=y[h];if(Array.isArray(m)){const w=f?m.length-1:0;m=m[w]}m!==null&&(i[h]=m)}for(const h in x)i[h]=x[h]}),i}const J=e=>e;class uc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function qy(e){let t=new uc,n=new uc,r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:(a,u=!1,f=!1)=>{const c=f&&i,p=c?t:n;return u&&s.add(a),p.add(a)&&c&&i&&(r=t.order.length),a},cancel:a=>{n.remove(a),s.delete(a)},process:a=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const f=t.order[u];f(a),s.has(f)&&(l.schedule(f),e())}i=!1,o&&(o=!1,l.process(a))}};return l}const ki=["prepare","read","update","preRender","render","postRender"],ev=40;function tv(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=ki.reduce((c,p)=>(c[p]=qy(()=>n=!0),c),{}),s=c=>o[c].process(i),l=()=>{const c=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(c-i.timestamp,ev),1),i.timestamp=c,i.isProcessing=!0,ki.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(l))},a=()=>{n=!0,r=!0,i.isProcessing||e(l)};return{schedule:ki.reduce((c,p)=>{const g=o[p];return c[p]=(v,x=!1,C=!1)=>(n||a(),g.schedule(v,x,C)),c},{}),cancel:c=>ki.forEach(p=>o[p].cancel(c)),state:i,steps:o}}const{schedule:W,cancel:yt,state:pe,steps:ms}=tv(typeof requestAnimationFrame<"u"?requestAnimationFrame:J,!0),nv={useVisualState:Ip({scrapeMotionValuesFromProps:Fp,createRenderState:Vp,onMount:(e,t,{renderState:n,latestValues:r})=>{W.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),W.render(()=>{Oa(n,r,{enableHardwareAcceleration:!1},_a(t.tagName),e.transformTemplate),_p(t,n)})}})},rv={useVisualState:Ip({scrapeMotionValuesFromProps:Fa,createRenderState:Na})};function iv(e,{forwardMotionProps:t=!1},n,r){return{...Da(e)?nv:rv,preloadedFeatures:n,useRender:Gy(t),createVisualElement:r,Component:e}}function ut(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Bp=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function zo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const ov=e=>t=>Bp(t)&&e(t,zo(t));function ft(e,t,n,r){return ut(e,t,ov(n),r)}const sv=(e,t)=>n=>t(e(n)),Ft=(...e)=>e.reduce(sv);function zp(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const cc=zp("dragHorizontal"),fc=zp("dragVertical");function Up(e){let t=!1;if(e==="y")t=fc();else if(e==="x")t=cc();else{const n=cc(),r=fc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Wp(){const e=Up(!0);return e?(e(),!1):!0}class Xt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function dc(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||Wp())return;const l=e.getProps();e.animationState&&l.whileHover&&e.animationState.setActive("whileHover",t),l[r]&&W.update(()=>l[r](o,s))};return ft(e.current,n,i,{passive:!e.getProps()[r]})}class lv extends Xt{mount(){this.unmount=Ft(dc(this.node,!0),dc(this.node,!1))}unmount(){}}class av extends Xt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Ft(ut(this.node.current,"focus",()=>this.onFocus()),ut(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const $p=(e,t)=>t?e===t?!0:$p(e,t.parentElement):!1;function gs(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,zo(n))}class uv extends Xt{constructor(){super(...arguments),this.removeStartListeners=J,this.removeEndListeners=J,this.removeAccessibleListeners=J,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=ft(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:f,globalTapTarget:c}=this.node.getProps();W.update(()=>{!c&&!$p(this.node.current,l.target)?f&&f(l,a):u&&u(l,a)})},{passive:!(r.onTap||r.onPointerUp)}),s=ft(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=Ft(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=l=>{l.key!=="Enter"||!this.checkPressEnd()||gs("up",(a,u)=>{const{onTap:f}=this.node.getProps();f&&W.update(()=>f(a,u))})};this.removeEndListeners(),this.removeEndListeners=ut(this.node.current,"keyup",s),gs("down",(l,a)=>{this.startPress(l,a)})},n=ut(this.node.current,"keydown",t),r=()=>{this.isPressing&&gs("cancel",(o,s)=>this.cancelPress(o,s))},i=ut(this.node.current,"blur",r);this.removeAccessibleListeners=Ft(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&W.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Wp()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&W.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=ft(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=ut(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=Ft(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Sl=new WeakMap,ys=new WeakMap,cv=e=>{const t=Sl.get(e.target);t&&t(e)},fv=e=>{e.forEach(cv)};function dv({root:e,...t}){const n=e||document;ys.has(n)||ys.set(n,{});const r=ys.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(fv,{root:e,...t})),r[i]}function pv(e,t,n){const r=dv(t);return Sl.set(e,n),r.observe(e),()=>{Sl.delete(e),r.unobserve(e)}}const hv={some:0,all:1};class mv extends Xt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:hv[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,o&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:f,onViewportLeave:c}=this.node.getProps(),p=u?f:c;p&&p(a)};return pv(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(gv(t,n))&&this.startObserver()}unmount(){}}function gv({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const yv={inView:{Feature:mv},tap:{Feature:uv},focus:{Feature:av},hover:{Feature:lv}};function Hp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function vv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function xv(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function Uo(e,t,n){const r=e.getProps();return Ia(r,t,n!==void 0?n:r.custom,vv(e),xv(e))}let Ba=J;const ln=e=>e*1e3,dt=e=>e/1e3,wv={current:!1},Xp=e=>Array.isArray(e)&&typeof e[0]=="number";function bp(e){return!!(!e||typeof e=="string"&&Gp[e]||Xp(e)||Array.isArray(e)&&e.every(bp))}const pr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Gp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:pr([0,.65,.55,1]),circOut:pr([.55,0,1,.45]),backIn:pr([.31,.01,.66,-.59]),backOut:pr([.33,1.53,.69,.99])};function Kp(e){if(e)return Xp(e)?pr(e):Array.isArray(e)?e.map(Kp):Gp[e]}function Sv(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:l,times:a}={}){const u={[t]:n};a&&(u.offset=a);const f=Kp(l);return Array.isArray(f)&&(u.easing=f),e.animate(u,{delay:r,duration:i,easing:Array.isArray(f)?"linear":f,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function Cv(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Qp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,kv=1e-7,Pv=12;function Tv(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=Qp(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>kv&&++l<Pv);return s}function ri(e,t,n,r){if(e===t&&n===r)return J;const i=o=>Tv(o,0,1,e,n);return o=>o===0||o===1?o:Qp(i(o),t,r)}const Ev=ri(.42,0,1,1),jv=ri(0,0,.58,1),Yp=ri(.42,0,.58,1),Rv=e=>Array.isArray(e)&&typeof e[0]!="number",Zp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Jp=e=>t=>1-e(1-t),za=e=>1-Math.sin(Math.acos(e)),qp=Jp(za),Lv=Zp(za),eh=ri(.33,1.53,.69,.99),Ua=Jp(eh),Mv=Zp(Ua),Av=e=>(e*=2)<1?.5*Ua(e):.5*(2-Math.pow(2,-10*(e-1))),Dv={linear:J,easeIn:Ev,easeInOut:Yp,easeOut:jv,circIn:za,circInOut:Lv,circOut:qp,backIn:Ua,backInOut:Mv,backOut:eh,anticipate:Av},pc=e=>{if(Array.isArray(e)){Ba(e.length===4);const[t,n,r,i]=e;return ri(t,n,r,i)}else if(typeof e=="string")return Dv[e];return e},Wa=(e,t)=>n=>!!(ti(n)&&Vy.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),th=(e,t,n)=>r=>{if(!ti(r))return r;const[i,o,s,l]=r.match(Bo);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},Vv=e=>zt(0,255,e),vs={...gn,transform:e=>Math.round(Vv(e))},rn={test:Wa("rgb","red"),parse:th("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+vs.transform(e)+", "+vs.transform(t)+", "+vs.transform(n)+", "+Pr(kr.transform(r))+")"};function Nv(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Cl={test:Wa("#"),parse:Nv,transform:rn.transform},Dn={test:Wa("hsl","hue"),parse:th("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform(Pr(t))+", "+it.transform(Pr(n))+", "+Pr(kr.transform(r))+")"},ye={test:e=>rn.test(e)||Cl.test(e)||Dn.test(e),parse:e=>rn.test(e)?rn.parse(e):Dn.test(e)?Dn.parse(e):Cl.parse(e),transform:e=>ti(e)?e:e.hasOwnProperty("red")?rn.transform(e):Dn.transform(e)},b=(e,t,n)=>-n*e+n*t+e;function xs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function Ov({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=xs(a,l,e+1/3),o=xs(a,l,e),s=xs(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const ws=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},_v=[Cl,rn,Dn],Fv=e=>_v.find(t=>t.test(e));function hc(e){const t=Fv(e);let n=t.parse(e);return t===Dn&&(n=Ov(n)),n}const nh=(e,t)=>{const n=hc(e),r=hc(t),i={...n};return o=>(i.red=ws(n.red,r.red,o),i.green=ws(n.green,r.green,o),i.blue=ws(n.blue,r.blue,o),i.alpha=b(n.alpha,r.alpha,o),rn.transform(i))};function Iv(e){var t,n;return isNaN(e)&&ti(e)&&(((t=e.match(Bo))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Lp))===null||n===void 0?void 0:n.length)||0)>0}const rh={regex:Ay,countKey:"Vars",token:"${v}",parse:J},ih={regex:Lp,countKey:"Colors",token:"${c}",parse:ye.parse},oh={regex:Bo,countKey:"Numbers",token:"${n}",parse:gn.parse};function Ss(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function ho(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Ss(n,rh),Ss(n,ih),Ss(n,oh),n}function sh(e){return ho(e).values}function lh(e){const{values:t,numColors:n,numVars:r,tokenised:i}=ho(e),o=t.length;return s=>{let l=i;for(let a=0;a<o;a++)a<r?l=l.replace(rh.token,s[a]):a<r+n?l=l.replace(ih.token,ye.transform(s[a])):l=l.replace(oh.token,Pr(s[a]));return l}}const Bv=e=>typeof e=="number"?0:e;function zv(e){const t=sh(e);return lh(e)(t.map(Bv))}const Ut={test:Iv,parse:sh,createTransformer:lh,getAnimatableNone:zv},ah=(e,t)=>n=>`${n>0?t:e}`;function uh(e,t){return typeof e=="number"?n=>b(e,t,n):ye.test(e)?nh(e,t):e.startsWith("var(")?ah(e,t):fh(e,t)}const ch=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>uh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},Uv=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=uh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},fh=(e,t)=>{const n=Ut.createTransformer(t),r=ho(e),i=ho(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?Ft(ch(r.values,i.values),n):ah(e,t)},br=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},mc=(e,t)=>n=>b(e,t,n);function Wv(e){return typeof e=="number"?mc:typeof e=="string"?ye.test(e)?nh:fh:Array.isArray(e)?ch:typeof e=="object"?Uv:mc}function $v(e,t,n){const r=[],i=n||Wv(e[0]),o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||J:t;l=Ft(a,l)}r.push(l)}return r}function dh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(Ba(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=$v(t,r,i),l=s.length,a=u=>{let f=0;if(l>1)for(;f<e.length-2&&!(u<e[f+1]);f++);const c=br(e[f],e[f+1],u);return s[f](c)};return n?u=>a(zt(e[0],e[o-1],u)):a}function Hv(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=br(0,t,r);e.push(b(n,1,i))}}function Xv(e){const t=[0];return Hv(t,e.length-1),t}function bv(e,t){return e.map(n=>n*t)}function Gv(e,t){return e.map(()=>t||Yp).splice(0,e.length-1)}function mo({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Rv(r)?r.map(pc):pc(r),o={done:!1,value:t[0]},s=bv(n&&n.length===t.length?n:Xv(t),e),l=dh(s,t,{ease:Array.isArray(i)?i:Gv(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}function ph(e,t){return t?e*(1e3/t):0}const Kv=5;function hh(e,t,n){const r=Math.max(t-Kv,0);return ph(n-e(r),t-r)}const Cs=.001,Qv=.01,Yv=10,Zv=.05,Jv=1;function qv({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=zt(Zv,Jv,s),e=zt(Qv,Yv,dt(e)),s<1?(i=u=>{const f=u*s,c=f*e,p=f-n,g=kl(u,s),v=Math.exp(-c);return Cs-p/g*v},o=u=>{const c=u*s*e,p=c*n+n,g=Math.pow(s,2)*Math.pow(u,2)*e,v=Math.exp(-c),x=kl(Math.pow(u,2),s);return(-i(u)+Cs>0?-1:1)*((p-g)*v)/x}):(i=u=>{const f=Math.exp(-u*e),c=(u-n)*e+1;return-Cs+f*c},o=u=>{const f=Math.exp(-u*e),c=(n-u)*(e*e);return f*c});const l=5/e,a=t0(i,o,l);if(e=ln(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:s*2*Math.sqrt(r*u),duration:e}}}const e0=12;function t0(e,t,n){let r=n;for(let i=1;i<e0;i++)r=r-e(r)/t(r);return r}function kl(e,t){return e*Math.sqrt(1-t*t)}const n0=["duration","bounce"],r0=["stiffness","damping","mass"];function gc(e,t){return t.some(n=>e[n]!==void 0)}function i0(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!gc(e,r0)&&gc(e,n0)){const n=qv(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function mh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:l,damping:a,mass:u,duration:f,velocity:c,isResolvedFromDuration:p}=i0({...r,velocity:-dt(r.velocity||0)}),g=c||0,v=a/(2*Math.sqrt(l*u)),x=o-i,C=dt(Math.sqrt(l/u)),y=Math.abs(x)<5;n||(n=y?.01:2),t||(t=y?.005:.5);let h;if(v<1){const m=kl(C,v);h=w=>{const S=Math.exp(-v*C*w);return o-S*((g+v*C*x)/m*Math.sin(m*w)+x*Math.cos(m*w))}}else if(v===1)h=m=>o-Math.exp(-C*m)*(x+(g+C*x)*m);else{const m=C*Math.sqrt(v*v-1);h=w=>{const S=Math.exp(-v*C*w),E=Math.min(m*w,300);return o-S*((g+v*C*x)*Math.sinh(E)+m*x*Math.cosh(E))/m}}return{calculatedDuration:p&&f||null,next:m=>{const w=h(m);if(p)s.done=m>=f;else{let S=g;m!==0&&(v<1?S=hh(h,m,w):S=0);const E=Math.abs(S)<=n,T=Math.abs(o-w)<=t;s.done=E&&T}return s.value=s.done?o:w,s}}}function yc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:u=.5,restSpeed:f}){const c=e[0],p={done:!1,value:c},g=P=>l!==void 0&&P<l||a!==void 0&&P>a,v=P=>l===void 0?a:a===void 0||Math.abs(l-P)<Math.abs(a-P)?l:a;let x=n*t;const C=c+x,y=s===void 0?C:s(C);y!==C&&(x=y-c);const h=P=>-x*Math.exp(-P/r),m=P=>y+h(P),w=P=>{const A=h(P),V=m(P);p.done=Math.abs(A)<=u,p.value=p.done?y:V};let S,E;const T=P=>{g(p.value)&&(S=P,E=mh({keyframes:[p.value,v(p.value)],velocity:hh(m,P,p.value),damping:i,stiffness:o,restDelta:u,restSpeed:f}))};return T(0),{calculatedDuration:null,next:P=>{let A=!1;return!E&&S===void 0&&(A=!0,w(P),T(P)),S!==void 0&&P>S?E.next(P-S):(!A&&w(P),p)}}}const o0=e=>{const t=({timestamp:n})=>e(n);return{start:()=>W.update(t,!0),stop:()=>yt(t),now:()=>pe.isProcessing?pe.timestamp:performance.now()}},vc=2e4;function xc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<vc;)t+=n,r=e.next(t);return t>=vc?1/0:t}const s0={decay:yc,inertia:yc,tween:mo,keyframes:mo,spring:mh};function go({autoplay:e=!0,delay:t=0,driver:n=o0,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:l="loop",onPlay:a,onStop:u,onComplete:f,onUpdate:c,...p}){let g=1,v=!1,x,C;const y=()=>{C=new Promise(N=>{x=N})};y();let h;const m=s0[i]||mo;let w;m!==mo&&typeof r[0]!="number"&&(w=dh([0,100],r,{clamp:!1}),r=[0,100]);const S=m({...p,keyframes:r});let E;l==="mirror"&&(E=m({...p,keyframes:[...r].reverse(),velocity:-(p.velocity||0)}));let T="idle",P=null,A=null,V=null;S.calculatedDuration===null&&o&&(S.calculatedDuration=xc(S));const{calculatedDuration:q}=S;let le=1/0,ge=1/0;q!==null&&(le=q+s,ge=le*(o+1)-s);let ie=0;const xt=N=>{if(A===null)return;g>0&&(A=Math.min(A,N)),g<0&&(A=Math.min(N-ge/g,A)),P!==null?ie=P:ie=Math.round(N-A)*g;const $=ie-t*(g>=0?1:-1),bt=g>=0?$<0:$>ge;ie=Math.max($,0),T==="finished"&&P===null&&(ie=ge);let Je=ie,yn=S;if(o){const Wo=Math.min(ie,ge)/le;let ii=Math.floor(Wo),Kt=Wo%1;!Kt&&Wo>=1&&(Kt=1),Kt===1&&ii--,ii=Math.min(ii,o+1),!!(ii%2)&&(l==="reverse"?(Kt=1-Kt,s&&(Kt-=s/le)):l==="mirror"&&(yn=E)),Je=zt(0,1,Kt)*le}const Re=bt?{done:!1,value:r[0]}:yn.next(Je);w&&(Re.value=w(Re.value));let{done:Gt}=Re;!bt&&q!==null&&(Gt=g>=0?ie>=ge:ie<=0);const Uh=P===null&&(T==="finished"||T==="running"&&Gt);return c&&c(Re.value),Uh&&j(),Re},Y=()=>{h&&h.stop(),h=void 0},_e=()=>{T="idle",Y(),x(),y(),A=V=null},j=()=>{T="finished",f&&f(),Y(),x()},D=()=>{if(v)return;h||(h=n(xt));const N=h.now();a&&a(),P!==null?A=N-P:(!A||T==="finished")&&(A=N),T==="finished"&&y(),V=A,P=null,T="running",h.start()};e&&D();const O={then(N,$){return C.then(N,$)},get time(){return dt(ie)},set time(N){N=ln(N),ie=N,P!==null||!h||g===0?P=N:A=h.now()-N/g},get duration(){const N=S.calculatedDuration===null?xc(S):S.calculatedDuration;return dt(N)},get speed(){return g},set speed(N){N===g||!h||(g=N,O.time=dt(ie))},get state(){return T},play:D,pause:()=>{T="paused",P=ie},stop:()=>{v=!0,T!=="idle"&&(T="idle",u&&u(),_e())},cancel:()=>{V!==null&&xt(V),_e()},complete:()=>{T="finished"},sample:N=>(A=0,xt(N))};return O}function l0(e){let t;return()=>(t===void 0&&(t=e()),t)}const a0=l0(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),u0=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Pi=10,c0=2e4,f0=(e,t)=>t.type==="spring"||e==="backgroundColor"||!bp(t.ease);function d0(e,t,{onUpdate:n,onComplete:r,...i}){if(!(a0()&&u0.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,l,a,u=!1;const f=()=>{a=new Promise(m=>{l=m})};f();let{keyframes:c,duration:p=300,ease:g,times:v}=i;if(f0(t,i)){const m=go({...i,repeat:0,delay:0});let w={done:!1,value:c[0]};const S=[];let E=0;for(;!w.done&&E<c0;)w=m.sample(E),S.push(w.value),E+=Pi;v=void 0,c=S,p=E-Pi,g="linear"}const x=Sv(e.owner.current,t,c,{...i,duration:p,ease:g,times:v}),C=()=>{u=!1,x.cancel()},y=()=>{u=!0,W.update(C),l(),f()};return x.onfinish=()=>{u||(e.set(Cv(c,i)),r&&r(),y())},{then(m,w){return a.then(m,w)},attachTimeline(m){return x.timeline=m,x.onfinish=null,J},get time(){return dt(x.currentTime||0)},set time(m){x.currentTime=ln(m)},get speed(){return x.playbackRate},set speed(m){x.playbackRate=m},get duration(){return dt(p)},play:()=>{s||(x.play(),yt(C))},pause:()=>x.pause(),stop:()=>{if(s=!0,x.playState==="idle")return;const{currentTime:m}=x;if(m){const w=go({...i,autoplay:!1});e.setWithVelocity(w.sample(m-Pi).value,w.sample(m).value,Pi)}y()},complete:()=>{u||x.finish()},cancel:y}}function p0({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:J,pause:J,stop:J,then:o=>(o(),Promise.resolve()),cancel:J,complete:J});return t?go({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const h0={type:"spring",stiffness:500,damping:25,restSpeed:10},m0=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),g0={type:"keyframes",duration:.8},y0={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},v0=(e,{keyframes:t})=>t.length>2?g0:mn.has(e)?e.startsWith("scale")?m0(t[1]):h0:y0,Pl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Ut.test(t)||t==="0")&&!t.startsWith("url(")),x0=new Set(["brightness","contrast","saturate","opacity"]);function w0(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Bo)||[];if(!r)return e;const i=n.replace(r,"");let o=x0.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const S0=/([a-z-]*)\(.*?\)/g,Tl={...Ut,getAnimatableNone:e=>{const t=e.match(S0);return t?t.map(w0).join(" "):e}},C0={...Mp,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:Tl,WebkitFilter:Tl},$a=e=>C0[e];function gh(e,t){let n=$a(e);return n!==Tl&&(n=Ut),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const yh=e=>/^0[^.\s]+$/.test(e);function k0(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||yh(e)}function P0(e,t,n,r){const i=Pl(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let l;const a=[];for(let u=0;u<o.length;u++)o[u]===null&&(o[u]=u===0?s:o[u-1]),k0(o[u])&&a.push(u),typeof o[u]=="string"&&o[u]!=="none"&&o[u]!=="0"&&(l=o[u]);if(i&&a.length&&l)for(let u=0;u<a.length;u++){const f=a[u];o[f]=gh(t,l)}return o}function T0({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:u,...f}){return!!Object.keys(f).length}function Ha(e,t){return e[t]||e.default||e}const E0={skipAnimations:!1},Xa=(e,t,n,r={})=>i=>{const o=Ha(r,e)||{},s=o.delay||r.delay||0;let{elapsed:l=0}=r;l=l-ln(s);const a=P0(t,e,n,o),u=a[0],f=a[a.length-1],c=Pl(e,u),p=Pl(e,f);let g={keyframes:a,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-l,onUpdate:v=>{t.set(v),o.onUpdate&&o.onUpdate(v)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(T0(o)||(g={...g,...v0(e,g)}),g.duration&&(g.duration=ln(g.duration)),g.repeatDelay&&(g.repeatDelay=ln(g.repeatDelay)),!c||!p||wv.current||o.type===!1||E0.skipAnimations)return p0(g);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const v=d0(t,e,g);if(v)return v}return go(g)};function yo(e){return!!(je(e)&&e.add)}const vh=e=>/^\-?\d*\.?\d+$/.test(e);function ba(e,t){e.indexOf(t)===-1&&e.push(t)}function Ga(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Ka{constructor(){this.subscriptions=[]}add(t){return ba(this.subscriptions,t),()=>Ga(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const j0=e=>!isNaN(parseFloat(e));class R0{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=pe;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,W.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>W.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=j0(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Ka);const r=this.events[t].add(n);return t==="change"?()=>{r(),W.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ph(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Kn(e,t){return new R0(e,t)}const xh=e=>t=>t.test(e),L0={test:e=>e==="auto",parse:e=>e},wh=[gn,M,it,St,Oy,Ny,L0],lr=e=>wh.find(xh(e)),M0=[...wh,ye,Ut],A0=e=>M0.find(xh(e));function D0(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Kn(n))}function V0(e,t){const n=Uo(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const l=Yy(o[s]);D0(e,s,l)}}function N0(e,t,n){var r,i;const o=Object.keys(t).filter(l=>!e.hasValue(l)),s=o.length;if(s)for(let l=0;l<s;l++){const a=o[l],u=t[a];let f=null;Array.isArray(u)&&(f=u[0]),f===null&&(f=(i=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&i!==void 0?i:t[a]),f!=null&&(typeof f=="string"&&(vh(f)||yh(f))?f=parseFloat(f):!A0(f)&&Ut.test(u)&&(f=gh(a,u)),e.addValue(a,Kn(f,{owner:e})),n[a]===void 0&&(n[a]=f),f!==null&&e.setBaseTarget(a,f))}}function O0(e,t){return t?(t[e]||t.default||t).from:void 0}function _0(e,t,n){const r={};for(const i in e){const o=O0(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function F0({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function I0(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Sh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...l}=e.makeTargetAnimatable(t);const a=e.getValue("willChange");r&&(o=r);const u=[],f=i&&e.animationState&&e.animationState.getState()[i];for(const c in l){const p=e.getValue(c),g=l[c];if(!p||g===void 0||f&&F0(f,c))continue;const v={delay:n,elapsed:0,...Ha(o||{},c)};if(window.HandoffAppearAnimations){const y=e.getProps()[Cp];if(y){const h=window.HandoffAppearAnimations(y,c,p,W);h!==null&&(v.elapsed=h,v.isHandoff=!0)}}let x=!v.isHandoff&&!I0(p,g);if(v.type==="spring"&&(p.getVelocity()||v.velocity)&&(x=!1),p.animation&&(x=!1),x)continue;p.start(Xa(c,p,g,e.shouldReduceMotion&&mn.has(c)?{type:!1}:v));const C=p.animation;yo(a)&&(a.add(c),C.then(()=>a.remove(c))),u.push(C)}return s&&Promise.all(u).then(()=>{s&&V0(e,s)}),u}function El(e,t,n={}){const r=Uo(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Sh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{const{delayChildren:u=0,staggerChildren:f,staggerDirection:c}=i;return B0(e,t,u+a,f,c,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[a,u]=l==="beforeChildren"?[o,s]:[s,o];return a().then(()=>u())}else return Promise.all([o(),s(n.delay)])}function B0(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(z0).forEach((u,f)=>{u.notify("AnimationStart",t),s.push(El(u,t,{...o,delay:n+a(f)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(s)}function z0(e,t){return e.sortNodePosition(t)}function U0(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>El(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=El(e,t,n);else{const i=typeof t=="function"?Uo(e,t,n.custom):t;r=Promise.all(Sh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const W0=[...Ma].reverse(),$0=Ma.length;function H0(e){return t=>Promise.all(t.map(({animation:n,options:r})=>U0(e,n,r)))}function X0(e){let t=H0(e);const n=G0();let r=!0;const i=(a,u)=>{const f=Uo(e,u);if(f){const{transition:c,transitionEnd:p,...g}=f;a={...a,...g,...p}}return a};function o(a){t=a(e)}function s(a,u){const f=e.getProps(),c=e.getVariantContext(!0)||{},p=[],g=new Set;let v={},x=1/0;for(let y=0;y<$0;y++){const h=W0[y],m=n[h],w=f[h]!==void 0?f[h]:c[h],S=Hr(w),E=h===u?m.isActive:null;E===!1&&(x=y);let T=w===c[h]&&w!==f[h]&&S;if(T&&r&&e.manuallyAnimateOnMount&&(T=!1),m.protectedKeys={...v},!m.isActive&&E===null||!w&&!m.prevProp||Fo(w)||typeof w=="boolean")continue;let A=b0(m.prevProp,w)||h===u&&m.isActive&&!T&&S||y>x&&S,V=!1;const q=Array.isArray(w)?w:[w];let le=q.reduce(i,{});E===!1&&(le={});const{prevResolvedValues:ge={}}=m,ie={...ge,...le},xt=Y=>{A=!0,g.has(Y)&&(V=!0,g.delete(Y)),m.needsAnimating[Y]=!0};for(const Y in ie){const _e=le[Y],j=ge[Y];if(v.hasOwnProperty(Y))continue;let D=!1;po(_e)&&po(j)?D=!Hp(_e,j):D=_e!==j,D?_e!==void 0?xt(Y):g.add(Y):_e!==void 0&&g.has(Y)?xt(Y):m.protectedKeys[Y]=!0}m.prevProp=w,m.prevResolvedValues=le,m.isActive&&(v={...v,...le}),r&&e.blockInitialAnimation&&(A=!1),A&&(!T||V)&&p.push(...q.map(Y=>({animation:Y,options:{type:h,...a}})))}if(g.size){const y={};g.forEach(h=>{const m=e.getBaseTarget(h);m!==void 0&&(y[h]=m)}),p.push({animation:y})}let C=!!p.length;return r&&(f.initial===!1||f.initial===f.animate)&&!e.manuallyAnimateOnMount&&(C=!1),r=!1,C?t(p):Promise.resolve()}function l(a,u,f){var c;if(n[a].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(g=>{var v;return(v=g.animationState)===null||v===void 0?void 0:v.setActive(a,u)}),n[a].isActive=u;const p=s(f,a);for(const g in n)n[g].protectedKeys={};return p}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n}}function b0(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Hp(t,e):!1}function Qt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function G0(){return{animate:Qt(!0),whileInView:Qt(),whileHover:Qt(),whileTap:Qt(),whileDrag:Qt(),whileFocus:Qt(),exit:Qt()}}class K0 extends Xt{constructor(t){super(t),t.animationState||(t.animationState=X0(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Fo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Q0=0;class Y0 extends Xt{constructor(){super(...arguments),this.id=Q0++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Z0={animation:{Feature:K0},exit:{Feature:Y0}},wc=(e,t)=>Math.abs(e-t);function J0(e,t){const n=wc(e.x,t.x),r=wc(e.y,t.y);return Math.sqrt(n**2+r**2)}class Ch{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const c=Ps(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,g=J0(c.offset,{x:0,y:0})>=3;if(!p&&!g)return;const{point:v}=c,{timestamp:x}=pe;this.history.push({...v,timestamp:x});const{onStart:C,onMove:y}=this.handlers;p||(C&&C(this.lastMoveEvent,c),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,c)},this.handlePointerMove=(c,p)=>{this.lastMoveEvent=c,this.lastMoveEventInfo=ks(p,this.transformPagePoint),W.update(this.updatePoint,!0)},this.handlePointerUp=(c,p)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const C=Ps(c.type==="pointercancel"?this.lastMoveEventInfo:ks(p,this.transformPagePoint),this.history);this.startEvent&&g&&g(c,C),v&&v(c,C)},!Bp(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=zo(t),l=ks(s,this.transformPagePoint),{point:a}=l,{timestamp:u}=pe;this.history=[{...a,timestamp:u}];const{onSessionStart:f}=n;f&&f(t,Ps(l,this.history)),this.removeListeners=Ft(ft(this.contextWindow,"pointermove",this.handlePointerMove),ft(this.contextWindow,"pointerup",this.handlePointerUp),ft(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),yt(this.updatePoint)}}function ks(e,t){return t?{point:t(e.point)}:e}function Sc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Ps({point:e},t){return{point:e,delta:Sc(e,kh(t)),offset:Sc(e,q0(t)),velocity:ex(t,.1)}}function q0(e){return e[0]}function kh(e){return e[e.length-1]}function ex(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=kh(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ln(t)));)n--;if(!r)return{x:0,y:0};const o=dt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Ve(e){return e.max-e.min}function jl(e,t=0,n=.01){return Math.abs(e-t)<=n}function Cc(e,t,n,r=.5){e.origin=r,e.originPoint=b(t.min,t.max,e.origin),e.scale=Ve(n)/Ve(t),(jl(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=b(n.min,n.max,e.origin)-e.originPoint,(jl(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Tr(e,t,n,r){Cc(e.x,t.x,n.x,r?r.originX:void 0),Cc(e.y,t.y,n.y,r?r.originY:void 0)}function kc(e,t,n){e.min=n.min+t.min,e.max=e.min+Ve(t)}function tx(e,t,n){kc(e.x,t.x,n.x),kc(e.y,t.y,n.y)}function Pc(e,t,n){e.min=t.min-n.min,e.max=e.min+Ve(t)}function Er(e,t,n){Pc(e.x,t.x,n.x),Pc(e.y,t.y,n.y)}function nx(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?b(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?b(n,e,r.max):Math.min(e,n)),e}function Tc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function rx(e,{top:t,left:n,bottom:r,right:i}){return{x:Tc(e.x,n,i),y:Tc(e.y,t,r)}}function Ec(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function ix(e,t){return{x:Ec(e.x,t.x),y:Ec(e.y,t.y)}}function ox(e,t){let n=.5;const r=Ve(e),i=Ve(t);return i>r?n=br(t.min,t.max-r,e.min):r>i&&(n=br(e.min,e.max-i,t.min)),zt(0,1,n)}function sx(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Rl=.35;function lx(e=Rl){return e===!1?e=0:e===!0&&(e=Rl),{x:jc(e,"left","right"),y:jc(e,"top","bottom")}}function jc(e,t,n){return{min:Rc(e,t),max:Rc(e,n)}}function Rc(e,t){return typeof e=="number"?e:e[t]||0}const Lc=()=>({translate:0,scale:1,origin:0,originPoint:0}),Vn=()=>({x:Lc(),y:Lc()}),Mc=()=>({min:0,max:0}),ee=()=>({x:Mc(),y:Mc()});function Ie(e){return[e("x"),e("y")]}function Ph({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function ax({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function ux(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ts(e){return e===void 0||e===1}function Ll({scale:e,scaleX:t,scaleY:n}){return!Ts(e)||!Ts(t)||!Ts(n)}function Jt(e){return Ll(e)||Th(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Th(e){return Ac(e.x)||Ac(e.y)}function Ac(e){return e&&e!=="0%"}function vo(e,t,n){const r=e-n,i=t*r;return n+i}function Dc(e,t,n,r,i){return i!==void 0&&(e=vo(e,i,r)),vo(e,n,r)+t}function Ml(e,t=0,n=1,r,i){e.min=Dc(e.min,t,n,r,i),e.max=Dc(e.max,t,n,r,i)}function Eh(e,{x:t,y:n}){Ml(e.x,t.translate,t.scale,t.originPoint),Ml(e.y,n.translate,n.scale,n.originPoint)}function cx(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const a=o.instance;a&&a.style&&a.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Nn(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Eh(e,s)),r&&Jt(o.latestValues)&&Nn(e,o.latestValues))}t.x=Vc(t.x),t.y=Vc(t.y)}function Vc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Pt(e,t){e.min=e.min+t,e.max=e.max+t}function Nc(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=b(e.min,e.max,o);Ml(e,t[n],t[r],s,t.scale)}const fx=["x","scaleX","originX"],dx=["y","scaleY","originY"];function Nn(e,t){Nc(e.x,t,fx),Nc(e.y,t,dx)}function jh(e,t){return Ph(ux(e.getBoundingClientRect(),t))}function px(e,t,n){const r=jh(e,n),{scroll:i}=t;return i&&(Pt(r.x,i.offset.x),Pt(r.y,i.offset.y)),r}const Rh=({current:e})=>e?e.ownerDocument.defaultView:null,hx=new WeakMap;class mx{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ee(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=f=>{const{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(zo(f,"page").point)},o=(f,c)=>{const{drag:p,dragPropagation:g,onDragStart:v}=this.getProps();if(p&&!g&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Up(p),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ie(C=>{let y=this.getAxisMotionValue(C).get()||0;if(it.test(y)){const{projection:h}=this.visualElement;if(h&&h.layout){const m=h.layout.layoutBox[C];m&&(y=Ve(m)*(parseFloat(y)/100))}}this.originPoint[C]=y}),v&&W.update(()=>v(f,c),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(f,c)=>{const{dragPropagation:p,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!p&&!this.openGlobalLock)return;const{offset:C}=c;if(g&&this.currentDirection===null){this.currentDirection=gx(C),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",c.point,C),this.updateAxis("y",c.point,C),this.visualElement.render(),x&&x(f,c)},l=(f,c)=>this.stop(f,c),a=()=>Ie(f=>{var c;return this.getAnimationState(f)==="paused"&&((c=this.getAxisMotionValue(f).animation)===null||c===void 0?void 0:c.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Ch(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Rh(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&W.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Ti(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=nx(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&An(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=rx(i.layoutBox,n):this.constraints=!1,this.elastic=lx(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ie(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=sx(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!An(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=px(r,i.root,this.visualElement.getTransformPagePoint());let s=ix(i.layout.layoutBox,o);if(n){const l=n(ax(s));this.hasMutatedConstraints=!!l,l&&(s=Ph(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=Ie(f=>{if(!Ti(f,n,this.currentDirection))return;let c=a&&a[f]||{};s&&(c={min:0,max:0});const p=i?200:1e6,g=i?40:1e7,v={type:"inertia",velocity:r?t[f]:0,bounceStiffness:p,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...o,...c};return this.startAxisValueAnimation(f,v)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(Xa(t,r,0,n))}stopAnimation(){Ie(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ie(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ie(n=>{const{drag:r}=this.getProps();if(!Ti(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-b(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!An(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ie(s=>{const l=this.getAxisMotionValue(s);if(l){const a=l.get();i[s]=ox({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ie(s=>{if(!Ti(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:u}=this.constraints[s];l.set(b(a,u,i[s]))})}addListeners(){if(!this.visualElement.current)return;hx.set(this.visualElement,this);const t=this.visualElement.current,n=ft(t,"pointerdown",a=>{const{drag:u,dragListener:f=!0}=this.getProps();u&&f&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();An(a)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=ut(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ie(f=>{const c=this.getAxisMotionValue(f);c&&(this.originPoint[f]+=a[f].translate,c.set(c.get()+a[f].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Rl,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function Ti(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function gx(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class yx extends Xt{constructor(t){super(t),this.removeGroupControls=J,this.removeListeners=J,this.controls=new mx(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||J}unmount(){this.removeGroupControls(),this.removeListeners()}}const Oc=e=>(t,n)=>{e&&W.update(()=>e(t,n))};class vx extends Xt{constructor(){super(...arguments),this.removePointerDownListener=J}onPointerDown(t){this.session=new Ch(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Rh(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Oc(t),onStart:Oc(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&W.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=ft(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function xx(){const e=L.useContext(Ra);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=L.useId();return L.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Bi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function _c(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ar={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(M.test(e))e=parseFloat(e);else return e;const n=_c(e,t.target.x),r=_c(e,t.target.y);return`${n}% ${r}%`}},wx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Ut.parse(e);if(i.length>5)return r;const o=Ut.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const u=b(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=u),typeof i[3+s]=="number"&&(i[3+s]/=u),o(i)}};class Sx extends Fl.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;jy(Cx),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Bi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||W.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Lh(e){const[t,n]=xx(),r=L.useContext(Pp);return Fl.createElement(Sx,{...e,layoutGroup:r,switchLayoutGroup:L.useContext(Tp),isPresent:t,safeToRemove:n})}const Cx={borderRadius:{...ar,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ar,borderTopRightRadius:ar,borderBottomLeftRadius:ar,borderBottomRightRadius:ar,boxShadow:wx},Mh=["TopLeft","TopRight","BottomLeft","BottomRight"],kx=Mh.length,Fc=e=>typeof e=="string"?parseFloat(e):e,Ic=e=>typeof e=="number"||M.test(e);function Px(e,t,n,r,i,o){i?(e.opacity=b(0,n.opacity!==void 0?n.opacity:1,Tx(r)),e.opacityExit=b(t.opacity!==void 0?t.opacity:1,0,Ex(r))):o&&(e.opacity=b(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<kx;s++){const l=`border${Mh[s]}Radius`;let a=Bc(t,l),u=Bc(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||Ic(a)===Ic(u)?(e[l]=Math.max(b(Fc(a),Fc(u),r),0),(it.test(u)||it.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=b(t.rotate||0,n.rotate||0,r))}function Bc(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Tx=Ah(0,.5,qp),Ex=Ah(.5,.95,J);function Ah(e,t,n){return r=>r<e?0:r>t?1:n(br(e,t,r))}function zc(e,t){e.min=t.min,e.max=t.max}function Fe(e,t){zc(e.x,t.x),zc(e.y,t.y)}function Uc(e,t,n,r,i){return e-=t,e=vo(e,1/n,r),i!==void 0&&(e=vo(e,1/i,r)),e}function jx(e,t=0,n=1,r=.5,i,o=e,s=e){if(it.test(t)&&(t=parseFloat(t),t=b(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=b(o.min,o.max,r);e===o&&(l-=t),e.min=Uc(e.min,t,n,l,i),e.max=Uc(e.max,t,n,l,i)}function Wc(e,t,[n,r,i],o,s){jx(e,t[n],t[r],t[i],t.scale,o,s)}const Rx=["x","scaleX","originX"],Lx=["y","scaleY","originY"];function $c(e,t,n,r){Wc(e.x,t,Rx,n?n.x:void 0,r?r.x:void 0),Wc(e.y,t,Lx,n?n.y:void 0,r?r.y:void 0)}function Hc(e){return e.translate===0&&e.scale===1}function Dh(e){return Hc(e.x)&&Hc(e.y)}function Mx(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Vh(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Xc(e){return Ve(e.x)/Ve(e.y)}class Ax{constructor(){this.members=[]}add(t){ba(this.members,t),t.scheduleRender()}remove(t){if(Ga(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function bc(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:u,rotateY:f}=n;a&&(r+=`rotate(${a}deg) `),u&&(r+=`rotateX(${u}deg) `),f&&(r+=`rotateY(${f}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const Dx=(e,t)=>e.depth-t.depth;class Vx{constructor(){this.children=[],this.isDirty=!1}add(t){ba(this.children,t),this.isDirty=!0}remove(t){Ga(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Dx),this.isDirty=!1,this.children.forEach(t)}}function Nx(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(yt(r),e(o-t))};return W.read(r,!0),()=>yt(r)}function Ox(e){window.MotionDebug&&window.MotionDebug.record(e)}function _x(e){return e instanceof SVGElement&&e.tagName!=="svg"}function Fx(e,t,n){const r=je(e)?e:Kn(e);return r.start(Xa("",r,t,n)),r.animation}const Gc=["","X","Y","Z"],Ix={visibility:"hidden"},Kc=1e3;let Bx=0;const qt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Nh({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=Bx++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,qt.totalNodes=qt.resolvedTargetDeltas=qt.recalculatedProjection=0,this.nodes.forEach(Wx),this.nodes.forEach(Gx),this.nodes.forEach(Kx),this.nodes.forEach($x),Ox(qt)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new Vx)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Ka),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=_x(s),this.instance=s;const{layoutId:a,layout:u,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let c;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=Nx(p,250),Bi.hasAnimatedSinceResize&&(Bi.hasAnimatedSinceResize=!1,this.nodes.forEach(Yc))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&f&&(a||u)&&this.addEventListener("didUpdate",({delta:c,hasLayoutChanged:p,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||f.getDefaultTransition()||qx,{onLayoutAnimationStart:C,onLayoutAnimationComplete:y}=f.getProps(),h=!this.targetLayout||!Vh(this.targetLayout,v)||g,m=!p&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||p&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(c,m);const w={...Ha(x,"layout"),onPlay:C,onComplete:y};(f.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else p||Yc(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,yt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Qx),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let f=0;f<this.path.length;f++){const c=this.path[f];c.shouldResetTransform=!0,c.updateScroll("snapshot"),c.options.layoutRoot&&c.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Qc);return}this.isUpdating||this.nodes.forEach(Xx),this.isUpdating=!1,this.nodes.forEach(bx),this.nodes.forEach(zx),this.nodes.forEach(Ux),this.clearAllSnapshots();const l=performance.now();pe.delta=zt(0,1e3/60,l-pe.timestamp),pe.timestamp=l,pe.isProcessing=!0,ms.update.process(pe),ms.preRender.process(pe),ms.render.process(pe),pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Hx),this.sharedNodes.forEach(Yx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,W.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){W.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ee(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Dh(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,f=u!==this.prevTransformTemplateValue;s&&(l||Jt(this.latestValues)||f)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),e1(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return ee();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(Pt(l.x,a.offset.x),Pt(l.y,a.offset.y)),l}removeElementScroll(s){const l=ee();Fe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a],{scroll:f,options:c}=u;if(u!==this.root&&f&&c.layoutScroll){if(f.isRoot){Fe(l,s);const{scroll:p}=this.root;p&&(Pt(l.x,-p.offset.x),Pt(l.y,-p.offset.y))}Pt(l.x,f.offset.x),Pt(l.y,f.offset.y)}}return l}applyTransform(s,l=!1){const a=ee();Fe(a,s);for(let u=0;u<this.path.length;u++){const f=this.path[u];!l&&f.options.layoutScroll&&f.scroll&&f!==f.root&&Nn(a,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),Jt(f.latestValues)&&Nn(a,f.latestValues)}return Jt(this.latestValues)&&Nn(a,this.latestValues),a}removeTransform(s){const l=ee();Fe(l,s);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!Jt(u.latestValues))continue;Ll(u.latestValues)&&u.updateSnapshot();const f=ee(),c=u.measurePageBox();Fe(f,c),$c(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,f)}return Jt(this.latestValues)&&$c(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(s||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:c,layoutId:p}=this.options;if(!(!this.layout||!(c||p))){if(this.resolvedRelativeTargetAt=pe.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Er(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Fe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=ee(),this.targetWithTransforms=ee()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),tx(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Fe(this.target,this.layout.layoutBox),Eh(this.target,this.targetDelta)):Fe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ee(),this.relativeTargetOrigin=ee(),Er(this.relativeTargetOrigin,this.target,g.target),Fe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}qt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ll(this.parent.latestValues)||Th(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===pe.timestamp&&(u=!1),u)return;const{layout:f,layoutId:c}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||c))return;Fe(this.layoutCorrected,this.layout.layoutBox);const p=this.treeScale.x,g=this.treeScale.y;cx(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:v}=l;if(!v){this.projectionTransform&&(this.projectionDelta=Vn(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Vn(),this.projectionDeltaWithTransform=Vn());const x=this.projectionTransform;Tr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=bc(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==p||this.treeScale.y!==g)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),qt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){const a=this.snapshot,u=a?a.latestValues:{},f={...this.latestValues},c=Vn();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const p=ee(),g=a?a.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,C=this.getStack(),y=!C||C.members.length<=1,h=!!(x&&!y&&this.options.crossfade===!0&&!this.path.some(Jx));this.animationProgress=0;let m;this.mixTargetDelta=w=>{const S=w/1e3;Zc(c.x,s.x,S),Zc(c.y,s.y,S),this.setTargetDelta(c),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Er(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Zx(this.relativeTarget,this.relativeTargetOrigin,p,S),m&&Mx(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=ee()),Fe(m,this.relativeTarget)),x&&(this.animationValues=f,Px(f,u,this.latestValues,S,h,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(yt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=W.update(()=>{Bi.hasAnimatedSinceResize=!0,this.currentAnimation=Fx(0,Kc,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Kc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:f}=s;if(!(!l||!a||!u)){if(this!==s&&this.layout&&u&&Oh(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||ee();const c=Ve(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+c;const p=Ve(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+p}Fe(l,a),Nn(l,f),Tr(this.projectionDeltaWithTransform,this.layoutCorrected,l,f)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Ax),this.sharedNodes.get(s).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const u={};for(let f=0;f<Gc.length;f++){const c="rotate"+Gc[f];a[c]&&(u[c]=a[c],s.setStaticValue(c,0))}s.render();for(const f in u)s.setStaticValue(f,u[f]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Ix;const u={visibility:""},f=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Ii(s==null?void 0:s.pointerEvents)||"",u.transform=f?f(this.latestValues,""):"none",u;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Ii(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!Jt(this.latestValues)&&(x.transform=f?f({},""):"none",this.hasProjected=!1),x}const p=c.animationValues||c.latestValues;this.applyTransformsToTarget(),u.transform=bc(this.projectionDeltaWithTransform,this.treeScale,p),f&&(u.transform=f(p,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,c.animationValues?u.opacity=c===this?(a=(l=p.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:u.opacity=c===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const x in co){if(p[x]===void 0)continue;const{correct:C,applyTo:y}=co[x],h=u.transform==="none"?p[x]:C(p[x],c);if(y){const m=y.length;for(let w=0;w<m;w++)u[y[w]]=h}else u[x]=h}return this.options.layoutId&&(u.pointerEvents=c===this?Ii(s==null?void 0:s.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Qc),this.root.sharedNodes.clear()}}}function zx(e){e.updateLayout()}function Ux(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?Ie(c=>{const p=s?n.measuredBox[c]:n.layoutBox[c],g=Ve(p);p.min=r[c].min,p.max=p.min+g}):Oh(o,n.layoutBox,r)&&Ie(c=>{const p=s?n.measuredBox[c]:n.layoutBox[c],g=Ve(r[c]);p.max=p.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[c].max=e.relativeTarget[c].min+g)});const l=Vn();Tr(l,r,n.layoutBox);const a=Vn();s?Tr(a,e.applyTransform(i,!0),n.measuredBox):Tr(a,r,n.layoutBox);const u=!Dh(l);let f=!1;if(!e.resumeFrom){const c=e.getClosestProjectingParent();if(c&&!c.resumeFrom){const{snapshot:p,layout:g}=c;if(p&&g){const v=ee();Er(v,n.layoutBox,p.layoutBox);const x=ee();Er(x,r,g.layoutBox),Vh(v,x)||(f=!0),c.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=c)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:f})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Wx(e){qt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function $x(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Hx(e){e.clearSnapshot()}function Qc(e){e.clearMeasurements()}function Xx(e){e.isLayoutDirty=!1}function bx(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Yc(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Gx(e){e.resolveTargetDelta()}function Kx(e){e.calcProjection()}function Qx(e){e.resetRotation()}function Yx(e){e.removeLeadSnapshot()}function Zc(e,t,n){e.translate=b(t.translate,0,n),e.scale=b(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Jc(e,t,n,r){e.min=b(t.min,n.min,r),e.max=b(t.max,n.max,r)}function Zx(e,t,n,r){Jc(e.x,t.x,n.x,r),Jc(e.y,t.y,n.y,r)}function Jx(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const qx={duration:.45,ease:[.4,0,.1,1]},qc=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),ef=qc("applewebkit/")&&!qc("chrome/")?Math.round:J;function tf(e){e.min=ef(e.min),e.max=ef(e.max)}function e1(e){tf(e.x),tf(e.y)}function Oh(e,t,n){return e==="position"||e==="preserve-aspect"&&!jl(Xc(t),Xc(n),.2)}const t1=Nh({attachResizeListener:(e,t)=>ut(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Es={current:void 0},_h=Nh({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Es.current){const e=new t1({});e.mount(window),e.setOptions({layoutScroll:!0}),Es.current=e}return Es.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),n1={pan:{Feature:vx},drag:{Feature:yx,ProjectionNode:_h,MeasureLayout:Lh}},r1=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function i1(e){const t=r1.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Al(e,t,n=1){const[r,i]=i1(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return vh(s)?parseFloat(s):s}else return wl(i)?Al(i,t,n+1):i}function o1(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!wl(o))return;const s=Al(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!wl(o))continue;const s=Al(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const s1=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Fh=e=>s1.has(e),l1=e=>Object.keys(e).some(Fh),nf=e=>e===gn||e===M,rf=(e,t)=>parseFloat(e.split(", ")[t]),of=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return rf(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?rf(o[1],e):0}},a1=new Set(["x","y","z"]),u1=ei.filter(e=>!a1.has(e));function c1(e){const t=[];return u1.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Qn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:of(4,13),y:of(5,14)};Qn.translateX=Qn.x;Qn.translateY=Qn.y;const f1=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{l[u]=Qn[u](r,o)}),t.render();const a=t.measureViewportBox();return n.forEach(u=>{const f=t.getValue(u);f&&f.jump(l[u]),e[u]=Qn[u](a,o)}),e},d1=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(Fh);let o=[],s=!1;const l=[];if(i.forEach(a=>{const u=e.getValue(a);if(!e.hasValue(a))return;let f=n[a],c=lr(f);const p=t[a];let g;if(po(p)){const v=p.length,x=p[0]===null?1:0;f=p[x],c=lr(f);for(let C=x;C<v&&p[C]!==null;C++)g?Ba(lr(p[C])===g):g=lr(p[C])}else g=lr(p);if(c!==g)if(nf(c)&&nf(g)){const v=u.get();typeof v=="string"&&u.set(parseFloat(v)),typeof p=="string"?t[a]=parseFloat(p):Array.isArray(p)&&g===M&&(t[a]=p.map(parseFloat))}else c!=null&&c.transform&&(g!=null&&g.transform)&&(f===0||p===0)?f===0?u.set(g.transform(f)):t[a]=c.transform(p):(s||(o=c1(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],u.jump(p))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,u=f1(t,e,l);return o.length&&o.forEach(([f,c])=>{e.getValue(f).set(c)}),e.render(),_o&&a!==null&&window.scrollTo({top:a}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function p1(e,t,n,r){return l1(t)?d1(e,t,n,r):{target:t,transitionEnd:r}}const h1=(e,t,n,r)=>{const i=o1(e,t,r);return t=i.target,r=i.transitionEnd,p1(e,t,n,r)},Dl={current:null},Ih={current:!1};function m1(){if(Ih.current=!0,!!_o)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Dl.current=e.matches;e.addListener(t),t()}else Dl.current=!1}function g1(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(je(o))e.addValue(i,o),yo(r)&&r.add(i);else if(je(s))e.addValue(i,Kn(o,{owner:e})),yo(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const l=e.getValue(i);!l.hasAnimated&&l.set(o)}else{const l=e.getStaticValue(i);e.addValue(i,Kn(l!==void 0?l:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const sf=new WeakMap,Bh=Object.keys(Xr),y1=Bh.length,lf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],v1=Aa.length;class x1{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>W.render(this.render,!1,!0);const{latestValues:l,renderState:a}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=a,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=Io(n),this.isVariantNode=kp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...f}=this.scrapeMotionValuesFromProps(n,{});for(const c in f){const p=f[c];l[c]!==void 0&&je(p)&&(p.set(l[c],!1),yo(u)&&u.add(c))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,sf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Ih.current||m1(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Dl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){sf.delete(this.current),this.projection&&this.projection.unmount(),yt(this.notifyUpdate),yt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=mn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&W.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,l;for(let a=0;a<y1;a++){const u=Bh[a],{isEnabled:f,Feature:c,ProjectionNode:p,MeasureLayout:g}=Xr[u];p&&(s=p),f(n)&&(!this.features[u]&&c&&(this.features[u]=new c(this)),g&&(l=g))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:u,drag:f,dragConstraints:c,layoutScroll:p,layoutRoot:g}=n;this.projection.setOptions({layoutId:a,layout:u,alwaysMeasureLayout:!!f||c&&An(c),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:o,layoutScroll:p,layoutRoot:g})}return l}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ee()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<lf.length;r++){const i=lf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=g1(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<v1;r++){const i=Aa[r],o=this.props[i];(Hr(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Kn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Ia(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!je(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Ka),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class zh extends x1{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=_0(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){N0(this,r,s);const l=h1(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function w1(e){return window.getComputedStyle(e)}class S1 extends zh{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(mn.has(n)){const r=$a(n);return r&&r.default||0}else{const r=w1(t),i=(Rp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return jh(t,n)}build(t,n,r,i){Va(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Fa(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;je(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){Np(t,n,r,i)}}class C1 extends zh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(mn.has(n)){const r=$a(n);return r&&r.default||0}return n=Op.has(n)?n:La(n),t.getAttribute(n)}measureInstanceViewportBox(){return ee()}scrapeMotionValuesFromProps(t,n){return Fp(t,n)}build(t,n,r,i){Oa(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){_p(t,n,r,i)}mount(t){this.isSVGTag=_a(t.tagName),super.mount(t)}}const k1=(e,t)=>Da(e)?new C1(t,{enableHardwareAcceleration:!1}):new S1(t,{enableHardwareAcceleration:!0}),P1={layout:{ProjectionNode:_h,MeasureLayout:Lh}},T1={...Z0,...yv,...n1,...P1},Gr=Ty((e,t)=>iv(e,t,T1,k1)),Kr={BEE_DIRECTIONS:["N","NE","E","SE","S","SW","W","NW"],PHEROMONE_LEVELS:[1,2,3,4,5],WEB_STRUCTURE:{center:[2,2],rings:[[[2,2]],[[1,1],[1,2],[1,3],[2,1],[2,3],[3,1],[3,2],[3,3]],[[0,0],[0,1],[0,2],[0,3],[0,4],[1,0],[1,4],[2,0],[2,4],[3,0],[3,4],[4,0],[4,1],[4,2],[4,3],[4,4]]]},letterToBeeAngle:e=>(e.charCodeAt(0)-65)*45%360,letterToPheromone:e=>(e.charCodeAt(0)-65)%5+1,getWebPosition:e=>{const t=Kr.WEB_STRUCTURE.rings;let n=0;for(let r=0;r<t.length;r++){if(e<n+t[r].length)return{ring:r,position:t[r][e-n],isCenter:r===0};n+=t[r].length}return{ring:2,position:[e%5,Math.floor(e/5)],isCenter:!1}}};function E1({message:e,gridSize:t}){const[n,r]=L.useState([]),[i,o]=L.useState([]);L.useEffect(()=>{const l=t*t,a=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(l,"X").slice(0,l);let u=Array(l).fill(""),f=[];for(let c=0;c<a.length;c++){const p=a[c],g=Math.floor(c/t),v=c%t,x=g*t+v,C=Kr.letterToBeeAngle(p),y=Kr.letterToPheromone(p);u[x]=p,f.push({letter:p,beeAngle:C,pheromoneLevel:y,gridIndex:x,row:g,col:v})}r(u),o(f)},[e,t]);const s=l=>{const a=i.find(m=>m.gridIndex===l),u=n[l]&&n[l]!=="",f=Math.floor(l/t),c=l%t,p=Math.floor(t/2),g=f===p&&c===p,v=Math.abs(f-p)+Math.abs(c-p);let x="#fef3c7",C="#f59e0b",y="2px";g?(x="#dc2626",C="#991b1b",y="3px"):v===1?(x="#16a34a",C="#15803d"):u&&a&&(x=`rgba(34, 197, 94, ${a.pheromoneLevel/5})`);const h=Math.max(30,Math.min(60,300/t));return{width:`${h}px`,height:`${h}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`${y} solid ${C}`,borderRadius:g?"50%":"8px",backgroundColor:x,color:g?"white":"#92400e",fontWeight:"bold",fontSize:`${Math.max(10,h/3)}px`,position:"relative"}};return d.jsxs("div",{children:[d.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:n.map((l,a)=>{const u=i.find(f=>f.gridIndex===a);return d.jsxs(Gr.div,{style:s(a),initial:{scale:0,rotate:u?u.beeAngle:0},animate:{scale:1,rotate:0},transition:{delay:a*.1,type:"spring",stiffness:200,damping:10},title:u?`🐝 Angle: ${u.beeAngle}° | 🐜 Pheromone: ${u.pheromoneLevel}`:"",children:[l,u&&d.jsx("div",{style:{position:"absolute",top:"-8px",right:"-8px",width:"16px",height:"16px",borderRadius:"50%",backgroundColor:"#dc2626",color:"white",fontSize:"10px",display:"flex",alignItems:"center",justifyContent:"center"},children:u.pheromoneLevel})]},a)})}),d.jsxs("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#f9fafb",borderRadius:"8px",fontSize:"12px"},children:[d.jsxs("div",{children:[d.jsx("strong",{children:"🕷️ Spider Web:"})," Red center, green inner ring"]}),d.jsxs("div",{children:[d.jsx("strong",{children:"🐝 Bee Dance:"})," Rotation angle encodes letter"]}),d.jsxs("div",{children:[d.jsx("strong",{children:"🐜 Ant Trails:"})," Pheromone strength (1-5) in red circles"]})]})]})}function j1({message:e,gridSize:t}){const[n,r]=L.useState([]),[i,o]=L.useState(""),[s,l]=L.useState([]);return L.useEffect(()=>{const a=e.toUpperCase().replace(/[^A-Z]/g,""),u=t*t;if(a.length===0){r(Array(u).fill("")),l([]),o("");return}let f=Array.from({length:t},()=>Array(t).fill("")),c=[],p=0;const g=Math.floor(t/2);p<a.length&&(f[g][g]=a[p++]);for(let C=1;C<=g;C++){let y=[];for(let h=0;h<t;h++)for(let m=0;m<t;m++)Math.max(Math.abs(h-g),Math.abs(m-g))===C&&y.push([h,m]);y.sort((h,m)=>{const[w,S]=h,[E,T]=m,P=Math.atan2(w-g,S-g),A=Math.atan2(E-g,T-g);return P-A});for(let[h,m]of y)p<a.length&&(f[h][m]=a[p++])}let v="",x=Array(u).fill("");for(let C=0;C<t;C++)for(let y=0;y<t;y++){const h=f[C][y]||"",m=C*t+y;x[m]=h,h&&(v+=h,c.push({letter:h,beeAngle:Kr.letterToBeeAngle(h),pheromoneLevel:Kr.letterToPheromone(h),isValidPosition:!0,gridIndex:m}))}r(x),l(c),o(v.replace(/X+$/,""))},[e,t]),d.jsxs("div",{children:[d.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:n.map((a,u)=>{const f=Math.floor(u/t),c=u%t,p=Math.floor(t/2),g=f===p&&c===p,v=Math.abs(f-p)+Math.abs(c-p),x=Math.max(30,Math.min(60,300/t));let C="#dbeafe",y="#3b82f6";return g?(C="#7c3aed",y="#5b21b6"):v===1&&(C="#059669",y="#047857"),d.jsx(Gr.div,{style:{width:`${x}px`,height:`${x}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`2px solid ${y}`,borderRadius:g?"50%":"8px",backgroundColor:C,color:g||v===1?"white":"#1e40af",fontWeight:"bold",fontSize:`${Math.max(10,x/3)}px`},initial:{scale:0,rotate:180},animate:{scale:1,rotate:0},transition:{delay:u*.08,type:"spring",stiffness:150,damping:12},children:a},u)})}),i&&d.jsx(Gr.div,{style:{marginTop:"20px",padding:"15px",backgroundColor:"#dcfce7",border:"2px solid #16a34a",borderRadius:"8px",textAlign:"center"},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2.5},children:d.jsxs("strong",{style:{color:"#15803d"},children:["Decrypted Message: ",i]})})]})}function R1({isVisible:e,onToggle:t}){return e?d.jsxs(Gr.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},style:{backgroundColor:"#f0fdf4",border:"2px solid #16a34a",borderRadius:"12px",padding:"20px",marginBottom:"30px",fontSize:"14px",lineHeight:"1.6"},children:[d.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[d.jsx("h3",{style:{color:"#15803d",margin:0},children:"📝 Manual Pen & Paper Guide"}),d.jsx("button",{onClick:t,style:{padding:"5px 10px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"✕ Close"})]}),d.jsxs("div",{style:{display:"grid",gap:"25px"},children:[d.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[d.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"📋 MATERIALS NEEDED"}),d.jsxs("ul",{style:{marginLeft:"20px"},children:[d.jsx("li",{children:"✏️ Pencil and eraser"}),d.jsx("li",{children:"📄 Graph paper or ruled paper"}),d.jsx("li",{children:"📐 Ruler (optional, for neat grids)"}),d.jsx("li",{children:"🎨 Colored pens/pencils (optional, for visual coding)"})]})]}),d.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[d.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"🔐 MANUAL ENCRYPTION STEPS"}),d.jsxs("div",{style:{marginLeft:"10px"},children:[d.jsx("p",{children:d.jsx("strong",{children:"Step 1: Prepare Your Message"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsxs("p",{style:{margin:0},children:["✍️ ",d.jsx("strong",{children:"Example:"}),' "MEET AT NOON"']}),d.jsxs("p",{style:{margin:0},children:["🧹 ",d.jsx("strong",{children:"Clean:"}),' Remove spaces → "MEETATNOON"']}),d.jsxs("p",{style:{margin:0},children:["📏 ",d.jsx("strong",{children:"Count:"})," 10 letters → Need at least 4×4 grid (16 spaces)"]}),d.jsxs("p",{style:{margin:0},children:["➕ ",d.jsx("strong",{children:"Pad:"}),' "MEETATNOONXXXXXX" (16 letters total)']})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 2: Draw Your Grid"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsx("p",{style:{margin:"0 0 10px 0"},children:"📐 Draw a 4×4 grid (or your chosen size):"}),d.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",d.jsx("br",{}),"│   │   │   │   │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│   │   │   │   │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│   │   │   │   │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│   │   │   │   │",d.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 3: Fill Grid Row by Row"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsx("p",{style:{margin:"0 0 10px 0"},children:"✍️ Write letters left to right, top to bottom:"}),d.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",d.jsx("br",{}),"│ M │ E │ E │ T │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ A │ T │ N │ O │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ O │ N │ X │ X │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ X │ X │ X │ X │",d.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 4: Mark the Spider Web Pattern"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsx("p",{style:{margin:"0 0 10px 0"},children:"🕷️ Number the cells in spider web order (center outward):"}),d.jsxs("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",d.jsx("br",{}),"│ 9 │ 2 │ 3 │10 │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ 8 │ 1 │ 4 │11 │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ 7 │ 6 │ 5 │12 │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│16 │15 │14 │13 │",d.jsx("br",{}),"└───┴───┴───┴───┘"]}),d.jsxs("p",{style:{margin:"10px 0 0 0",fontSize:"12px"},children:["🎯 ",d.jsx("strong",{children:"Pattern:"})," Start at center (1), then spiral outward clockwise"]})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 5: Read in Spider Web Order"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsx("p",{style:{margin:"0 0 10px 0"},children:"📖 Follow the numbers to read letters:"}),d.jsx("p",{style:{margin:0,fontFamily:"monospace"},children:"1→T, 2→E, 3→E, 4→N, 5→X, 6→N, 7→O, 8→A, 9→M, 10→T, 11→O, 12→X, 13→X, 14→X, 15→X, 16→X"}),d.jsxs("p",{style:{margin:"10px 0 0 0",fontWeight:"bold",color:"#dc2626"},children:["🔐 ",d.jsx("strong",{children:"Encrypted Result:"})," TEENXNOAMTOXXX"]})]})]})]}),d.jsxs("div",{style:{backgroundColor:"#dbeafe",padding:"15px",borderRadius:"8px",border:"1px solid #3b82f6"},children:[d.jsx("h4",{style:{color:"#1e40af",marginTop:0},children:"🔓 MANUAL DECRYPTION STEPS"}),d.jsxs("div",{style:{marginLeft:"10px"},children:[d.jsx("p",{children:d.jsx("strong",{children:"Step 1: Prepare the Encrypted Message"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsxs("p",{style:{margin:0},children:["📝 ",d.jsx("strong",{children:"Given:"}),' "TEENXNOAMTOXXX"']}),d.jsxs("p",{style:{margin:0},children:["📏 ",d.jsx("strong",{children:"Count:"})," 14 letters → Use 4×4 grid (16 spaces)"]})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 2: Draw Empty Grid with Spider Numbers"})}),d.jsx("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:d.jsxs("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",d.jsx("br",{}),"│ 9 │ 2 │ 3 │10 │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ 8 │ 1 │ 4 │11 │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ 7 │ 6 │ 5 │12 │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│16 │15 │14 │13 │",d.jsx("br",{}),"└───┴───┴───┴───┘"]})}),d.jsx("p",{children:d.jsx("strong",{children:"Step 3: Place Letters in Spider Web Order"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsx("p",{style:{margin:"0 0 10px 0"},children:"🕷️ Place each letter according to its spider web number:"}),d.jsx("p",{style:{margin:"0 0 10px 0",fontFamily:"monospace",fontSize:"12px"},children:"T→1, E→2, E→3, N→4, X→5, N→6, O→7, A→8, M→9, T→10, O→11, X→12, X→13, X→14, X→15, X→16"}),d.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",d.jsx("br",{}),"│ M │ E │ E │ T │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ A │ T │ N │ O │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ O │ N │ X │ X │",d.jsx("br",{}),"├───┼───┼───┼───┤",d.jsx("br",{}),"│ X │ X │ X │ X │",d.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 4: Read Row by Row"})}),d.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[d.jsx("p",{style:{margin:"0 0 10px 0"},children:"📖 Read left to right, top to bottom:"}),d.jsxs("p",{style:{margin:0,fontFamily:"monospace"},children:["Row 1: M-E-E-T",d.jsx("br",{}),"Row 2: A-T-N-O",d.jsx("br",{}),"Row 3: O-N-X-X",d.jsx("br",{}),"Row 4: X-X-X-X"]}),d.jsxs("p",{style:{margin:"10px 0 0 0"},children:["🧹 ",d.jsx("strong",{children:"Remove padding X's:"})," MEETATNOON"]}),d.jsxs("p",{style:{margin:"10px 0 0 0",fontWeight:"bold",color:"#16a34a"},children:["🎉 ",d.jsx("strong",{children:"Decrypted Message:"}),' "MEET AT NOON"']})]})]})]}),d.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"1px solid #a855f7"},children:[d.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"⚡ QUICK REFERENCE CARD"}),d.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px"},children:[d.jsxs("div",{children:[d.jsx("p",{children:d.jsx("strong",{children:"🔐 Encryption:"})}),d.jsxs("ol",{style:{fontSize:"12px",marginLeft:"15px"},children:[d.jsx("li",{children:"Clean message (letters only)"}),d.jsx("li",{children:"Fill grid row by row"}),d.jsx("li",{children:"Read in spider web pattern"})]})]}),d.jsxs("div",{children:[d.jsx("p",{children:d.jsx("strong",{children:"🔓 Decryption:"})}),d.jsxs("ol",{style:{fontSize:"12px",marginLeft:"15px"},children:[d.jsx("li",{children:"Place letters in spider web order"}),d.jsx("li",{children:"Read grid row by row"}),d.jsx("li",{children:"Remove padding X's"})]})]})]}),d.jsxs("div",{style:{marginTop:"15px",padding:"10px",backgroundColor:"white",borderRadius:"4px"},children:[d.jsx("p",{style:{margin:"0 0 10px 0",fontWeight:"bold"},children:"🕷️ Spider Web Pattern (for any grid size):"}),d.jsxs("p",{style:{margin:0,fontSize:"12px"},children:["1. Find center of grid",d.jsx("br",{}),'2. Number center as "1"',d.jsx("br",{}),"3. Move outward in rings",d.jsx("br",{}),"4. Within each ring, go clockwise",d.jsx("br",{}),"5. Continue until all cells numbered"]})]})]}),d.jsxs("div",{style:{backgroundColor:"#fef2f2",padding:"15px",borderRadius:"8px",border:"1px solid #ef4444"},children:[d.jsx("h4",{style:{color:"#dc2626",marginTop:0},children:"🎭 PRESENTATION & DEMO TIPS"}),d.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[d.jsxs("li",{children:["🎨 ",d.jsx("strong",{children:"Use colors:"})," Different colors for each ring of the spider web"]}),d.jsxs("li",{children:["📏 ",d.jsx("strong",{children:"Large grids:"})," Use poster board or whiteboard for audience visibility"]}),d.jsxs("li",{children:["👥 ",d.jsx("strong",{children:"Audience participation:"})," Have volunteers help fill the grid"]}),d.jsxs("li",{children:["🔄 ",d.jsx("strong",{children:"Show both ways:"})," Encrypt a message, then decrypt it back"]}),d.jsxs("li",{children:["🌟 ",d.jsx("strong",{children:"Bio-connection:"})," Explain the spider, bee, and ant inspiration"]}),d.jsxs("li",{children:["📱 ",d.jsx("strong",{children:"Compare:"})," Show manual vs. digital tool results"]}),d.jsxs("li",{children:["🎯 ",d.jsx("strong",{children:"Start simple:"}),' Use 3×3 grid with short words like "HELLO"']})]})]})]})]}):d.jsx("div",{style:{textAlign:"center",marginBottom:"20px"},children:d.jsx("button",{onClick:t,style:{padding:"10px 20px",backgroundColor:"#059669",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold"},children:"📝 Pen & Paper Guide (For Demos)"})})}function L1({isVisible:e,onToggle:t}){return e?d.jsxs(Gr.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},style:{backgroundColor:"#f8fafc",border:"2px solid #e2e8f0",borderRadius:"12px",padding:"20px",marginBottom:"30px",fontSize:"14px",lineHeight:"1.6"},children:[d.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[d.jsx("h3",{style:{color:"#1e293b",margin:0},children:"🧠 How the Bio-Inspired Cipher Works"}),d.jsx("button",{onClick:t,style:{padding:"5px 10px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"✕ Close"})]}),d.jsxs("div",{style:{display:"grid",gap:"20px"},children:[d.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[d.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"🔐 ENCRYPTION PROCESS"}),d.jsxs("div",{style:{marginLeft:"10px"},children:[d.jsx("p",{children:d.jsx("strong",{children:"Step 1: Message Preparation"})}),d.jsxs("ul",{style:{marginLeft:"20px"},children:[d.jsx("li",{children:"Your message is cleaned (only letters A-Z kept)"}),d.jsx("li",{children:"Padded with 'X' characters to fill the grid completely"}),d.jsx("li",{children:'Example: "HELLO" → "HELLOX..." for a 5×5 grid (25 characters)'})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 2: Grid Filling (Like Ants Building)"})}),d.jsxs("ul",{style:{marginLeft:"20px"},children:[d.jsxs("li",{children:["Letters are placed in the grid ",d.jsx("em",{children:"row by row"}),", left to right"]}),d.jsx("li",{children:"Just like ants systematically building their nest"}),d.jsx("li",{children:"Each letter gets bio-inspired properties:"}),d.jsxs("li",{style:{marginLeft:"20px"},children:["🐝 ",d.jsx("strong",{children:"Bee Angle:"})," Each letter has a dance direction (A=0°, B=45°, etc.)"]}),d.jsxs("li",{style:{marginLeft:"20px"},children:["🐜 ",d.jsx("strong",{children:"Pheromone Level:"})," Strength from 1-5 based on the letter"]})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 3: Spider Web Reading Pattern"})}),d.jsxs("ul",{style:{marginLeft:"20px"},children:[d.jsxs("li",{children:["🕷️ ",d.jsx("strong",{children:"Start from center:"})," Like a spider beginning its web"]}),d.jsxs("li",{children:["📍 ",d.jsx("strong",{children:"Expand in rings:"})," Read outward in concentric circles"]}),d.jsxs("li",{children:["🔄 ",d.jsx("strong",{children:"Clockwise spiral:"})," Within each ring, go clockwise"]}),d.jsx("li",{children:"This creates the encrypted message by reading in spider-web order!"})]})]})]}),d.jsxs("div",{style:{backgroundColor:"#dbeafe",padding:"15px",borderRadius:"8px",border:"1px solid #3b82f6"},children:[d.jsx("h4",{style:{color:"#1e40af",marginTop:0},children:"🔓 DECRYPTION PROCESS"}),d.jsxs("div",{style:{marginLeft:"10px"},children:[d.jsx("p",{children:d.jsx("strong",{children:"Step 1: Reverse Spider Web Placement"})}),d.jsxs("ul",{style:{marginLeft:"20px"},children:[d.jsx("li",{children:"Take the encrypted message and place it back using spider web pattern"}),d.jsxs("li",{children:["🕷️ ",d.jsx("strong",{children:"First character:"})," Goes to center of the web"]}),d.jsxs("li",{children:["📍 ",d.jsx("strong",{children:"Next characters:"})," Fill rings outward (center → ring 1 → ring 2...)"]}),d.jsxs("li",{children:["🔄 ",d.jsx("strong",{children:"Clockwise order:"})," Within each ring, place clockwise"]})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 2: Row-by-Row Reading"})}),d.jsxs("ul",{style:{marginLeft:"20px"},children:[d.jsx("li",{children:"Once the grid is filled using spider pattern, read it normally"}),d.jsxs("li",{children:["📖 ",d.jsx("strong",{children:"Left to right, top to bottom"})," (like reading a book)"]}),d.jsx("li",{children:"This reverses the encryption and reveals the original message!"})]}),d.jsx("p",{children:d.jsx("strong",{children:"Step 3: Clean Up"})}),d.jsxs("ul",{style:{marginLeft:"20px"},children:[d.jsx("li",{children:"Remove trailing 'X' characters that were padding"}),d.jsxs("li",{children:["🎉 ",d.jsx("strong",{children:"Result:"})," Your original message is revealed!"]})]})]})]}),d.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"8px",border:"1px solid #16a34a"},children:[d.jsx("h4",{style:{color:"#15803d",marginTop:0},children:"📋 SIMPLE EXAMPLE"}),d.jsxs("div",{style:{marginLeft:"10px"},children:[d.jsxs("p",{children:[d.jsx("strong",{children:"Message:"}),' "HELLO" (using 3×3 grid)']}),d.jsxs("div",{style:{display:"flex",gap:"20px",flexWrap:"wrap",alignItems:"flex-start"},children:[d.jsxs("div",{children:[d.jsx("p",{children:d.jsx("strong",{children:"1. Fill row by row:"})}),d.jsxs("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px"},children:["H E L",d.jsx("br",{}),"L O X",d.jsx("br",{}),"X X X"]})]}),d.jsxs("div",{children:[d.jsx("p",{children:d.jsx("strong",{children:"2. Read spider-web style:"})}),d.jsxs("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px"},children:["3 1 4",d.jsx("br",{}),"2 🕷️ 5",d.jsx("br",{}),"8 7 6"]}),d.jsx("p",{style:{fontSize:"12px",margin:"5px 0"},children:"🕷️ = center (start here)"})]}),d.jsxs("div",{children:[d.jsx("p",{children:d.jsx("strong",{children:"3. Encrypted result:"})}),d.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px",fontWeight:"bold",color:"#dc2626"},children:"OHELXLXXX"}),d.jsx("p",{style:{fontSize:"12px",margin:"5px 0"},children:"Reading: O(center) → H,E,L,L(ring1) → X,X,X,X(ring2)"})]})]})]})]}),d.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"1px solid #a855f7"},children:[d.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"🌿 WHY BIO-INSPIRED?"}),d.jsxs("div",{style:{marginLeft:"10px"},children:[d.jsxs("p",{children:[d.jsx("strong",{children:"🕷️ Spider Web Construction:"})," Spiders build from center outward in organized patterns"]}),d.jsxs("p",{children:[d.jsx("strong",{children:"🐝 Bee Waggle Dance:"})," Bees communicate direction through dance angles"]}),d.jsxs("p",{children:[d.jsx("strong",{children:"🐜 Ant Pheromone Trails:"})," Ants leave chemical signals with different strengths"]}),d.jsx("p",{style:{fontStyle:"italic",color:"#6b46c1"},children:"Nature's communication methods inspire our encryption patterns, making them both beautiful and secure!"})]})]})]})]}):d.jsx("div",{style:{textAlign:"center",marginBottom:"20px"},children:d.jsx("button",{onClick:t,style:{padding:"10px 20px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold"},children:"📚 How Does This Cipher Work?"})})}function M1(){const[e,t]=L.useState("MEETATNOON"),[n,r]=L.useState("encrypt"),[i,o]=L.useState(5),[s,l]=L.useState(!1),[a,u]=L.useState(!1),f=()=>{if(n!=="encrypt")return"";const c=i*i,p=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(c,"X").slice(0,c);let g=Array.from({length:i},()=>Array(i).fill("")),v=0;for(let h=0;h<i;h++)for(let m=0;m<i;m++)v<p.length&&(g[h][m]=p[v++]);let x="";const C=Math.floor(i/2);let y=Array.from({length:i},()=>Array(i).fill(!1));g[C][C]&&(x+=g[C][C],y[C][C]=!0);for(let h=1;h<=C;h++){let m=[];for(let w=0;w<i;w++)for(let S=0;S<i;S++)Math.max(Math.abs(w-C),Math.abs(S-C))===h&&!y[w][S]&&m.push([w,S]);m.sort((w,S)=>{const[E,T]=w,[P,A]=S,V=Math.atan2(E-C,T-C),q=Math.atan2(P-C,A-C);return V-q});for(let[w,S]of m)g[w][S]&&(x+=g[w][S],y[w][S]=!0)}return x.replace(/X+$/,"")};return d.jsxs("div",{style:{backgroundColor:"#ffffff",border:"2px solid #e5e7eb",borderRadius:"16px",padding:"30px",boxShadow:"0 10px 25px rgba(0,0,0,0.1)"},children:[d.jsx("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#374151",marginBottom:"20px",textAlign:"center"},children:"�️🐝🐜 Bio-Inspired Cipher: Nature's Communication Secrets"}),d.jsxs("div",{style:{backgroundColor:"#f0f9ff",border:"1px solid #0ea5e9",borderRadius:"8px",padding:"15px",marginBottom:"20px",fontSize:"14px",textAlign:"center"},children:[d.jsx("strong",{children:"Inspired by Nature:"})," Spider web construction 🕷️ + Bee waggle dance 🐝 + Ant pheromone trails 🐜"]}),d.jsx(R1,{isVisible:a,onToggle:()=>u(!a)}),d.jsx(L1,{isVisible:s,onToggle:()=>l(!s)}),d.jsxs("div",{style:{marginBottom:"20px"},children:[d.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"Enter your message:"}),d.jsx("input",{type:"text",value:e,onChange:c=>t(c.target.value),placeholder:"Enter your message here...",style:{width:"100%",padding:"12px 16px",border:"2px solid #d1d5db",borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s"},onFocus:c=>c.target.style.borderColor="#3b82f6",onBlur:c=>c.target.style.borderColor="#d1d5db"})]}),d.jsxs("div",{style:{marginBottom:"20px",textAlign:"center"},children:[d.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"🕸️ Spider Web Size (Grid Dimensions):"}),d.jsx("div",{style:{display:"flex",gap:"8px",justifyContent:"center",flexWrap:"wrap"},children:[3,4,5,6,7,8].map(c=>d.jsxs("button",{onClick:()=>o(c),style:{padding:"8px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:i===c?"#8b5cf6":"#f3f4f6",borderColor:i===c?"#7c3aed":"#d1d5db",color:i===c?"white":"#374151",transform:i===c?"scale(1.05)":"scale(1)"},children:[c,"×",c]},c))}),d.jsxs("p",{style:{fontSize:"12px",color:"#6b7280",marginTop:"8px"},children:["Larger grids can hold more characters (",i,"×",i," = ",i*i," characters)"]})]}),d.jsxs("div",{style:{display:"flex",gap:"12px",marginBottom:"30px",justifyContent:"center"},children:[d.jsx("button",{onClick:()=>r("encrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="encrypt"?"#f59e0b":"#fef3c7",color:n==="encrypt"?"white":"#92400e",transform:n==="encrypt"?"scale(1.05)":"scale(1)"},children:"🕷️ Encrypt"}),d.jsx("button",{onClick:()=>r("decrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="decrypt"?"#3b82f6":"#dbeafe",color:n==="decrypt"?"white":"#1e40af",transform:n==="decrypt"?"scale(1.05)":"scale(1)"},children:"🔓 Decrypt"})]}),n==="encrypt"?d.jsxs("div",{children:[d.jsx("h3",{style:{textAlign:"center",color:"#92400e",marginBottom:"10px"},children:"🕷️ Spider Web Construction + 🐝 Bee Dance + 🐜 Ant Trails"}),d.jsx(E1,{message:e,gridSize:i}),e&&d.jsx("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#fef3c7",border:"2px solid #f59e0b",borderRadius:"8px",textAlign:"center"},children:d.jsxs("strong",{style:{color:"#92400e"},children:["🔐 Bio-Encrypted: ",f()]})})]}):d.jsxs("div",{children:[d.jsx("h3",{style:{textAlign:"center",color:"#1e40af",marginBottom:"10px"},children:"🔍 Bio-Pattern Analysis & Decryption"}),d.jsx(j1,{message:e,gridSize:i})]})]})}function A1(){return d.jsx("div",{style:{minHeight:"100vh",backgroundColor:"#fffbee",padding:"20px"},children:d.jsxs("div",{style:{maxWidth:"800px",margin:"0 auto"},children:[d.jsx("h1",{style:{textAlign:"center",color:"#92400e",fontSize:"2.5rem",marginBottom:"1rem"},children:"🕷️🐝🐜 Nature's Cipher Laboratory"}),d.jsx("p",{style:{textAlign:"center",color:"#6b7280",fontSize:"1.2rem",marginBottom:"2rem",fontStyle:"italic"},children:"Encryption inspired by spider webs, bee dances, and ant trails"}),d.jsx(M1,{})]})})}js.createRoot(document.getElementById("root")).render(d.jsx(Fl.StrictMode,{children:d.jsx(A1,{})}));
