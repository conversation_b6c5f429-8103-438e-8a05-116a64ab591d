(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const s of o.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function r(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();function Uh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ld={exports:{}},xo={},ad={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qr=Symbol.for("react.element"),Wh=Symbol.for("react.portal"),Hh=Symbol.for("react.fragment"),$h=Symbol.for("react.strict_mode"),Xh=Symbol.for("react.profiler"),Gh=Symbol.for("react.provider"),Kh=Symbol.for("react.context"),Qh=Symbol.for("react.forward_ref"),Yh=Symbol.for("react.suspense"),Zh=Symbol.for("react.memo"),Jh=Symbol.for("react.lazy"),Qa=Symbol.iterator;function qh(e){return e===null||typeof e!="object"?null:(e=Qa&&e[Qa]||e["@@iterator"],typeof e=="function"?e:null)}var ud={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cd=Object.assign,dd={};function Zn(e,t,n){this.props=e,this.context=t,this.refs=dd,this.updater=n||ud}Zn.prototype.isReactComponent={};Zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function fd(){}fd.prototype=Zn.prototype;function Vl(e,t,n){this.props=e,this.context=t,this.refs=dd,this.updater=n||ud}var Ol=Vl.prototype=new fd;Ol.constructor=Vl;cd(Ol,Zn.prototype);Ol.isPureReactComponent=!0;var Ya=Array.isArray,pd=Object.prototype.hasOwnProperty,Nl={current:null},hd={key:!0,ref:!0,__self:!0,__source:!0};function md(e,t,n){var r,i={},o=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(o=""+t.key),t)pd.call(t,r)&&!hd.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),c=0;c<l;c++)a[c]=arguments[c+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:Qr,type:e,key:o,ref:s,props:i,_owner:Nl.current}}function em(e,t){return{$$typeof:Qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Fl(e){return typeof e=="object"&&e!==null&&e.$$typeof===Qr}function tm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Za=/\/+/g;function Wo(e,t){return typeof e=="object"&&e!==null&&e.key!=null?tm(""+e.key):t.toString(36)}function Ei(e,t,n,r,i){var o=typeof e;(o==="undefined"||o==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Qr:case Wh:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+Wo(s,0):r,Ya(i)?(n="",e!=null&&(n=e.replace(Za,"$&/")+"/"),Ei(i,t,n,"",function(c){return c})):i!=null&&(Fl(i)&&(i=em(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(Za,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",Ya(e))for(var l=0;l<e.length;l++){o=e[l];var a=r+Wo(o,l);s+=Ei(o,t,n,a,i)}else if(a=qh(e),typeof a=="function")for(e=a.call(e),l=0;!(o=e.next()).done;)o=o.value,a=r+Wo(o,l++),s+=Ei(o,t,n,a,i);else if(o==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function oi(e,t,n){if(e==null)return e;var r=[],i=0;return Ei(e,r,"","",function(o){return t.call(n,o,i++)}),r}function nm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var we={current:null},Ti={transition:null},rm={ReactCurrentDispatcher:we,ReactCurrentBatchConfig:Ti,ReactCurrentOwner:Nl};function gd(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:oi,forEach:function(e,t,n){oi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return oi(e,function(){t++}),t},toArray:function(e){return oi(e,function(t){return t})||[]},only:function(e){if(!Fl(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=Zn;F.Fragment=Hh;F.Profiler=Xh;F.PureComponent=Vl;F.StrictMode=$h;F.Suspense=Yh;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=rm;F.act=gd;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=cd({},e.props),i=e.key,o=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(o=t.ref,s=Nl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)pd.call(t,a)&&!hd.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var c=0;c<a;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:Qr,type:e.type,key:i,ref:o,props:r,_owner:s}};F.createContext=function(e){return e={$$typeof:Kh,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Gh,_context:e},e.Consumer=e};F.createElement=md;F.createFactory=function(e){var t=md.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:Qh,render:e}};F.isValidElement=Fl;F.lazy=function(e){return{$$typeof:Jh,_payload:{_status:-1,_result:e},_init:nm}};F.memo=function(e,t){return{$$typeof:Zh,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Ti.transition;Ti.transition={};try{e()}finally{Ti.transition=t}};F.unstable_act=gd;F.useCallback=function(e,t){return we.current.useCallback(e,t)};F.useContext=function(e){return we.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return we.current.useDeferredValue(e)};F.useEffect=function(e,t){return we.current.useEffect(e,t)};F.useId=function(){return we.current.useId()};F.useImperativeHandle=function(e,t,n){return we.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return we.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return we.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return we.current.useMemo(e,t)};F.useReducer=function(e,t,n){return we.current.useReducer(e,t,n)};F.useRef=function(e){return we.current.useRef(e)};F.useState=function(e){return we.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return we.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return we.current.useTransition()};F.version="18.3.1";ad.exports=F;var R=ad.exports;const Bl=Uh(R);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var im=R,om=Symbol.for("react.element"),sm=Symbol.for("react.fragment"),lm=Object.prototype.hasOwnProperty,am=im.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,um={key:!0,ref:!0,__self:!0,__source:!0};function yd(e,t,n){var r,i={},o=null,s=null;n!==void 0&&(o=""+n),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)lm.call(t,r)&&!um.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:om,type:e,key:o,ref:s,props:i,_owner:am.current}}xo.Fragment=sm;xo.jsx=yd;xo.jsxs=yd;ld.exports=xo;var u=ld.exports,Ts={},vd={exports:{}},Oe={},xd={exports:{}},wd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,M){var N=T.length;T.push(M);e:for(;0<N;){var V=N-1>>>1,W=T[V];if(0<i(W,M))T[V]=M,T[N]=W,N=V;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var M=T[0],N=T.pop();if(N!==M){T[0]=N;e:for(var V=0,W=T.length,Gt=W>>>1;V<Gt;){var qe=2*(V+1)-1,vn=T[qe],Le=qe+1,Kt=T[Le];if(0>i(vn,N))Le<W&&0>i(Kt,vn)?(T[V]=Kt,T[Le]=N,V=Le):(T[V]=vn,T[qe]=N,V=qe);else if(Le<W&&0>i(Kt,N))T[V]=Kt,T[Le]=N,V=Le;else break e}}return M}function i(T,M){var N=T.sortIndex-M.sortIndex;return N!==0?N:T.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var o=performance;e.unstable_now=function(){return o.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],c=[],f=1,d=null,p=3,m=!1,v=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(T){for(var M=n(c);M!==null;){if(M.callback===null)r(c);else if(M.startTime<=T)r(c),M.sortIndex=M.expirationTime,t(a,M);else break;M=n(c)}}function w(T){if(x=!1,g(T),!v)if(n(a)!==null)v=!0,Y(j);else{var M=n(c);M!==null&&Fe(w,M.startTime-T)}}function j(T,M){v=!1,x&&(x=!1,y(k),k=-1),m=!0;var N=p;try{for(g(M),d=n(a);d!==null&&(!(d.expirationTime>M)||T&&!re());){var V=d.callback;if(typeof V=="function"){d.callback=null,p=d.priorityLevel;var W=V(d.expirationTime<=M);M=e.unstable_now(),typeof W=="function"?d.callback=W:d===n(a)&&r(a),g(M)}else r(a);d=n(a)}if(d!==null)var Gt=!0;else{var qe=n(c);qe!==null&&Fe(w,qe.startTime-M),Gt=!1}return Gt}finally{d=null,p=N,m=!1}}var E=!1,P=null,k=-1,O=5,D=-1;function re(){return!(e.unstable_now()-D<O)}function le(){if(P!==null){var T=e.unstable_now();D=T;var M=!0;try{M=P(!0,T)}finally{M?ge():(E=!1,P=null)}}else E=!1}var ge;if(typeof h=="function")ge=function(){h(le)};else if(typeof MessageChannel<"u"){var ie=new MessageChannel,wt=ie.port2;ie.port1.onmessage=le,ge=function(){wt.postMessage(null)}}else ge=function(){S(le,0)};function Y(T){P=T,E||(E=!0,ge())}function Fe(T,M){k=S(function(){T(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){v||m||(v=!0,Y(j))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(T){switch(p){case 1:case 2:case 3:var M=3;break;default:M=p}var N=p;p=M;try{return T()}finally{p=N}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,M){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var N=p;p=T;try{return M()}finally{p=N}},e.unstable_scheduleCallback=function(T,M,N){var V=e.unstable_now();switch(typeof N=="object"&&N!==null?(N=N.delay,N=typeof N=="number"&&0<N?V+N:V):N=V,T){case 1:var W=-1;break;case 2:W=250;break;case 5:W=**********;break;case 4:W=1e4;break;default:W=5e3}return W=N+W,T={id:f++,callback:M,priorityLevel:T,startTime:N,expirationTime:W,sortIndex:-1},N>V?(T.sortIndex=N,t(c,T),n(a)===null&&T===n(c)&&(x?(y(k),k=-1):x=!0,Fe(w,N-V))):(T.sortIndex=W,t(a,T),v||m||(v=!0,Y(j))),T},e.unstable_shouldYield=re,e.unstable_wrapCallback=function(T){var M=p;return function(){var N=p;p=M;try{return T.apply(this,arguments)}finally{p=N}}}})(wd);xd.exports=wd;var cm=xd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dm=R,De=cm;function C(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Sd=new Set,Lr={};function hn(e,t){Un(e,t),Un(e+"Capture",t)}function Un(e,t){for(Lr[e]=t,e=0;e<t.length;e++)Sd.add(t[e])}var ht=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ls=Object.prototype.hasOwnProperty,fm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Ja={},qa={};function pm(e){return Ls.call(qa,e)?!0:Ls.call(Ja,e)?!1:fm.test(e)?qa[e]=!0:(Ja[e]=!0,!1)}function hm(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function mm(e,t,n,r){if(t===null||typeof t>"u"||hm(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Se(e,t,n,r,i,o,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=s}var ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ce[e]=new Se(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ce[t]=new Se(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ce[e]=new Se(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ce[e]=new Se(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ce[e]=new Se(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ce[e]=new Se(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ce[e]=new Se(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ce[e]=new Se(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ce[e]=new Se(e,5,!1,e.toLowerCase(),null,!1,!1)});var _l=/[\-:]([a-z])/g;function Il(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_l,Il);ce[t]=new Se(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_l,Il);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_l,Il);ce[t]=new Se(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!1,!1)});ce.xlinkHref=new Se("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ce[e]=new Se(e,1,!1,e.toLowerCase(),null,!0,!0)});function bl(e,t,n,r){var i=ce.hasOwnProperty(t)?ce[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(mm(t,n,i,r)&&(n=null),r||i===null?pm(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=dm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,si=Symbol.for("react.element"),wn=Symbol.for("react.portal"),Sn=Symbol.for("react.fragment"),zl=Symbol.for("react.strict_mode"),Rs=Symbol.for("react.profiler"),jd=Symbol.for("react.provider"),Cd=Symbol.for("react.context"),Ul=Symbol.for("react.forward_ref"),As=Symbol.for("react.suspense"),Ms=Symbol.for("react.suspense_list"),Wl=Symbol.for("react.memo"),Ct=Symbol.for("react.lazy"),kd=Symbol.for("react.offscreen"),eu=Symbol.iterator;function er(e){return e===null||typeof e!="object"?null:(e=eu&&e[eu]||e["@@iterator"],typeof e=="function"?e:null)}var K=Object.assign,Ho;function cr(e){if(Ho===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ho=t&&t[1]||""}return`
`+Ho+e}var $o=!1;function Xo(e,t){if(!e||$o)return"";$o=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var i=c.stack.split(`
`),o=r.stack.split(`
`),s=i.length-1,l=o.length-1;1<=s&&0<=l&&i[s]!==o[l];)l--;for(;1<=s&&0<=l;s--,l--)if(i[s]!==o[l]){if(s!==1||l!==1)do if(s--,l--,0>l||i[s]!==o[l]){var a=`
`+i[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{$o=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?cr(e):""}function gm(e){switch(e.tag){case 5:return cr(e.type);case 16:return cr("Lazy");case 13:return cr("Suspense");case 19:return cr("SuspenseList");case 0:case 2:case 15:return e=Xo(e.type,!1),e;case 11:return e=Xo(e.type.render,!1),e;case 1:return e=Xo(e.type,!0),e;default:return""}}function Ds(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Sn:return"Fragment";case wn:return"Portal";case Rs:return"Profiler";case zl:return"StrictMode";case As:return"Suspense";case Ms:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Cd:return(e.displayName||"Context")+".Consumer";case jd:return(e._context.displayName||"Context")+".Provider";case Ul:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Wl:return t=e.displayName||null,t!==null?t:Ds(e.type)||"Memo";case Ct:t=e._payload,e=e._init;try{return Ds(e(t))}catch{}}return null}function ym(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ds(t);case 8:return t===zl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function It(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Pd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vm(e){var t=Pd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,o.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function li(e){e._valueTracker||(e._valueTracker=vm(e))}function Ed(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Pd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function bi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Vs(e,t){var n=t.checked;return K({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function tu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=It(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Td(e,t){t=t.checked,t!=null&&bl(e,"checked",t,!1)}function Os(e,t){Td(e,t);var n=It(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ns(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ns(e,t.type,It(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function nu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ns(e,t,n){(t!=="number"||bi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var dr=Array.isArray;function Fn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+It(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Fs(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(C(91));return K({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ru(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(C(92));if(dr(n)){if(1<n.length)throw Error(C(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:It(n)}}function Ld(e,t){var n=It(t.value),r=It(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function iu(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Rd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Bs(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Rd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var ai,Ad=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(ai=ai||document.createElement("div"),ai.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ai.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Rr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var mr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},xm=["Webkit","ms","Moz","O"];Object.keys(mr).forEach(function(e){xm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),mr[t]=mr[e]})});function Md(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||mr.hasOwnProperty(e)&&mr[e]?(""+t).trim():t+"px"}function Dd(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=Md(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var wm=K({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function _s(e,t){if(t){if(wm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(C(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(C(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(C(61))}if(t.style!=null&&typeof t.style!="object")throw Error(C(62))}}function Is(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var bs=null;function Hl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var zs=null,Bn=null,_n=null;function ou(e){if(e=Jr(e)){if(typeof zs!="function")throw Error(C(280));var t=e.stateNode;t&&(t=ko(t),zs(e.stateNode,e.type,t))}}function Vd(e){Bn?_n?_n.push(e):_n=[e]:Bn=e}function Od(){if(Bn){var e=Bn,t=_n;if(_n=Bn=null,ou(e),t)for(e=0;e<t.length;e++)ou(t[e])}}function Nd(e,t){return e(t)}function Fd(){}var Go=!1;function Bd(e,t,n){if(Go)return e(t,n);Go=!0;try{return Nd(e,t,n)}finally{Go=!1,(Bn!==null||_n!==null)&&(Fd(),Od())}}function Ar(e,t){var n=e.stateNode;if(n===null)return null;var r=ko(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(C(231,t,typeof n));return n}var Us=!1;if(ht)try{var tr={};Object.defineProperty(tr,"passive",{get:function(){Us=!0}}),window.addEventListener("test",tr,tr),window.removeEventListener("test",tr,tr)}catch{Us=!1}function Sm(e,t,n,r,i,o,s,l,a){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(f){this.onError(f)}}var gr=!1,zi=null,Ui=!1,Ws=null,jm={onError:function(e){gr=!0,zi=e}};function Cm(e,t,n,r,i,o,s,l,a){gr=!1,zi=null,Sm.apply(jm,arguments)}function km(e,t,n,r,i,o,s,l,a){if(Cm.apply(this,arguments),gr){if(gr){var c=zi;gr=!1,zi=null}else throw Error(C(198));Ui||(Ui=!0,Ws=c)}}function mn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function _d(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function su(e){if(mn(e)!==e)throw Error(C(188))}function Pm(e){var t=e.alternate;if(!t){if(t=mn(e),t===null)throw Error(C(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var o=i.alternate;if(o===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return su(i),e;if(o===r)return su(i),t;o=o.sibling}throw Error(C(188))}if(n.return!==r.return)n=i,r=o;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s){for(l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s)throw Error(C(189))}}if(n.alternate!==r)throw Error(C(190))}if(n.tag!==3)throw Error(C(188));return n.stateNode.current===n?e:t}function Id(e){return e=Pm(e),e!==null?bd(e):null}function bd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=bd(e);if(t!==null)return t;e=e.sibling}return null}var zd=De.unstable_scheduleCallback,lu=De.unstable_cancelCallback,Em=De.unstable_shouldYield,Tm=De.unstable_requestPaint,Z=De.unstable_now,Lm=De.unstable_getCurrentPriorityLevel,$l=De.unstable_ImmediatePriority,Ud=De.unstable_UserBlockingPriority,Wi=De.unstable_NormalPriority,Rm=De.unstable_LowPriority,Wd=De.unstable_IdlePriority,wo=null,rt=null;function Am(e){if(rt&&typeof rt.onCommitFiberRoot=="function")try{rt.onCommitFiberRoot(wo,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:Vm,Mm=Math.log,Dm=Math.LN2;function Vm(e){return e>>>=0,e===0?32:31-(Mm(e)/Dm|0)|0}var ui=64,ci=4194304;function fr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Hi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~i;l!==0?r=fr(l):(o&=s,o!==0&&(r=fr(o)))}else s=n&~i,s!==0?r=fr(s):o!==0&&(r=fr(o));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,o=t&-t,i>=o||i===16&&(o&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),i=1<<n,r|=e[n],t&=~i;return r}function Om(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Nm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var s=31-Ye(o),l=1<<s,a=i[s];a===-1?(!(l&n)||l&r)&&(i[s]=Om(l,t)):a<=t&&(e.expiredLanes|=l),o&=~l}}function Hs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Hd(){var e=ui;return ui<<=1,!(ui&4194240)&&(ui=64),e}function Ko(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Yr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function Fm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ye(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}function Xl(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var _=0;function $d(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Xd,Gl,Gd,Kd,Qd,$s=!1,di=[],At=null,Mt=null,Dt=null,Mr=new Map,Dr=new Map,Et=[],Bm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function au(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":Dt=null;break;case"pointerover":case"pointerout":Mr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Dr.delete(t.pointerId)}}function nr(e,t,n,r,i,o){return e===null||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},t!==null&&(t=Jr(t),t!==null&&Gl(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function _m(e,t,n,r,i){switch(t){case"focusin":return At=nr(At,e,t,n,r,i),!0;case"dragenter":return Mt=nr(Mt,e,t,n,r,i),!0;case"mouseover":return Dt=nr(Dt,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Mr.set(o,nr(Mr.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Dr.set(o,nr(Dr.get(o)||null,e,t,n,r,i)),!0}return!1}function Yd(e){var t=tn(e.target);if(t!==null){var n=mn(t);if(n!==null){if(t=n.tag,t===13){if(t=_d(n),t!==null){e.blockedOn=t,Qd(e.priority,function(){Gd(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Li(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Xs(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);bs=r,n.target.dispatchEvent(r),bs=null}else return t=Jr(n),t!==null&&Gl(t),e.blockedOn=n,!1;t.shift()}return!0}function uu(e,t,n){Li(e)&&n.delete(t)}function Im(){$s=!1,At!==null&&Li(At)&&(At=null),Mt!==null&&Li(Mt)&&(Mt=null),Dt!==null&&Li(Dt)&&(Dt=null),Mr.forEach(uu),Dr.forEach(uu)}function rr(e,t){e.blockedOn===t&&(e.blockedOn=null,$s||($s=!0,De.unstable_scheduleCallback(De.unstable_NormalPriority,Im)))}function Vr(e){function t(i){return rr(i,e)}if(0<di.length){rr(di[0],e);for(var n=1;n<di.length;n++){var r=di[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&rr(At,e),Mt!==null&&rr(Mt,e),Dt!==null&&rr(Dt,e),Mr.forEach(t),Dr.forEach(t),n=0;n<Et.length;n++)r=Et[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Et.length&&(n=Et[0],n.blockedOn===null);)Yd(n),n.blockedOn===null&&Et.shift()}var In=xt.ReactCurrentBatchConfig,$i=!0;function bm(e,t,n,r){var i=_,o=In.transition;In.transition=null;try{_=1,Kl(e,t,n,r)}finally{_=i,In.transition=o}}function zm(e,t,n,r){var i=_,o=In.transition;In.transition=null;try{_=4,Kl(e,t,n,r)}finally{_=i,In.transition=o}}function Kl(e,t,n,r){if($i){var i=Xs(e,t,n,r);if(i===null)is(e,t,r,Xi,n),au(e,r);else if(_m(i,e,t,n,r))r.stopPropagation();else if(au(e,r),t&4&&-1<Bm.indexOf(e)){for(;i!==null;){var o=Jr(i);if(o!==null&&Xd(o),o=Xs(e,t,n,r),o===null&&is(e,t,r,Xi,n),o===i)break;i=o}i!==null&&r.stopPropagation()}else is(e,t,r,null,n)}}var Xi=null;function Xs(e,t,n,r){if(Xi=null,e=Hl(r),e=tn(e),e!==null)if(t=mn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=_d(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Xi=e,null}function Zd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Lm()){case $l:return 1;case Ud:return 4;case Wi:case Rm:return 16;case Wd:return 536870912;default:return 16}default:return 16}}var Lt=null,Ql=null,Ri=null;function Jd(){if(Ri)return Ri;var e,t=Ql,n=t.length,r,i="value"in Lt?Lt.value:Lt.textContent,o=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[o-r];r++);return Ri=i.slice(e,1<r?1-r:void 0)}function Ai(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function fi(){return!0}function cu(){return!1}function Ne(e){function t(n,r,i,o,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=o,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(o):o[l]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?fi:cu,this.isPropagationStopped=cu,this}return K(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=fi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=fi)},persist:function(){},isPersistent:fi}),t}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Yl=Ne(Jn),Zr=K({},Jn,{view:0,detail:0}),Um=Ne(Zr),Qo,Yo,ir,So=K({},Zr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Zl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ir&&(ir&&e.type==="mousemove"?(Qo=e.screenX-ir.screenX,Yo=e.screenY-ir.screenY):Yo=Qo=0,ir=e),Qo)},movementY:function(e){return"movementY"in e?e.movementY:Yo}}),du=Ne(So),Wm=K({},So,{dataTransfer:0}),Hm=Ne(Wm),$m=K({},Zr,{relatedTarget:0}),Zo=Ne($m),Xm=K({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),Gm=Ne(Xm),Km=K({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qm=Ne(Km),Ym=K({},Jn,{data:0}),fu=Ne(Ym),Zm={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function eg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qm[e])?!!t[e]:!1}function Zl(){return eg}var tg=K({},Zr,{key:function(e){if(e.key){var t=Zm[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ai(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Zl,charCode:function(e){return e.type==="keypress"?Ai(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ai(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),ng=Ne(tg),rg=K({},So,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),pu=Ne(rg),ig=K({},Zr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Zl}),og=Ne(ig),sg=K({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),lg=Ne(sg),ag=K({},So,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ug=Ne(ag),cg=[9,13,27,32],Jl=ht&&"CompositionEvent"in window,yr=null;ht&&"documentMode"in document&&(yr=document.documentMode);var dg=ht&&"TextEvent"in window&&!yr,qd=ht&&(!Jl||yr&&8<yr&&11>=yr),hu=" ",mu=!1;function ef(e,t){switch(e){case"keyup":return cg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var jn=!1;function fg(e,t){switch(e){case"compositionend":return tf(t);case"keypress":return t.which!==32?null:(mu=!0,hu);case"textInput":return e=t.data,e===hu&&mu?null:e;default:return null}}function pg(e,t){if(jn)return e==="compositionend"||!Jl&&ef(e,t)?(e=Jd(),Ri=Ql=Lt=null,jn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qd&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function nf(e,t,n,r){Vd(r),t=Gi(t,"onChange"),0<t.length&&(n=new Yl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var vr=null,Or=null;function mg(e){hf(e,0)}function jo(e){var t=Pn(e);if(Ed(t))return e}function gg(e,t){if(e==="change")return t}var rf=!1;if(ht){var Jo;if(ht){var qo="oninput"in document;if(!qo){var yu=document.createElement("div");yu.setAttribute("oninput","return;"),qo=typeof yu.oninput=="function"}Jo=qo}else Jo=!1;rf=Jo&&(!document.documentMode||9<document.documentMode)}function vu(){vr&&(vr.detachEvent("onpropertychange",of),Or=vr=null)}function of(e){if(e.propertyName==="value"&&jo(Or)){var t=[];nf(t,Or,e,Hl(e)),Bd(mg,t)}}function yg(e,t,n){e==="focusin"?(vu(),vr=t,Or=n,vr.attachEvent("onpropertychange",of)):e==="focusout"&&vu()}function vg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return jo(Or)}function xg(e,t){if(e==="click")return jo(t)}function wg(e,t){if(e==="input"||e==="change")return jo(t)}function Sg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Je=typeof Object.is=="function"?Object.is:Sg;function Nr(e,t){if(Je(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!Ls.call(t,i)||!Je(e[i],t[i]))return!1}return!0}function xu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wu(e,t){var n=xu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=xu(n)}}function sf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?sf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function lf(){for(var e=window,t=bi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=bi(e.document)}return t}function ql(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function jg(e){var t=lf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&sf(n.ownerDocument.documentElement,n)){if(r!==null&&ql(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=r.end===void 0?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=wu(n,o);var s=wu(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Cg=ht&&"documentMode"in document&&11>=document.documentMode,Cn=null,Gs=null,xr=null,Ks=!1;function Su(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ks||Cn==null||Cn!==bi(r)||(r=Cn,"selectionStart"in r&&ql(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),xr&&Nr(xr,r)||(xr=r,r=Gi(Gs,"onSelect"),0<r.length&&(t=new Yl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Cn)))}function pi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kn={animationend:pi("Animation","AnimationEnd"),animationiteration:pi("Animation","AnimationIteration"),animationstart:pi("Animation","AnimationStart"),transitionend:pi("Transition","TransitionEnd")},es={},af={};ht&&(af=document.createElement("div").style,"AnimationEvent"in window||(delete kn.animationend.animation,delete kn.animationiteration.animation,delete kn.animationstart.animation),"TransitionEvent"in window||delete kn.transitionend.transition);function Co(e){if(es[e])return es[e];if(!kn[e])return e;var t=kn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in af)return es[e]=t[n];return e}var uf=Co("animationend"),cf=Co("animationiteration"),df=Co("animationstart"),ff=Co("transitionend"),pf=new Map,ju="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Wt(e,t){pf.set(e,t),hn(t,[e])}for(var ts=0;ts<ju.length;ts++){var ns=ju[ts],kg=ns.toLowerCase(),Pg=ns[0].toUpperCase()+ns.slice(1);Wt(kg,"on"+Pg)}Wt(uf,"onAnimationEnd");Wt(cf,"onAnimationIteration");Wt(df,"onAnimationStart");Wt("dblclick","onDoubleClick");Wt("focusin","onFocus");Wt("focusout","onBlur");Wt(ff,"onTransitionEnd");Un("onMouseEnter",["mouseout","mouseover"]);Un("onMouseLeave",["mouseout","mouseover"]);Un("onPointerEnter",["pointerout","pointerover"]);Un("onPointerLeave",["pointerout","pointerover"]);hn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));hn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));hn("onBeforeInput",["compositionend","keypress","textInput","paste"]);hn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));hn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Eg=new Set("cancel close invalid load scroll toggle".split(" ").concat(pr));function Cu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,km(r,t,void 0,e),e.currentTarget=null}function hf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,c=l.currentTarget;if(l=l.listener,a!==o&&i.isPropagationStopped())break e;Cu(i,l,c),o=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,c=l.currentTarget,l=l.listener,a!==o&&i.isPropagationStopped())break e;Cu(i,l,c),o=a}}}if(Ui)throw e=Ws,Ui=!1,Ws=null,e}function b(e,t){var n=t[qs];n===void 0&&(n=t[qs]=new Set);var r=e+"__bubble";n.has(r)||(mf(t,e,2,!1),n.add(r))}function rs(e,t,n){var r=0;t&&(r|=4),mf(n,e,r,t)}var hi="_reactListening"+Math.random().toString(36).slice(2);function Fr(e){if(!e[hi]){e[hi]=!0,Sd.forEach(function(n){n!=="selectionchange"&&(Eg.has(n)||rs(n,!1,e),rs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[hi]||(t[hi]=!0,rs("selectionchange",!1,t))}}function mf(e,t,n,r){switch(Zd(t)){case 1:var i=bm;break;case 4:i=zm;break;default:i=Kl}n=i.bind(null,t,n,e),i=void 0,!Us||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function is(e,t,n,r,i){var o=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;s=s.return}for(;l!==null;){if(s=tn(l),s===null)return;if(a=s.tag,a===5||a===6){r=o=s;continue e}l=l.parentNode}}r=r.return}Bd(function(){var c=o,f=Hl(n),d=[];e:{var p=pf.get(e);if(p!==void 0){var m=Yl,v=e;switch(e){case"keypress":if(Ai(n)===0)break e;case"keydown":case"keyup":m=ng;break;case"focusin":v="focus",m=Zo;break;case"focusout":v="blur",m=Zo;break;case"beforeblur":case"afterblur":m=Zo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=du;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=Hm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=og;break;case uf:case cf:case df:m=Gm;break;case ff:m=lg;break;case"scroll":m=Um;break;case"wheel":m=ug;break;case"copy":case"cut":case"paste":m=Qm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=pu}var x=(t&4)!==0,S=!x&&e==="scroll",y=x?p!==null?p+"Capture":null:p;x=[];for(var h=c,g;h!==null;){g=h;var w=g.stateNode;if(g.tag===5&&w!==null&&(g=w,y!==null&&(w=Ar(h,y),w!=null&&x.push(Br(h,w,g)))),S)break;h=h.return}0<x.length&&(p=new m(p,v,null,n,f),d.push({event:p,listeners:x}))}}if(!(t&7)){e:{if(p=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",p&&n!==bs&&(v=n.relatedTarget||n.fromElement)&&(tn(v)||v[mt]))break e;if((m||p)&&(p=f.window===f?f:(p=f.ownerDocument)?p.defaultView||p.parentWindow:window,m?(v=n.relatedTarget||n.toElement,m=c,v=v?tn(v):null,v!==null&&(S=mn(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(m=null,v=c),m!==v)){if(x=du,w="onMouseLeave",y="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=pu,w="onPointerLeave",y="onPointerEnter",h="pointer"),S=m==null?p:Pn(m),g=v==null?p:Pn(v),p=new x(w,h+"leave",m,n,f),p.target=S,p.relatedTarget=g,w=null,tn(f)===c&&(x=new x(y,h+"enter",v,n,f),x.target=g,x.relatedTarget=S,w=x),S=w,m&&v)t:{for(x=m,y=v,h=0,g=x;g;g=xn(g))h++;for(g=0,w=y;w;w=xn(w))g++;for(;0<h-g;)x=xn(x),h--;for(;0<g-h;)y=xn(y),g--;for(;h--;){if(x===y||y!==null&&x===y.alternate)break t;x=xn(x),y=xn(y)}x=null}else x=null;m!==null&&ku(d,p,m,x,!1),v!==null&&S!==null&&ku(d,S,v,x,!0)}}e:{if(p=c?Pn(c):window,m=p.nodeName&&p.nodeName.toLowerCase(),m==="select"||m==="input"&&p.type==="file")var j=gg;else if(gu(p))if(rf)j=wg;else{j=vg;var E=yg}else(m=p.nodeName)&&m.toLowerCase()==="input"&&(p.type==="checkbox"||p.type==="radio")&&(j=xg);if(j&&(j=j(e,c))){nf(d,j,n,f);break e}E&&E(e,p,c),e==="focusout"&&(E=p._wrapperState)&&E.controlled&&p.type==="number"&&Ns(p,"number",p.value)}switch(E=c?Pn(c):window,e){case"focusin":(gu(E)||E.contentEditable==="true")&&(Cn=E,Gs=c,xr=null);break;case"focusout":xr=Gs=Cn=null;break;case"mousedown":Ks=!0;break;case"contextmenu":case"mouseup":case"dragend":Ks=!1,Su(d,n,f);break;case"selectionchange":if(Cg)break;case"keydown":case"keyup":Su(d,n,f)}var P;if(Jl)e:{switch(e){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else jn?ef(e,n)&&(k="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(qd&&n.locale!=="ko"&&(jn||k!=="onCompositionStart"?k==="onCompositionEnd"&&jn&&(P=Jd()):(Lt=f,Ql="value"in Lt?Lt.value:Lt.textContent,jn=!0)),E=Gi(c,k),0<E.length&&(k=new fu(k,e,null,n,f),d.push({event:k,listeners:E}),P?k.data=P:(P=tf(n),P!==null&&(k.data=P)))),(P=dg?fg(e,n):pg(e,n))&&(c=Gi(c,"onBeforeInput"),0<c.length&&(f=new fu("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:c}),f.data=P))}hf(d,t)})}function Br(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Gi(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,o=i.stateNode;i.tag===5&&o!==null&&(i=o,o=Ar(e,n),o!=null&&r.unshift(Br(e,o,i)),o=Ar(e,t),o!=null&&r.push(Br(e,o,i))),e=e.return}return r}function xn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function ku(e,t,n,r,i){for(var o=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,c=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&c!==null&&(l=c,i?(a=Ar(n,o),a!=null&&s.unshift(Br(n,a,l))):i||(a=Ar(n,o),a!=null&&s.push(Br(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var Tg=/\r\n?/g,Lg=/\u0000|\uFFFD/g;function Pu(e){return(typeof e=="string"?e:""+e).replace(Tg,`
`).replace(Lg,"")}function mi(e,t,n){if(t=Pu(t),Pu(e)!==t&&n)throw Error(C(425))}function Ki(){}var Qs=null,Ys=null;function Zs(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Js=typeof setTimeout=="function"?setTimeout:void 0,Rg=typeof clearTimeout=="function"?clearTimeout:void 0,Eu=typeof Promise=="function"?Promise:void 0,Ag=typeof queueMicrotask=="function"?queueMicrotask:typeof Eu<"u"?function(e){return Eu.resolve(null).then(e).catch(Mg)}:Js;function Mg(e){setTimeout(function(){throw e})}function os(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Vr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Vr(t)}function Vt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Tu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var qn=Math.random().toString(36).slice(2),nt="__reactFiber$"+qn,_r="__reactProps$"+qn,mt="__reactContainer$"+qn,qs="__reactEvents$"+qn,Dg="__reactListeners$"+qn,Vg="__reactHandles$"+qn;function tn(e){var t=e[nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mt]||n[nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Tu(e);e!==null;){if(n=e[nt])return n;e=Tu(e)}return t}e=n,n=e.parentNode}return null}function Jr(e){return e=e[nt]||e[mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Pn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(C(33))}function ko(e){return e[_r]||null}var el=[],En=-1;function Ht(e){return{current:e}}function z(e){0>En||(e.current=el[En],el[En]=null,En--)}function I(e,t){En++,el[En]=e.current,e.current=t}var bt={},me=Ht(bt),ke=Ht(!1),un=bt;function Wn(e,t){var n=e.type.contextTypes;if(!n)return bt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},o;for(o in n)i[o]=t[o];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Pe(e){return e=e.childContextTypes,e!=null}function Qi(){z(ke),z(me)}function Lu(e,t,n){if(me.current!==bt)throw Error(C(168));I(me,t),I(ke,n)}function gf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(C(108,ym(e)||"Unknown",i));return K({},n,r)}function Yi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||bt,un=me.current,I(me,e),I(ke,ke.current),!0}function Ru(e,t,n){var r=e.stateNode;if(!r)throw Error(C(169));n?(e=gf(e,t,un),r.__reactInternalMemoizedMergedChildContext=e,z(ke),z(me),I(me,e)):z(ke),I(ke,n)}var lt=null,Po=!1,ss=!1;function yf(e){lt===null?lt=[e]:lt.push(e)}function Og(e){Po=!0,yf(e)}function $t(){if(!ss&&lt!==null){ss=!0;var e=0,t=_;try{var n=lt;for(_=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,Po=!1}catch(i){throw lt!==null&&(lt=lt.slice(e+1)),zd($l,$t),i}finally{_=t,ss=!1}}return null}var Tn=[],Ln=0,Zi=null,Ji=0,Ie=[],be=0,cn=null,at=1,ut="";function Zt(e,t){Tn[Ln++]=Ji,Tn[Ln++]=Zi,Zi=e,Ji=t}function vf(e,t,n){Ie[be++]=at,Ie[be++]=ut,Ie[be++]=cn,cn=e;var r=at;e=ut;var i=32-Ye(r)-1;r&=~(1<<i),n+=1;var o=32-Ye(t)+i;if(30<o){var s=i-i%5;o=(r&(1<<s)-1).toString(32),r>>=s,i-=s,at=1<<32-Ye(t)+i|n<<i|r,ut=o+e}else at=1<<o|n<<i|r,ut=e}function ea(e){e.return!==null&&(Zt(e,1),vf(e,1,0))}function ta(e){for(;e===Zi;)Zi=Tn[--Ln],Tn[Ln]=null,Ji=Tn[--Ln],Tn[Ln]=null;for(;e===cn;)cn=Ie[--be],Ie[be]=null,ut=Ie[--be],Ie[be]=null,at=Ie[--be],Ie[be]=null}var Me=null,Ae=null,H=!1,Qe=null;function xf(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Au(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Me=e,Ae=Vt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Me=e,Ae=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=cn!==null?{id:at,overflow:ut}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Me=e,Ae=null,!0):!1;default:return!1}}function tl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nl(e){if(H){var t=Ae;if(t){var n=t;if(!Au(e,t)){if(tl(e))throw Error(C(418));t=Vt(n.nextSibling);var r=Me;t&&Au(e,t)?xf(r,n):(e.flags=e.flags&-4097|2,H=!1,Me=e)}}else{if(tl(e))throw Error(C(418));e.flags=e.flags&-4097|2,H=!1,Me=e}}}function Mu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Me=e}function gi(e){if(e!==Me)return!1;if(!H)return Mu(e),H=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Zs(e.type,e.memoizedProps)),t&&(t=Ae)){if(tl(e))throw wf(),Error(C(418));for(;t;)xf(e,t),t=Vt(t.nextSibling)}if(Mu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(C(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ae=Vt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ae=null}}else Ae=Me?Vt(e.stateNode.nextSibling):null;return!0}function wf(){for(var e=Ae;e;)e=Vt(e.nextSibling)}function Hn(){Ae=Me=null,H=!1}function na(e){Qe===null?Qe=[e]:Qe.push(e)}var Ng=xt.ReactCurrentBatchConfig;function or(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(C(309));var r=n.stateNode}if(!r)throw Error(C(147,e));var i=r,o=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===o?t.ref:(t=function(s){var l=i.refs;s===null?delete l[o]:l[o]=s},t._stringRef=o,t)}if(typeof e!="string")throw Error(C(284));if(!n._owner)throw Error(C(290,e))}return e}function yi(e,t){throw e=Object.prototype.toString.call(t),Error(C(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Du(e){var t=e._init;return t(e._payload)}function Sf(e){function t(y,h){if(e){var g=y.deletions;g===null?(y.deletions=[h],y.flags|=16):g.push(h)}}function n(y,h){if(!e)return null;for(;h!==null;)t(y,h),h=h.sibling;return null}function r(y,h){for(y=new Map;h!==null;)h.key!==null?y.set(h.key,h):y.set(h.index,h),h=h.sibling;return y}function i(y,h){return y=Bt(y,h),y.index=0,y.sibling=null,y}function o(y,h,g){return y.index=g,e?(g=y.alternate,g!==null?(g=g.index,g<h?(y.flags|=2,h):g):(y.flags|=2,h)):(y.flags|=1048576,h)}function s(y){return e&&y.alternate===null&&(y.flags|=2),y}function l(y,h,g,w){return h===null||h.tag!==6?(h=ps(g,y.mode,w),h.return=y,h):(h=i(h,g),h.return=y,h)}function a(y,h,g,w){var j=g.type;return j===Sn?f(y,h,g.props.children,w,g.key):h!==null&&(h.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===Ct&&Du(j)===h.type)?(w=i(h,g.props),w.ref=or(y,h,g),w.return=y,w):(w=Bi(g.type,g.key,g.props,null,y.mode,w),w.ref=or(y,h,g),w.return=y,w)}function c(y,h,g,w){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=hs(g,y.mode,w),h.return=y,h):(h=i(h,g.children||[]),h.return=y,h)}function f(y,h,g,w,j){return h===null||h.tag!==7?(h=ln(g,y.mode,w,j),h.return=y,h):(h=i(h,g),h.return=y,h)}function d(y,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ps(""+h,y.mode,g),h.return=y,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case si:return g=Bi(h.type,h.key,h.props,null,y.mode,g),g.ref=or(y,null,h),g.return=y,g;case wn:return h=hs(h,y.mode,g),h.return=y,h;case Ct:var w=h._init;return d(y,w(h._payload),g)}if(dr(h)||er(h))return h=ln(h,y.mode,g,null),h.return=y,h;yi(y,h)}return null}function p(y,h,g,w){var j=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return j!==null?null:l(y,h,""+g,w);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case si:return g.key===j?a(y,h,g,w):null;case wn:return g.key===j?c(y,h,g,w):null;case Ct:return j=g._init,p(y,h,j(g._payload),w)}if(dr(g)||er(g))return j!==null?null:f(y,h,g,w,null);yi(y,g)}return null}function m(y,h,g,w,j){if(typeof w=="string"&&w!==""||typeof w=="number")return y=y.get(g)||null,l(h,y,""+w,j);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case si:return y=y.get(w.key===null?g:w.key)||null,a(h,y,w,j);case wn:return y=y.get(w.key===null?g:w.key)||null,c(h,y,w,j);case Ct:var E=w._init;return m(y,h,g,E(w._payload),j)}if(dr(w)||er(w))return y=y.get(g)||null,f(h,y,w,j,null);yi(h,w)}return null}function v(y,h,g,w){for(var j=null,E=null,P=h,k=h=0,O=null;P!==null&&k<g.length;k++){P.index>k?(O=P,P=null):O=P.sibling;var D=p(y,P,g[k],w);if(D===null){P===null&&(P=O);break}e&&P&&D.alternate===null&&t(y,P),h=o(D,h,k),E===null?j=D:E.sibling=D,E=D,P=O}if(k===g.length)return n(y,P),H&&Zt(y,k),j;if(P===null){for(;k<g.length;k++)P=d(y,g[k],w),P!==null&&(h=o(P,h,k),E===null?j=P:E.sibling=P,E=P);return H&&Zt(y,k),j}for(P=r(y,P);k<g.length;k++)O=m(P,y,k,g[k],w),O!==null&&(e&&O.alternate!==null&&P.delete(O.key===null?k:O.key),h=o(O,h,k),E===null?j=O:E.sibling=O,E=O);return e&&P.forEach(function(re){return t(y,re)}),H&&Zt(y,k),j}function x(y,h,g,w){var j=er(g);if(typeof j!="function")throw Error(C(150));if(g=j.call(g),g==null)throw Error(C(151));for(var E=j=null,P=h,k=h=0,O=null,D=g.next();P!==null&&!D.done;k++,D=g.next()){P.index>k?(O=P,P=null):O=P.sibling;var re=p(y,P,D.value,w);if(re===null){P===null&&(P=O);break}e&&P&&re.alternate===null&&t(y,P),h=o(re,h,k),E===null?j=re:E.sibling=re,E=re,P=O}if(D.done)return n(y,P),H&&Zt(y,k),j;if(P===null){for(;!D.done;k++,D=g.next())D=d(y,D.value,w),D!==null&&(h=o(D,h,k),E===null?j=D:E.sibling=D,E=D);return H&&Zt(y,k),j}for(P=r(y,P);!D.done;k++,D=g.next())D=m(P,y,k,D.value,w),D!==null&&(e&&D.alternate!==null&&P.delete(D.key===null?k:D.key),h=o(D,h,k),E===null?j=D:E.sibling=D,E=D);return e&&P.forEach(function(le){return t(y,le)}),H&&Zt(y,k),j}function S(y,h,g,w){if(typeof g=="object"&&g!==null&&g.type===Sn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case si:e:{for(var j=g.key,E=h;E!==null;){if(E.key===j){if(j=g.type,j===Sn){if(E.tag===7){n(y,E.sibling),h=i(E,g.props.children),h.return=y,y=h;break e}}else if(E.elementType===j||typeof j=="object"&&j!==null&&j.$$typeof===Ct&&Du(j)===E.type){n(y,E.sibling),h=i(E,g.props),h.ref=or(y,E,g),h.return=y,y=h;break e}n(y,E);break}else t(y,E);E=E.sibling}g.type===Sn?(h=ln(g.props.children,y.mode,w,g.key),h.return=y,y=h):(w=Bi(g.type,g.key,g.props,null,y.mode,w),w.ref=or(y,h,g),w.return=y,y=w)}return s(y);case wn:e:{for(E=g.key;h!==null;){if(h.key===E)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(y,h.sibling),h=i(h,g.children||[]),h.return=y,y=h;break e}else{n(y,h);break}else t(y,h);h=h.sibling}h=hs(g,y.mode,w),h.return=y,y=h}return s(y);case Ct:return E=g._init,S(y,h,E(g._payload),w)}if(dr(g))return v(y,h,g,w);if(er(g))return x(y,h,g,w);yi(y,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(y,h.sibling),h=i(h,g),h.return=y,y=h):(n(y,h),h=ps(g,y.mode,w),h.return=y,y=h),s(y)):n(y,h)}return S}var $n=Sf(!0),jf=Sf(!1),qi=Ht(null),eo=null,Rn=null,ra=null;function ia(){ra=Rn=eo=null}function oa(e){var t=qi.current;z(qi),e._currentValue=t}function rl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function bn(e,t){eo=e,ra=Rn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function He(e){var t=e._currentValue;if(ra!==e)if(e={context:e,memoizedValue:t,next:null},Rn===null){if(eo===null)throw Error(C(308));Rn=e,eo.dependencies={lanes:0,firstContext:e}}else Rn=Rn.next=e;return t}var nn=null;function sa(e){nn===null?nn=[e]:nn.push(e)}function Cf(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,sa(t)):(n.next=i.next,i.next=n),t.interleaved=n,gt(e,r)}function gt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var kt=!1;function la(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function kf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ot(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,gt(e,n)}return i=r.interleaved,i===null?(t.next=t,sa(r)):(t.next=i.next,i.next=t),r.interleaved=t,gt(e,n)}function Mi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Xl(e,n)}}function Vu(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};o===null?i=o=s:o=o.next=s,n=n.next}while(n!==null);o===null?i=o=t:o=o.next=t}else i=o=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function to(e,t,n,r){var i=e.updateQueue;kt=!1;var o=i.firstBaseUpdate,s=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,c=a.next;a.next=null,s===null?o=c:s.next=c,s=a;var f=e.alternate;f!==null&&(f=f.updateQueue,l=f.lastBaseUpdate,l!==s&&(l===null?f.firstBaseUpdate=c:l.next=c,f.lastBaseUpdate=a))}if(o!==null){var d=i.baseState;s=0,f=c=a=null,l=o;do{var p=l.lane,m=l.eventTime;if((r&p)===p){f!==null&&(f=f.next={eventTime:m,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var v=e,x=l;switch(p=t,m=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){d=v.call(m,d,p);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,p=typeof v=="function"?v.call(m,d,p):v,p==null)break e;d=K({},d,p);break e;case 2:kt=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,p=i.effects,p===null?i.effects=[l]:p.push(l))}else m={eventTime:m,lane:p,tag:l.tag,payload:l.payload,callback:l.callback,next:null},f===null?(c=f=m,a=d):f=f.next=m,s|=p;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;p=l,l=p.next,p.next=null,i.lastBaseUpdate=p,i.shared.pending=null}}while(!0);if(f===null&&(a=d),i.baseState=a,i.firstBaseUpdate=c,i.lastBaseUpdate=f,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else o===null&&(i.shared.lanes=0);fn|=s,e.lanes=s,e.memoizedState=d}}function Ou(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(C(191,i));i.call(r)}}}var qr={},it=Ht(qr),Ir=Ht(qr),br=Ht(qr);function rn(e){if(e===qr)throw Error(C(174));return e}function aa(e,t){switch(I(br,t),I(Ir,e),I(it,qr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Bs(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Bs(t,e)}z(it),I(it,t)}function Xn(){z(it),z(Ir),z(br)}function Pf(e){rn(br.current);var t=rn(it.current),n=Bs(t,e.type);t!==n&&(I(Ir,e),I(it,n))}function ua(e){Ir.current===e&&(z(it),z(Ir))}var $=Ht(0);function no(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ls=[];function ca(){for(var e=0;e<ls.length;e++)ls[e]._workInProgressVersionPrimary=null;ls.length=0}var Di=xt.ReactCurrentDispatcher,as=xt.ReactCurrentBatchConfig,dn=0,G=null,te=null,oe=null,ro=!1,wr=!1,zr=0,Fg=0;function de(){throw Error(C(321))}function da(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Je(e[n],t[n]))return!1;return!0}function fa(e,t,n,r,i,o){if(dn=o,G=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Di.current=e===null||e.memoizedState===null?bg:zg,e=n(r,i),wr){o=0;do{if(wr=!1,zr=0,25<=o)throw Error(C(301));o+=1,oe=te=null,t.updateQueue=null,Di.current=Ug,e=n(r,i)}while(wr)}if(Di.current=io,t=te!==null&&te.next!==null,dn=0,oe=te=G=null,ro=!1,t)throw Error(C(300));return e}function pa(){var e=zr!==0;return zr=0,e}function tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?G.memoizedState=oe=e:oe=oe.next=e,oe}function $e(){if(te===null){var e=G.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=oe===null?G.memoizedState:oe.next;if(t!==null)oe=t,te=e;else{if(e===null)throw Error(C(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},oe===null?G.memoizedState=oe=e:oe=oe.next=e}return oe}function Ur(e,t){return typeof t=="function"?t(e):t}function us(e){var t=$e(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=te,i=r.baseQueue,o=n.pending;if(o!==null){if(i!==null){var s=i.next;i.next=o.next,o.next=s}r.baseQueue=i=o,n.pending=null}if(i!==null){o=i.next,r=r.baseState;var l=s=null,a=null,c=o;do{var f=c.lane;if((dn&f)===f)a!==null&&(a=a.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var d={lane:f,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};a===null?(l=a=d,s=r):a=a.next=d,G.lanes|=f,fn|=f}c=c.next}while(c!==null&&c!==o);a===null?s=r:a.next=l,Je(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do o=i.lane,G.lanes|=o,fn|=o,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function cs(e){var t=$e(),n=t.queue;if(n===null)throw Error(C(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,o=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do o=e(o,s.action),s=s.next;while(s!==i);Je(o,t.memoizedState)||(Ce=!0),t.memoizedState=o,t.baseQueue===null&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ef(){}function Tf(e,t){var n=G,r=$e(),i=t(),o=!Je(r.memoizedState,i);if(o&&(r.memoizedState=i,Ce=!0),r=r.queue,ha(Af.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,Wr(9,Rf.bind(null,n,r,i,t),void 0,null),se===null)throw Error(C(349));dn&30||Lf(n,t,i)}return i}function Lf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Rf(e,t,n,r){t.value=n,t.getSnapshot=r,Mf(t)&&Df(e)}function Af(e,t,n){return n(function(){Mf(t)&&Df(e)})}function Mf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Je(e,n)}catch{return!0}}function Df(e){var t=gt(e,1);t!==null&&Ze(t,e,1,-1)}function Nu(e){var t=tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ur,lastRenderedState:e},t.queue=e,e=e.dispatch=Ig.bind(null,G,e),[t.memoizedState,e]}function Wr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=G.updateQueue,t===null?(t={lastEffect:null,stores:null},G.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Vf(){return $e().memoizedState}function Vi(e,t,n,r){var i=tt();G.flags|=e,i.memoizedState=Wr(1|t,n,void 0,r===void 0?null:r)}function Eo(e,t,n,r){var i=$e();r=r===void 0?null:r;var o=void 0;if(te!==null){var s=te.memoizedState;if(o=s.destroy,r!==null&&da(r,s.deps)){i.memoizedState=Wr(t,n,o,r);return}}G.flags|=e,i.memoizedState=Wr(1|t,n,o,r)}function Fu(e,t){return Vi(8390656,8,e,t)}function ha(e,t){return Eo(2048,8,e,t)}function Of(e,t){return Eo(4,2,e,t)}function Nf(e,t){return Eo(4,4,e,t)}function Ff(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Bf(e,t,n){return n=n!=null?n.concat([e]):null,Eo(4,4,Ff.bind(null,t,e),n)}function ma(){}function _f(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&da(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function If(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&da(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function bf(e,t,n){return dn&21?(Je(n,t)||(n=Hd(),G.lanes|=n,fn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function Bg(e,t){var n=_;_=n!==0&&4>n?n:4,e(!0);var r=as.transition;as.transition={};try{e(!1),t()}finally{_=n,as.transition=r}}function zf(){return $e().memoizedState}function _g(e,t,n){var r=Ft(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Uf(e))Wf(t,n);else if(n=Cf(e,t,n,r),n!==null){var i=xe();Ze(n,e,r,i),Hf(n,t,r)}}function Ig(e,t,n){var r=Ft(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uf(e))Wf(t,i);else{var o=e.alternate;if(e.lanes===0&&(o===null||o.lanes===0)&&(o=t.lastRenderedReducer,o!==null))try{var s=t.lastRenderedState,l=o(s,n);if(i.hasEagerState=!0,i.eagerState=l,Je(l,s)){var a=t.interleaved;a===null?(i.next=i,sa(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=Cf(e,t,i,r),n!==null&&(i=xe(),Ze(n,e,r,i),Hf(n,t,r))}}function Uf(e){var t=e.alternate;return e===G||t!==null&&t===G}function Wf(e,t){wr=ro=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Hf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Xl(e,n)}}var io={readContext:He,useCallback:de,useContext:de,useEffect:de,useImperativeHandle:de,useInsertionEffect:de,useLayoutEffect:de,useMemo:de,useReducer:de,useRef:de,useState:de,useDebugValue:de,useDeferredValue:de,useTransition:de,useMutableSource:de,useSyncExternalStore:de,useId:de,unstable_isNewReconciler:!1},bg={readContext:He,useCallback:function(e,t){return tt().memoizedState=[e,t===void 0?null:t],e},useContext:He,useEffect:Fu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Vi(4194308,4,Ff.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Vi(4194308,4,e,t)},useInsertionEffect:function(e,t){return Vi(4,2,e,t)},useMemo:function(e,t){var n=tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=_g.bind(null,G,e),[r.memoizedState,e]},useRef:function(e){var t=tt();return e={current:e},t.memoizedState=e},useState:Nu,useDebugValue:ma,useDeferredValue:function(e){return tt().memoizedState=e},useTransition:function(){var e=Nu(!1),t=e[0];return e=Bg.bind(null,e[1]),tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=G,i=tt();if(H){if(n===void 0)throw Error(C(407));n=n()}else{if(n=t(),se===null)throw Error(C(349));dn&30||Lf(r,t,n)}i.memoizedState=n;var o={value:n,getSnapshot:t};return i.queue=o,Fu(Af.bind(null,r,o,e),[e]),r.flags|=2048,Wr(9,Rf.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=tt(),t=se.identifierPrefix;if(H){var n=ut,r=at;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=zr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Fg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},zg={readContext:He,useCallback:_f,useContext:He,useEffect:ha,useImperativeHandle:Bf,useInsertionEffect:Of,useLayoutEffect:Nf,useMemo:If,useReducer:us,useRef:Vf,useState:function(){return us(Ur)},useDebugValue:ma,useDeferredValue:function(e){var t=$e();return bf(t,te.memoizedState,e)},useTransition:function(){var e=us(Ur)[0],t=$e().memoizedState;return[e,t]},useMutableSource:Ef,useSyncExternalStore:Tf,useId:zf,unstable_isNewReconciler:!1},Ug={readContext:He,useCallback:_f,useContext:He,useEffect:ha,useImperativeHandle:Bf,useInsertionEffect:Of,useLayoutEffect:Nf,useMemo:If,useReducer:cs,useRef:Vf,useState:function(){return cs(Ur)},useDebugValue:ma,useDeferredValue:function(e){var t=$e();return te===null?t.memoizedState=e:bf(t,te.memoizedState,e)},useTransition:function(){var e=cs(Ur)[0],t=$e().memoizedState;return[e,t]},useMutableSource:Ef,useSyncExternalStore:Tf,useId:zf,unstable_isNewReconciler:!1};function Ge(e,t){if(e&&e.defaultProps){t=K({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function il(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:K({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var To={isMounted:function(e){return(e=e._reactInternals)?mn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Ft(e),o=dt(r,i);o.payload=t,n!=null&&(o.callback=n),t=Ot(e,o,i),t!==null&&(Ze(t,e,i,r),Mi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=xe(),i=Ft(e),o=dt(r,i);o.tag=1,o.payload=t,n!=null&&(o.callback=n),t=Ot(e,o,i),t!==null&&(Ze(t,e,i,r),Mi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=xe(),r=Ft(e),i=dt(n,r);i.tag=2,t!=null&&(i.callback=t),t=Ot(e,i,r),t!==null&&(Ze(t,e,r,n),Mi(t,e,r))}};function Bu(e,t,n,r,i,o,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,o,s):t.prototype&&t.prototype.isPureReactComponent?!Nr(n,r)||!Nr(i,o):!0}function $f(e,t,n){var r=!1,i=bt,o=t.contextType;return typeof o=="object"&&o!==null?o=He(o):(i=Pe(t)?un:me.current,r=t.contextTypes,o=(r=r!=null)?Wn(e,i):bt),t=new t(n,o),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=To,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function _u(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&To.enqueueReplaceState(t,t.state,null)}function ol(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},la(e);var o=t.contextType;typeof o=="object"&&o!==null?i.context=He(o):(o=Pe(t)?un:me.current,i.context=Wn(e,o)),i.state=e.memoizedState,o=t.getDerivedStateFromProps,typeof o=="function"&&(il(e,t,o,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&To.enqueueReplaceState(i,i.state,null),to(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Gn(e,t){try{var n="",r=t;do n+=gm(r),r=r.return;while(r);var i=n}catch(o){i=`
Error generating stack: `+o.message+`
`+o.stack}return{value:e,source:t,stack:i,digest:null}}function ds(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function sl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Wg=typeof WeakMap=="function"?WeakMap:Map;function Xf(e,t,n){n=dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){so||(so=!0,gl=r),sl(e,t)},n}function Gf(e,t,n){n=dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){sl(e,t)}}var o=e.stateNode;return o!==null&&typeof o.componentDidCatch=="function"&&(n.callback=function(){sl(e,t),typeof r!="function"&&(Nt===null?Nt=new Set([this]):Nt.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Iu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Wg;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=ry.bind(null,e,t,n),t.then(e,e))}function bu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function zu(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=dt(-1,1),t.tag=2,Ot(n,t,1))),n.lanes|=1),e)}var Hg=xt.ReactCurrentOwner,Ce=!1;function ve(e,t,n,r){t.child=e===null?jf(t,null,n,r):$n(t,e.child,n,r)}function Uu(e,t,n,r,i){n=n.render;var o=t.ref;return bn(t,i),r=fa(e,t,n,r,o,i),n=pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):(H&&n&&ea(t),t.flags|=1,ve(e,t,r,i),t.child)}function Wu(e,t,n,r,i){if(e===null){var o=n.type;return typeof o=="function"&&!Ca(o)&&o.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=o,Kf(e,t,o,r,i)):(e=Bi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(o=e.child,!(e.lanes&i)){var s=o.memoizedProps;if(n=n.compare,n=n!==null?n:Nr,n(s,r)&&e.ref===t.ref)return yt(e,t,i)}return t.flags|=1,e=Bt(o,r),e.ref=t.ref,e.return=t,t.child=e}function Kf(e,t,n,r,i){if(e!==null){var o=e.memoizedProps;if(Nr(o,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=o,(e.lanes&i)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,yt(e,t,i)}return ll(e,t,n,r,i)}function Qf(e,t,n){var r=t.pendingProps,i=r.children,o=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},I(Mn,Re),Re|=n;else{if(!(n&1073741824))return e=o!==null?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,I(Mn,Re),Re|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=o!==null?o.baseLanes:n,I(Mn,Re),Re|=r}else o!==null?(r=o.baseLanes|n,t.memoizedState=null):r=n,I(Mn,Re),Re|=r;return ve(e,t,i,n),t.child}function Yf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ll(e,t,n,r,i){var o=Pe(n)?un:me.current;return o=Wn(t,o),bn(t,i),n=fa(e,t,n,r,o,i),r=pa(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,yt(e,t,i)):(H&&r&&ea(t),t.flags|=1,ve(e,t,n,i),t.child)}function Hu(e,t,n,r,i){if(Pe(n)){var o=!0;Yi(t)}else o=!1;if(bn(t,i),t.stateNode===null)Oi(e,t),$f(t,n,r),ol(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=He(c):(c=Pe(n)?un:me.current,c=Wn(t,c));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==c)&&_u(t,s,r,c),kt=!1;var p=t.memoizedState;s.state=p,to(t,r,s,i),a=t.memoizedState,l!==r||p!==a||ke.current||kt?(typeof f=="function"&&(il(t,n,f,r),a=t.memoizedState),(l=kt||Bu(t,n,l,r,p,a,c))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=c,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,kf(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:Ge(t.type,l),s.props=c,d=t.pendingProps,p=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=He(a):(a=Pe(n)?un:me.current,a=Wn(t,a));var m=n.getDerivedStateFromProps;(f=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==d||p!==a)&&_u(t,s,r,a),kt=!1,p=t.memoizedState,s.state=p,to(t,r,s,i);var v=t.memoizedState;l!==d||p!==v||ke.current||kt?(typeof m=="function"&&(il(t,n,m,r),v=t.memoizedState),(c=kt||Bu(t,n,c,r,p,v,a)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,v,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,v,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),s.props=r,s.state=v,s.context=a,r=c):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&p===e.memoizedState||(t.flags|=1024),r=!1)}return al(e,t,n,r,o,i)}function al(e,t,n,r,i,o){Yf(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&Ru(t,n,!1),yt(e,t,o);r=t.stateNode,Hg.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=$n(t,e.child,null,o),t.child=$n(t,null,l,o)):ve(e,t,l,o),t.memoizedState=r.state,i&&Ru(t,n,!0),t.child}function Zf(e){var t=e.stateNode;t.pendingContext?Lu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Lu(e,t.context,!1),aa(e,t.containerInfo)}function $u(e,t,n,r,i){return Hn(),na(i),t.flags|=256,ve(e,t,n,r),t.child}var ul={dehydrated:null,treeContext:null,retryLane:0};function cl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Jf(e,t,n){var r=t.pendingProps,i=$.current,o=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(o=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),I($,i&1),e===null)return nl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,o?(r=t.mode,o=t.child,s={mode:"hidden",children:s},!(r&1)&&o!==null?(o.childLanes=0,o.pendingProps=s):o=Ao(s,r,0,null),e=ln(e,r,n,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=cl(n),t.memoizedState=ul,e):ga(t,s));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return $g(e,t,s,r,l,i,n);if(o){o=r.fallback,s=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Bt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?o=Bt(l,o):(o=ln(o,s,n,null),o.flags|=2),o.return=t,r.return=t,r.sibling=o,t.child=r,r=o,o=t.child,s=e.child.memoizedState,s=s===null?cl(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~n,t.memoizedState=ul,r}return o=e.child,e=o.sibling,r=Bt(o,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ga(e,t){return t=Ao({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function vi(e,t,n,r){return r!==null&&na(r),$n(t,e.child,null,n),e=ga(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function $g(e,t,n,r,i,o,s){if(n)return t.flags&256?(t.flags&=-257,r=ds(Error(C(422))),vi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(o=r.fallback,i=t.mode,r=Ao({mode:"visible",children:r.children},i,0,null),o=ln(o,i,s,null),o.flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,t.mode&1&&$n(t,e.child,null,s),t.child.memoizedState=cl(s),t.memoizedState=ul,o);if(!(t.mode&1))return vi(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,o=Error(C(419)),r=ds(o,r,void 0),vi(e,t,s,r)}if(l=(s&e.childLanes)!==0,Ce||l){if(r=se,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==o.retryLane&&(o.retryLane=i,gt(e,i),Ze(r,e,i,-1))}return ja(),r=ds(Error(C(421))),vi(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=iy.bind(null,e),i._reactRetry=t,null):(e=o.treeContext,Ae=Vt(i.nextSibling),Me=t,H=!0,Qe=null,e!==null&&(Ie[be++]=at,Ie[be++]=ut,Ie[be++]=cn,at=e.id,ut=e.overflow,cn=t),t=ga(t,r.children),t.flags|=4096,t)}function Xu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),rl(e.return,t,n)}function fs(e,t,n,r,i){var o=e.memoizedState;o===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function qf(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(ve(e,t,r.children,n),r=$.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Xu(e,n,t);else if(e.tag===19)Xu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(I($,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&no(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),fs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&no(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}fs(t,!0,n,null,o);break;case"together":fs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Oi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function yt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),fn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(C(153));if(t.child!==null){for(e=t.child,n=Bt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Bt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Xg(e,t,n){switch(t.tag){case 3:Zf(t),Hn();break;case 5:Pf(t);break;case 1:Pe(t.type)&&Yi(t);break;case 4:aa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;I(qi,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(I($,$.current&1),t.flags|=128,null):n&t.child.childLanes?Jf(e,t,n):(I($,$.current&1),e=yt(e,t,n),e!==null?e.sibling:null);I($,$.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return qf(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),I($,$.current),r)break;return null;case 22:case 23:return t.lanes=0,Qf(e,t,n)}return yt(e,t,n)}var ep,dl,tp,np;ep=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};dl=function(){};tp=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,rn(it.current);var o=null;switch(n){case"input":i=Vs(e,i),r=Vs(e,r),o=[];break;case"select":i=K({},i,{value:void 0}),r=K({},r,{value:void 0}),o=[];break;case"textarea":i=Fs(e,i),r=Fs(e,r),o=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Ki)}_s(n,r);var s;n=null;for(c in i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&i[c]!=null)if(c==="style"){var l=i[c];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Lr.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var a=r[c];if(l=i!=null?i[c]:void 0,r.hasOwnProperty(c)&&a!==l&&(a!=null||l!=null))if(c==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(o||(o=[]),o.push(c,n)),n=a;else c==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(o=o||[]).push(c,a)):c==="children"?typeof a!="string"&&typeof a!="number"||(o=o||[]).push(c,""+a):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Lr.hasOwnProperty(c)?(a!=null&&c==="onScroll"&&b("scroll",e),o||l===a||(o=[])):(o=o||[]).push(c,a))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}};np=function(e,t,n,r){n!==r&&(t.flags|=4)};function sr(e,t){if(!H)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function fe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Gg(e,t,n){var r=t.pendingProps;switch(ta(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return fe(t),null;case 1:return Pe(t.type)&&Qi(),fe(t),null;case 3:return r=t.stateNode,Xn(),z(ke),z(me),ca(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(gi(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Qe!==null&&(xl(Qe),Qe=null))),dl(e,t),fe(t),null;case 5:ua(t);var i=rn(br.current);if(n=t.type,e!==null&&t.stateNode!=null)tp(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(C(166));return fe(t),null}if(e=rn(it.current),gi(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[nt]=t,r[_r]=o,e=(t.mode&1)!==0,n){case"dialog":b("cancel",r),b("close",r);break;case"iframe":case"object":case"embed":b("load",r);break;case"video":case"audio":for(i=0;i<pr.length;i++)b(pr[i],r);break;case"source":b("error",r);break;case"img":case"image":case"link":b("error",r),b("load",r);break;case"details":b("toggle",r);break;case"input":tu(r,o),b("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},b("invalid",r);break;case"textarea":ru(r,o),b("invalid",r)}_s(n,o),i=null;for(var s in o)if(o.hasOwnProperty(s)){var l=o[s];s==="children"?typeof l=="string"?r.textContent!==l&&(o.suppressHydrationWarning!==!0&&mi(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(o.suppressHydrationWarning!==!0&&mi(r.textContent,l,e),i=["children",""+l]):Lr.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&b("scroll",r)}switch(n){case"input":li(r),nu(r,o,!0);break;case"textarea":li(r),iu(r);break;case"select":case"option":break;default:typeof o.onClick=="function"&&(r.onclick=Ki)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Rd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[nt]=t,e[_r]=r,ep(e,t,!1,!1),t.stateNode=e;e:{switch(s=Is(n,r),n){case"dialog":b("cancel",e),b("close",e),i=r;break;case"iframe":case"object":case"embed":b("load",e),i=r;break;case"video":case"audio":for(i=0;i<pr.length;i++)b(pr[i],e);i=r;break;case"source":b("error",e),i=r;break;case"img":case"image":case"link":b("error",e),b("load",e),i=r;break;case"details":b("toggle",e),i=r;break;case"input":tu(e,r),i=Vs(e,r),b("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=K({},r,{value:void 0}),b("invalid",e);break;case"textarea":ru(e,r),i=Fs(e,r),b("invalid",e);break;default:i=r}_s(n,i),l=i;for(o in l)if(l.hasOwnProperty(o)){var a=l[o];o==="style"?Dd(e,a):o==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Ad(e,a)):o==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Rr(e,a):typeof a=="number"&&Rr(e,""+a):o!=="suppressContentEditableWarning"&&o!=="suppressHydrationWarning"&&o!=="autoFocus"&&(Lr.hasOwnProperty(o)?a!=null&&o==="onScroll"&&b("scroll",e):a!=null&&bl(e,o,a,s))}switch(n){case"input":li(e),nu(e,r,!1);break;case"textarea":li(e),iu(e);break;case"option":r.value!=null&&e.setAttribute("value",""+It(r.value));break;case"select":e.multiple=!!r.multiple,o=r.value,o!=null?Fn(e,!!r.multiple,o,!1):r.defaultValue!=null&&Fn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Ki)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return fe(t),null;case 6:if(e&&t.stateNode!=null)np(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(C(166));if(n=rn(br.current),rn(it.current),gi(t)){if(r=t.stateNode,n=t.memoizedProps,r[nt]=t,(o=r.nodeValue!==n)&&(e=Me,e!==null))switch(e.tag){case 3:mi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&mi(r.nodeValue,n,(e.mode&1)!==0)}o&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nt]=t,t.stateNode=r}return fe(t),null;case 13:if(z($),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(H&&Ae!==null&&t.mode&1&&!(t.flags&128))wf(),Hn(),t.flags|=98560,o=!1;else if(o=gi(t),r!==null&&r.dehydrated!==null){if(e===null){if(!o)throw Error(C(318));if(o=t.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(C(317));o[nt]=t}else Hn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;fe(t),o=!1}else Qe!==null&&(xl(Qe),Qe=null),o=!0;if(!o)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||$.current&1?ne===0&&(ne=3):ja())),t.updateQueue!==null&&(t.flags|=4),fe(t),null);case 4:return Xn(),dl(e,t),e===null&&Fr(t.stateNode.containerInfo),fe(t),null;case 10:return oa(t.type._context),fe(t),null;case 17:return Pe(t.type)&&Qi(),fe(t),null;case 19:if(z($),o=t.memoizedState,o===null)return fe(t),null;if(r=(t.flags&128)!==0,s=o.rendering,s===null)if(r)sr(o,!1);else{if(ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=no(e),s!==null){for(t.flags|=128,sr(o,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)o=n,e=r,o.flags&=14680066,s=o.alternate,s===null?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return I($,$.current&1|2),t.child}e=e.sibling}o.tail!==null&&Z()>Kn&&(t.flags|=128,r=!0,sr(o,!1),t.lanes=4194304)}else{if(!r)if(e=no(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),sr(o,!0),o.tail===null&&o.tailMode==="hidden"&&!s.alternate&&!H)return fe(t),null}else 2*Z()-o.renderingStartTime>Kn&&n!==1073741824&&(t.flags|=128,r=!0,sr(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(n=o.last,n!==null?n.sibling=s:t.child=s,o.last=s)}return o.tail!==null?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Z(),t.sibling=null,n=$.current,I($,r?n&1|2:n&1),t):(fe(t),null);case 22:case 23:return Sa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Re&1073741824&&(fe(t),t.subtreeFlags&6&&(t.flags|=8192)):fe(t),null;case 24:return null;case 25:return null}throw Error(C(156,t.tag))}function Kg(e,t){switch(ta(t),t.tag){case 1:return Pe(t.type)&&Qi(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Xn(),z(ke),z(me),ca(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ua(t),null;case 13:if(z($),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(C(340));Hn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return z($),null;case 4:return Xn(),null;case 10:return oa(t.type._context),null;case 22:case 23:return Sa(),null;case 24:return null;default:return null}}var xi=!1,he=!1,Qg=typeof WeakSet=="function"?WeakSet:Set,L=null;function An(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Q(e,t,r)}else n.current=null}function fl(e,t,n){try{n()}catch(r){Q(e,t,r)}}var Gu=!1;function Yg(e,t){if(Qs=$i,e=lf(),ql(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,c=0,f=0,d=e,p=null;t:for(;;){for(var m;d!==n||i!==0&&d.nodeType!==3||(l=s+i),d!==o||r!==0&&d.nodeType!==3||(a=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(m=d.firstChild)!==null;)p=d,d=m;for(;;){if(d===e)break t;if(p===n&&++c===i&&(l=s),p===o&&++f===r&&(a=s),(m=d.nextSibling)!==null)break;d=p,p=d.parentNode}d=m}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ys={focusedElem:e,selectionRange:n},$i=!1,L=t;L!==null;)if(t=L,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,L=e;else for(;L!==null;){t=L;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,S=v.memoizedState,y=t.stateNode,h=y.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ge(t.type,x),S);y.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(C(163))}}catch(w){Q(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,L=e;break}L=t.return}return v=Gu,Gu=!1,v}function Sr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,o!==void 0&&fl(t,n,o)}i=i.next}while(i!==r)}}function Lo(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function rp(e){var t=e.alternate;t!==null&&(e.alternate=null,rp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nt],delete t[_r],delete t[qs],delete t[Dg],delete t[Vg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ip(e){return e.tag===5||e.tag===3||e.tag===4}function Ku(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ip(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Ki));else if(r!==4&&(e=e.child,e!==null))for(hl(e,t,n),e=e.sibling;e!==null;)hl(e,t,n),e=e.sibling}function ml(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ml(e,t,n),e=e.sibling;e!==null;)ml(e,t,n),e=e.sibling}var ae=null,Ke=!1;function St(e,t,n){for(n=n.child;n!==null;)op(e,t,n),n=n.sibling}function op(e,t,n){if(rt&&typeof rt.onCommitFiberUnmount=="function")try{rt.onCommitFiberUnmount(wo,n)}catch{}switch(n.tag){case 5:he||An(n,t);case 6:var r=ae,i=Ke;ae=null,St(e,t,n),ae=r,Ke=i,ae!==null&&(Ke?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(Ke?(e=ae,n=n.stateNode,e.nodeType===8?os(e.parentNode,n):e.nodeType===1&&os(e,n),Vr(e)):os(ae,n.stateNode));break;case 4:r=ae,i=Ke,ae=n.stateNode.containerInfo,Ke=!0,St(e,t,n),ae=r,Ke=i;break;case 0:case 11:case 14:case 15:if(!he&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var o=i,s=o.destroy;o=o.tag,s!==void 0&&(o&2||o&4)&&fl(n,t,s),i=i.next}while(i!==r)}St(e,t,n);break;case 1:if(!he&&(An(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Q(n,t,l)}St(e,t,n);break;case 21:St(e,t,n);break;case 22:n.mode&1?(he=(r=he)||n.memoizedState!==null,St(e,t,n),he=r):St(e,t,n);break;default:St(e,t,n)}}function Qu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Qg),t.forEach(function(r){var i=oy.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function Xe(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var o=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ae=l.stateNode,Ke=!1;break e;case 3:ae=l.stateNode.containerInfo,Ke=!0;break e;case 4:ae=l.stateNode.containerInfo,Ke=!0;break e}l=l.return}if(ae===null)throw Error(C(160));op(o,s,i),ae=null,Ke=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(c){Q(i,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)sp(t,e),t=t.sibling}function sp(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Xe(t,e),et(e),r&4){try{Sr(3,e,e.return),Lo(3,e)}catch(x){Q(e,e.return,x)}try{Sr(5,e,e.return)}catch(x){Q(e,e.return,x)}}break;case 1:Xe(t,e),et(e),r&512&&n!==null&&An(n,n.return);break;case 5:if(Xe(t,e),et(e),r&512&&n!==null&&An(n,n.return),e.flags&32){var i=e.stateNode;try{Rr(i,"")}catch(x){Q(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var o=e.memoizedProps,s=n!==null?n.memoizedProps:o,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&o.type==="radio"&&o.name!=null&&Td(i,o),Is(l,s);var c=Is(l,o);for(s=0;s<a.length;s+=2){var f=a[s],d=a[s+1];f==="style"?Dd(i,d):f==="dangerouslySetInnerHTML"?Ad(i,d):f==="children"?Rr(i,d):bl(i,f,d,c)}switch(l){case"input":Os(i,o);break;case"textarea":Ld(i,o);break;case"select":var p=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!o.multiple;var m=o.value;m!=null?Fn(i,!!o.multiple,m,!1):p!==!!o.multiple&&(o.defaultValue!=null?Fn(i,!!o.multiple,o.defaultValue,!0):Fn(i,!!o.multiple,o.multiple?[]:"",!1))}i[_r]=o}catch(x){Q(e,e.return,x)}}break;case 6:if(Xe(t,e),et(e),r&4){if(e.stateNode===null)throw Error(C(162));i=e.stateNode,o=e.memoizedProps;try{i.nodeValue=o}catch(x){Q(e,e.return,x)}}break;case 3:if(Xe(t,e),et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Vr(t.containerInfo)}catch(x){Q(e,e.return,x)}break;case 4:Xe(t,e),et(e);break;case 13:Xe(t,e),et(e),i=e.child,i.flags&8192&&(o=i.memoizedState!==null,i.stateNode.isHidden=o,!o||i.alternate!==null&&i.alternate.memoizedState!==null||(xa=Z())),r&4&&Qu(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(he=(c=he)||f,Xe(t,e),he=c):Xe(t,e),et(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!f&&e.mode&1)for(L=e,f=e.child;f!==null;){for(d=L=f;L!==null;){switch(p=L,m=p.child,p.tag){case 0:case 11:case 14:case 15:Sr(4,p,p.return);break;case 1:An(p,p.return);var v=p.stateNode;if(typeof v.componentWillUnmount=="function"){r=p,n=p.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){Q(r,n,x)}}break;case 5:An(p,p.return);break;case 22:if(p.memoizedState!==null){Zu(d);continue}}m!==null?(m.return=p,L=m):Zu(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{i=d.stateNode,c?(o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none"):(l=d.stateNode,a=d.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Md("display",s))}catch(x){Q(e,e.return,x)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(x){Q(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Xe(t,e),et(e),r&4&&Qu(e);break;case 21:break;default:Xe(t,e),et(e)}}function et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(ip(n)){var r=n;break e}n=n.return}throw Error(C(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Rr(i,""),r.flags&=-33);var o=Ku(e);ml(e,o,i);break;case 3:case 4:var s=r.stateNode.containerInfo,l=Ku(e);hl(e,l,s);break;default:throw Error(C(161))}}catch(a){Q(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zg(e,t,n){L=e,lp(e)}function lp(e,t,n){for(var r=(e.mode&1)!==0;L!==null;){var i=L,o=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||xi;if(!s){var l=i.alternate,a=l!==null&&l.memoizedState!==null||he;l=xi;var c=he;if(xi=s,(he=a)&&!c)for(L=i;L!==null;)s=L,a=s.child,s.tag===22&&s.memoizedState!==null?Ju(i):a!==null?(a.return=s,L=a):Ju(i);for(;o!==null;)L=o,lp(o),o=o.sibling;L=i,xi=l,he=c}Yu(e)}else i.subtreeFlags&8772&&o!==null?(o.return=i,L=o):Yu(e)}}function Yu(e){for(;L!==null;){var t=L;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:he||Lo(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!he)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ge(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;o!==null&&Ou(t,o,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ou(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var f=c.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&Vr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(C(163))}he||t.flags&512&&pl(t)}catch(p){Q(t,t.return,p)}}if(t===e){L=null;break}if(n=t.sibling,n!==null){n.return=t.return,L=n;break}L=t.return}}function Zu(e){for(;L!==null;){var t=L;if(t===e){L=null;break}var n=t.sibling;if(n!==null){n.return=t.return,L=n;break}L=t.return}}function Ju(e){for(;L!==null;){var t=L;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Lo(4,t)}catch(a){Q(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Q(t,i,a)}}var o=t.return;try{pl(t)}catch(a){Q(t,o,a)}break;case 5:var s=t.return;try{pl(t)}catch(a){Q(t,s,a)}}}catch(a){Q(t,t.return,a)}if(t===e){L=null;break}var l=t.sibling;if(l!==null){l.return=t.return,L=l;break}L=t.return}}var Jg=Math.ceil,oo=xt.ReactCurrentDispatcher,ya=xt.ReactCurrentOwner,We=xt.ReactCurrentBatchConfig,B=0,se=null,ee=null,ue=0,Re=0,Mn=Ht(0),ne=0,Hr=null,fn=0,Ro=0,va=0,jr=null,je=null,xa=0,Kn=1/0,st=null,so=!1,gl=null,Nt=null,wi=!1,Rt=null,lo=0,Cr=0,yl=null,Ni=-1,Fi=0;function xe(){return B&6?Z():Ni!==-1?Ni:Ni=Z()}function Ft(e){return e.mode&1?B&2&&ue!==0?ue&-ue:Ng.transition!==null?(Fi===0&&(Fi=Hd()),Fi):(e=_,e!==0||(e=window.event,e=e===void 0?16:Zd(e.type)),e):1}function Ze(e,t,n,r){if(50<Cr)throw Cr=0,yl=null,Error(C(185));Yr(e,n,r),(!(B&2)||e!==se)&&(e===se&&(!(B&2)&&(Ro|=n),ne===4&&Tt(e,ue)),Ee(e,r),n===1&&B===0&&!(t.mode&1)&&(Kn=Z()+500,Po&&$t()))}function Ee(e,t){var n=e.callbackNode;Nm(e,t);var r=Hi(e,e===se?ue:0);if(r===0)n!==null&&lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&lu(n),t===1)e.tag===0?Og(qu.bind(null,e)):yf(qu.bind(null,e)),Ag(function(){!(B&6)&&$t()}),n=null;else{switch($d(r)){case 1:n=$l;break;case 4:n=Ud;break;case 16:n=Wi;break;case 536870912:n=Wd;break;default:n=Wi}n=mp(n,ap.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ap(e,t){if(Ni=-1,Fi=0,B&6)throw Error(C(327));var n=e.callbackNode;if(zn()&&e.callbackNode!==n)return null;var r=Hi(e,e===se?ue:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ao(e,r);else{t=r;var i=B;B|=2;var o=cp();(se!==e||ue!==t)&&(st=null,Kn=Z()+500,sn(e,t));do try{ty();break}catch(l){up(e,l)}while(!0);ia(),oo.current=o,B=i,ee!==null?t=0:(se=null,ue=0,t=ne)}if(t!==0){if(t===2&&(i=Hs(e),i!==0&&(r=i,t=vl(e,i))),t===1)throw n=Hr,sn(e,0),Tt(e,r),Ee(e,Z()),n;if(t===6)Tt(e,r);else{if(i=e.current.alternate,!(r&30)&&!qg(i)&&(t=ao(e,r),t===2&&(o=Hs(e),o!==0&&(r=o,t=vl(e,o))),t===1))throw n=Hr,sn(e,0),Tt(e,r),Ee(e,Z()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(C(345));case 2:Jt(e,je,st);break;case 3:if(Tt(e,r),(r&130023424)===r&&(t=xa+500-Z(),10<t)){if(Hi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){xe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Js(Jt.bind(null,e,je,st),t);break}Jt(e,je,st);break;case 4:if(Tt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Ye(r);o=1<<s,s=t[s],s>i&&(i=s),r&=~o}if(r=i,r=Z()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Jg(r/1960))-r,10<r){e.timeoutHandle=Js(Jt.bind(null,e,je,st),r);break}Jt(e,je,st);break;case 5:Jt(e,je,st);break;default:throw Error(C(329))}}}return Ee(e,Z()),e.callbackNode===n?ap.bind(null,e):null}function vl(e,t){var n=jr;return e.current.memoizedState.isDehydrated&&(sn(e,t).flags|=256),e=ao(e,t),e!==2&&(t=je,je=n,t!==null&&xl(t)),e}function xl(e){je===null?je=e:je.push.apply(je,e)}function qg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!Je(o(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Tt(e,t){for(t&=~va,t&=~Ro,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function qu(e){if(B&6)throw Error(C(327));zn();var t=Hi(e,0);if(!(t&1))return Ee(e,Z()),null;var n=ao(e,t);if(e.tag!==0&&n===2){var r=Hs(e);r!==0&&(t=r,n=vl(e,r))}if(n===1)throw n=Hr,sn(e,0),Tt(e,t),Ee(e,Z()),n;if(n===6)throw Error(C(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Jt(e,je,st),Ee(e,Z()),null}function wa(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(Kn=Z()+500,Po&&$t())}}function pn(e){Rt!==null&&Rt.tag===0&&!(B&6)&&zn();var t=B;B|=1;var n=We.transition,r=_;try{if(We.transition=null,_=1,e)return e()}finally{_=r,We.transition=n,B=t,!(B&6)&&$t()}}function Sa(){Re=Mn.current,z(Mn)}function sn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Rg(n)),ee!==null)for(n=ee.return;n!==null;){var r=n;switch(ta(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Qi();break;case 3:Xn(),z(ke),z(me),ca();break;case 5:ua(r);break;case 4:Xn();break;case 13:z($);break;case 19:z($);break;case 10:oa(r.type._context);break;case 22:case 23:Sa()}n=n.return}if(se=e,ee=e=Bt(e.current,null),ue=Re=t,ne=0,Hr=null,va=Ro=fn=0,je=jr=null,nn!==null){for(t=0;t<nn.length;t++)if(n=nn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,o=n.pending;if(o!==null){var s=o.next;o.next=i,r.next=s}n.pending=r}nn=null}return e}function up(e,t){do{var n=ee;try{if(ia(),Di.current=io,ro){for(var r=G.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ro=!1}if(dn=0,oe=te=G=null,wr=!1,zr=0,ya.current=null,n===null||n.return===null){ne=1,Hr=t,ee=null;break}e:{var o=e,s=n.return,l=n,a=t;if(t=ue,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var c=a,f=l,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var p=f.alternate;p?(f.updateQueue=p.updateQueue,f.memoizedState=p.memoizedState,f.lanes=p.lanes):(f.updateQueue=null,f.memoizedState=null)}var m=bu(s);if(m!==null){m.flags&=-257,zu(m,s,l,o,t),m.mode&1&&Iu(o,c,t),t=m,a=c;var v=t.updateQueue;if(v===null){var x=new Set;x.add(a),t.updateQueue=x}else v.add(a);break e}else{if(!(t&1)){Iu(o,c,t),ja();break e}a=Error(C(426))}}else if(H&&l.mode&1){var S=bu(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),zu(S,s,l,o,t),na(Gn(a,l));break e}}o=a=Gn(a,l),ne!==4&&(ne=2),jr===null?jr=[o]:jr.push(o),o=s;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t;var y=Xf(o,a,t);Vu(o,y);break e;case 1:l=a;var h=o.type,g=o.stateNode;if(!(o.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Nt===null||!Nt.has(g)))){o.flags|=65536,t&=-t,o.lanes|=t;var w=Gf(o,l,t);Vu(o,w);break e}}o=o.return}while(o!==null)}fp(n)}catch(j){t=j,ee===n&&n!==null&&(ee=n=n.return);continue}break}while(!0)}function cp(){var e=oo.current;return oo.current=io,e===null?io:e}function ja(){(ne===0||ne===3||ne===2)&&(ne=4),se===null||!(fn&268435455)&&!(Ro&268435455)||Tt(se,ue)}function ao(e,t){var n=B;B|=2;var r=cp();(se!==e||ue!==t)&&(st=null,sn(e,t));do try{ey();break}catch(i){up(e,i)}while(!0);if(ia(),B=n,oo.current=r,ee!==null)throw Error(C(261));return se=null,ue=0,ne}function ey(){for(;ee!==null;)dp(ee)}function ty(){for(;ee!==null&&!Em();)dp(ee)}function dp(e){var t=hp(e.alternate,e,Re);e.memoizedProps=e.pendingProps,t===null?fp(e):ee=t,ya.current=null}function fp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Kg(n,t),n!==null){n.flags&=32767,ee=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ne=6,ee=null;return}}else if(n=Gg(n,t,Re),n!==null){ee=n;return}if(t=t.sibling,t!==null){ee=t;return}ee=t=e}while(t!==null);ne===0&&(ne=5)}function Jt(e,t,n){var r=_,i=We.transition;try{We.transition=null,_=1,ny(e,t,n,r)}finally{We.transition=i,_=r}return null}function ny(e,t,n,r){do zn();while(Rt!==null);if(B&6)throw Error(C(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(C(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(Fm(e,o),e===se&&(ee=se=null,ue=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||wi||(wi=!0,mp(Wi,function(){return zn(),null})),o=(n.flags&15990)!==0,n.subtreeFlags&15990||o){o=We.transition,We.transition=null;var s=_;_=1;var l=B;B|=4,ya.current=null,Yg(e,n),sp(n,e),jg(Ys),$i=!!Qs,Ys=Qs=null,e.current=n,Zg(n),Tm(),B=l,_=s,We.transition=o}else e.current=n;if(wi&&(wi=!1,Rt=e,lo=i),o=e.pendingLanes,o===0&&(Nt=null),Am(n.stateNode),Ee(e,Z()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(so)throw so=!1,e=gl,gl=null,e;return lo&1&&e.tag!==0&&zn(),o=e.pendingLanes,o&1?e===yl?Cr++:(Cr=0,yl=e):Cr=0,$t(),null}function zn(){if(Rt!==null){var e=$d(lo),t=We.transition,n=_;try{if(We.transition=null,_=16>e?16:e,Rt===null)var r=!1;else{if(e=Rt,Rt=null,lo=0,B&6)throw Error(C(331));var i=B;for(B|=4,L=e.current;L!==null;){var o=L,s=o.child;if(L.flags&16){var l=o.deletions;if(l!==null){for(var a=0;a<l.length;a++){var c=l[a];for(L=c;L!==null;){var f=L;switch(f.tag){case 0:case 11:case 15:Sr(8,f,o)}var d=f.child;if(d!==null)d.return=f,L=d;else for(;L!==null;){f=L;var p=f.sibling,m=f.return;if(rp(f),f===c){L=null;break}if(p!==null){p.return=m,L=p;break}L=m}}}var v=o.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}L=o}}if(o.subtreeFlags&2064&&s!==null)s.return=o,L=s;else e:for(;L!==null;){if(o=L,o.flags&2048)switch(o.tag){case 0:case 11:case 15:Sr(9,o,o.return)}var y=o.sibling;if(y!==null){y.return=o.return,L=y;break e}L=o.return}}var h=e.current;for(L=h;L!==null;){s=L;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,L=g;else e:for(s=h;L!==null;){if(l=L,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Lo(9,l)}}catch(j){Q(l,l.return,j)}if(l===s){L=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,L=w;break e}L=l.return}}if(B=i,$t(),rt&&typeof rt.onPostCommitFiberRoot=="function")try{rt.onPostCommitFiberRoot(wo,e)}catch{}r=!0}return r}finally{_=n,We.transition=t}}return!1}function ec(e,t,n){t=Gn(n,t),t=Xf(e,t,1),e=Ot(e,t,1),t=xe(),e!==null&&(Yr(e,1,t),Ee(e,t))}function Q(e,t,n){if(e.tag===3)ec(e,e,n);else for(;t!==null;){if(t.tag===3){ec(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Nt===null||!Nt.has(r))){e=Gn(n,e),e=Gf(t,e,1),t=Ot(t,e,1),e=xe(),t!==null&&(Yr(t,1,e),Ee(t,e));break}}t=t.return}}function ry(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=xe(),e.pingedLanes|=e.suspendedLanes&n,se===e&&(ue&n)===n&&(ne===4||ne===3&&(ue&130023424)===ue&&500>Z()-xa?sn(e,0):va|=n),Ee(e,t)}function pp(e,t){t===0&&(e.mode&1?(t=ci,ci<<=1,!(ci&130023424)&&(ci=4194304)):t=1);var n=xe();e=gt(e,t),e!==null&&(Yr(e,t,n),Ee(e,n))}function iy(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),pp(e,n)}function oy(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(C(314))}r!==null&&r.delete(t),pp(e,n)}var hp;hp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ke.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,Xg(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,H&&t.flags&1048576&&vf(t,Ji,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Oi(e,t),e=t.pendingProps;var i=Wn(t,me.current);bn(t,n),i=fa(null,t,r,e,i,n);var o=pa();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(o=!0,Yi(t)):o=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,la(t),i.updater=To,t.stateNode=i,i._reactInternals=t,ol(t,r,e,n),t=al(null,t,r,!0,o,n)):(t.tag=0,H&&o&&ea(t),ve(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Oi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=ly(r),e=Ge(r,e),i){case 0:t=ll(null,t,r,e,n);break e;case 1:t=Hu(null,t,r,e,n);break e;case 11:t=Uu(null,t,r,e,n);break e;case 14:t=Wu(null,t,r,Ge(r.type,e),n);break e}throw Error(C(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),ll(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Hu(e,t,r,i,n);case 3:e:{if(Zf(t),e===null)throw Error(C(387));r=t.pendingProps,o=t.memoizedState,i=o.element,kf(e,t),to(t,r,null,n);var s=t.memoizedState;if(r=s.element,o.isDehydrated)if(o={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=o,t.memoizedState=o,t.flags&256){i=Gn(Error(C(423)),t),t=$u(e,t,r,n,i);break e}else if(r!==i){i=Gn(Error(C(424)),t),t=$u(e,t,r,n,i);break e}else for(Ae=Vt(t.stateNode.containerInfo.firstChild),Me=t,H=!0,Qe=null,n=jf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hn(),r===i){t=yt(e,t,n);break e}ve(e,t,r,n)}t=t.child}return t;case 5:return Pf(t),e===null&&nl(t),r=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,s=i.children,Zs(r,i)?s=null:o!==null&&Zs(r,o)&&(t.flags|=32),Yf(e,t),ve(e,t,s,n),t.child;case 6:return e===null&&nl(t),null;case 13:return Jf(e,t,n);case 4:return aa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=$n(t,null,r,n):ve(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Uu(e,t,r,i,n);case 7:return ve(e,t,t.pendingProps,n),t.child;case 8:return ve(e,t,t.pendingProps.children,n),t.child;case 12:return ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,o=t.memoizedProps,s=i.value,I(qi,r._currentValue),r._currentValue=s,o!==null)if(Je(o.value,s)){if(o.children===i.children&&!ke.current){t=yt(e,t,n);break e}}else for(o=t.child,o!==null&&(o.return=t);o!==null;){var l=o.dependencies;if(l!==null){s=o.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(o.tag===1){a=dt(-1,n&-n),a.tag=2;var c=o.updateQueue;if(c!==null){c=c.shared;var f=c.pending;f===null?a.next=a:(a.next=f.next,f.next=a),c.pending=a}}o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),rl(o.return,n,t),l.lanes|=n;break}a=a.next}}else if(o.tag===10)s=o.type===t.type?null:o.child;else if(o.tag===18){if(s=o.return,s===null)throw Error(C(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),rl(s,n,t),s=o.sibling}else s=o.child;if(s!==null)s.return=o;else for(s=o;s!==null;){if(s===t){s=null;break}if(o=s.sibling,o!==null){o.return=s.return,s=o;break}s=s.return}o=s}ve(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,bn(t,n),i=He(i),r=r(i),t.flags|=1,ve(e,t,r,n),t.child;case 14:return r=t.type,i=Ge(r,t.pendingProps),i=Ge(r.type,i),Wu(e,t,r,i,n);case 15:return Kf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ge(r,i),Oi(e,t),t.tag=1,Pe(r)?(e=!0,Yi(t)):e=!1,bn(t,n),$f(t,r,i),ol(t,r,i,n),al(null,t,r,!0,e,n);case 19:return qf(e,t,n);case 22:return Qf(e,t,n)}throw Error(C(156,t.tag))};function mp(e,t){return zd(e,t)}function sy(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new sy(e,t,n,r)}function Ca(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ly(e){if(typeof e=="function")return Ca(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ul)return 11;if(e===Wl)return 14}return 2}function Bt(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Bi(e,t,n,r,i,o){var s=2;if(r=e,typeof e=="function")Ca(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Sn:return ln(n.children,i,o,t);case zl:s=8,i|=8;break;case Rs:return e=ze(12,n,t,i|2),e.elementType=Rs,e.lanes=o,e;case As:return e=ze(13,n,t,i),e.elementType=As,e.lanes=o,e;case Ms:return e=ze(19,n,t,i),e.elementType=Ms,e.lanes=o,e;case kd:return Ao(n,i,o,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case jd:s=10;break e;case Cd:s=9;break e;case Ul:s=11;break e;case Wl:s=14;break e;case Ct:s=16,r=null;break e}throw Error(C(130,e==null?e:typeof e,""))}return t=ze(s,n,t,i),t.elementType=e,t.type=r,t.lanes=o,t}function ln(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function Ao(e,t,n,r){return e=ze(22,e,r,t),e.elementType=kd,e.lanes=n,e.stateNode={isHidden:!1},e}function ps(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function hs(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ay(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ko(0),this.expirationTimes=Ko(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ko(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function ka(e,t,n,r,i,o,s,l,a){return e=new ay(e,t,n,l,a),t===1?(t=1,o===!0&&(t|=8)):t=0,o=ze(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},la(o),e}function uy(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:wn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function gp(e){if(!e)return bt;e=e._reactInternals;e:{if(mn(e)!==e||e.tag!==1)throw Error(C(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(C(171))}if(e.tag===1){var n=e.type;if(Pe(n))return gf(e,n,t)}return t}function yp(e,t,n,r,i,o,s,l,a){return e=ka(n,r,!0,e,i,o,s,l,a),e.context=gp(null),n=e.current,r=xe(),i=Ft(n),o=dt(r,i),o.callback=t??null,Ot(n,o,i),e.current.lanes=i,Yr(e,i,r),Ee(e,r),e}function Mo(e,t,n,r){var i=t.current,o=xe(),s=Ft(i);return n=gp(n),t.context===null?t.context=n:t.pendingContext=n,t=dt(o,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Ot(i,t,s),e!==null&&(Ze(e,i,s,o),Mi(e,i,s)),s}function uo(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function tc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Pa(e,t){tc(e,t),(e=e.alternate)&&tc(e,t)}function cy(){return null}var vp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ea(e){this._internalRoot=e}Do.prototype.render=Ea.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(C(409));Mo(e,t,null,null)};Do.prototype.unmount=Ea.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;pn(function(){Mo(null,e,null,null)}),t[mt]=null}};function Do(e){this._internalRoot=e}Do.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Et.length&&t!==0&&t<Et[n].priority;n++);Et.splice(n,0,e),n===0&&Yd(e)}};function Ta(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Vo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function nc(){}function dy(e,t,n,r,i){if(i){if(typeof r=="function"){var o=r;r=function(){var c=uo(s);o.call(c)}}var s=yp(t,r,e,0,null,!1,!1,"",nc);return e._reactRootContainer=s,e[mt]=s.current,Fr(e.nodeType===8?e.parentNode:e),pn(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var c=uo(a);l.call(c)}}var a=ka(e,0,!1,null,null,!1,!1,"",nc);return e._reactRootContainer=a,e[mt]=a.current,Fr(e.nodeType===8?e.parentNode:e),pn(function(){Mo(t,a,n,r)}),a}function Oo(e,t,n,r,i){var o=n._reactRootContainer;if(o){var s=o;if(typeof i=="function"){var l=i;i=function(){var a=uo(s);l.call(a)}}Mo(t,s,e,i)}else s=dy(n,t,e,i,r);return uo(s)}Xd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fr(t.pendingLanes);n!==0&&(Xl(t,n|1),Ee(t,Z()),!(B&6)&&(Kn=Z()+500,$t()))}break;case 13:pn(function(){var r=gt(e,1);if(r!==null){var i=xe();Ze(r,e,1,i)}}),Pa(e,1)}};Gl=function(e){if(e.tag===13){var t=gt(e,134217728);if(t!==null){var n=xe();Ze(t,e,134217728,n)}Pa(e,134217728)}};Gd=function(e){if(e.tag===13){var t=Ft(e),n=gt(e,t);if(n!==null){var r=xe();Ze(n,e,t,r)}Pa(e,t)}};Kd=function(){return _};Qd=function(e,t){var n=_;try{return _=e,t()}finally{_=n}};zs=function(e,t,n){switch(t){case"input":if(Os(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=ko(r);if(!i)throw Error(C(90));Ed(r),Os(r,i)}}}break;case"textarea":Ld(e,n);break;case"select":t=n.value,t!=null&&Fn(e,!!n.multiple,t,!1)}};Nd=wa;Fd=pn;var fy={usingClientEntryPoint:!1,Events:[Jr,Pn,ko,Vd,Od,wa]},lr={findFiberByHostInstance:tn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},py={bundleType:lr.bundleType,version:lr.version,rendererPackageName:lr.rendererPackageName,rendererConfig:lr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Id(e),e===null?null:e.stateNode},findFiberByHostInstance:lr.findFiberByHostInstance||cy,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Si=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Si.isDisabled&&Si.supportsFiber)try{wo=Si.inject(py),rt=Si}catch{}}Oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fy;Oe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ta(t))throw Error(C(200));return uy(e,t,null,n)};Oe.createRoot=function(e,t){if(!Ta(e))throw Error(C(299));var n=!1,r="",i=vp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=ka(e,1,!1,null,null,n,!1,r,i),e[mt]=t.current,Fr(e.nodeType===8?e.parentNode:e),new Ea(t)};Oe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(C(188)):(e=Object.keys(e).join(","),Error(C(268,e)));return e=Id(t),e=e===null?null:e.stateNode,e};Oe.flushSync=function(e){return pn(e)};Oe.hydrate=function(e,t,n){if(!Vo(t))throw Error(C(200));return Oo(null,e,t,!0,n)};Oe.hydrateRoot=function(e,t,n){if(!Ta(e))throw Error(C(405));var r=n!=null&&n.hydratedSources||null,i=!1,o="",s=vp;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=yp(t,null,e,1,n??null,i,!1,o,s),e[mt]=t.current,Fr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Do(t)};Oe.render=function(e,t,n){if(!Vo(t))throw Error(C(200));return Oo(null,e,t,!1,n)};Oe.unmountComponentAtNode=function(e){if(!Vo(e))throw Error(C(40));return e._reactRootContainer?(pn(function(){Oo(null,null,e,!1,function(){e._reactRootContainer=null,e[mt]=null})}),!0):!1};Oe.unstable_batchedUpdates=wa;Oe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Vo(n))throw Error(C(200));if(e==null||e._reactInternals===void 0)throw Error(C(38));return Oo(e,t,n,!1,r)};Oe.version="18.3.1-next-f1338f8080-20240426";function xp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(xp)}catch(e){console.error(e)}}xp(),vd.exports=Oe;var hy=vd.exports,rc=hy;Ts.createRoot=rc.createRoot,Ts.hydrateRoot=rc.hydrateRoot;const wp=R.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),No=R.createContext({}),La=R.createContext(null),Fo=typeof document<"u",my=Fo?R.useLayoutEffect:R.useEffect,Sp=R.createContext({strict:!1}),Ra=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),gy="framerAppearId",jp="data-"+Ra(gy);function yy(e,t,n,r){const{visualElement:i}=R.useContext(No),o=R.useContext(Sp),s=R.useContext(La),l=R.useContext(wp).reducedMotion,a=R.useRef();r=r||o.renderer,!a.current&&r&&(a.current=r(e,{visualState:t,parent:i,props:n,presenceContext:s,blockInitialAnimation:s?s.initial===!1:!1,reducedMotionConfig:l}));const c=a.current;R.useInsertionEffect(()=>{c&&c.update(n,s)});const f=R.useRef(!!(n[jp]&&!window.HandoffComplete));return my(()=>{c&&(c.render(),f.current&&c.animationState&&c.animationState.animateChanges())}),R.useEffect(()=>{c&&(c.updateFeatures(),!f.current&&c.animationState&&c.animationState.animateChanges(),f.current&&(f.current=!1,window.HandoffComplete=!0))}),c}function Dn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function vy(e,t,n){return R.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Dn(n)&&(n.current=r))},[t])}function $r(e){return typeof e=="string"||Array.isArray(e)}function Bo(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Aa=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Ma=["initial",...Aa];function _o(e){return Bo(e.animate)||Ma.some(t=>$r(e[t]))}function Cp(e){return!!(_o(e)||e.variants)}function xy(e,t){if(_o(e)){const{initial:n,animate:r}=e;return{initial:n===!1||$r(n)?n:void 0,animate:$r(r)?r:void 0}}return e.inherit!==!1?t:{}}function wy(e){const{initial:t,animate:n}=xy(e,R.useContext(No));return R.useMemo(()=>({initial:t,animate:n}),[ic(t),ic(n)])}function ic(e){return Array.isArray(e)?e.join(" "):e}const oc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Xr={};for(const e in oc)Xr[e]={isEnabled:t=>oc[e].some(n=>!!t[n])};function Sy(e){for(const t in e)Xr[t]={...Xr[t],...e[t]}}const kp=R.createContext({}),Pp=R.createContext({}),jy=Symbol.for("motionComponentSymbol");function Cy({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&Sy(e);function o(l,a){let c;const f={...R.useContext(wp),...l,layoutId:ky(l)},{isStatic:d}=f,p=wy(l),m=r(l,d);if(!d&&Fo){p.visualElement=yy(i,m,f,t);const v=R.useContext(Pp),x=R.useContext(Sp).strict;p.visualElement&&(c=p.visualElement.loadFeatures(f,x,e,v))}return R.createElement(No.Provider,{value:p},c&&p.visualElement?R.createElement(c,{visualElement:p.visualElement,...f}):null,n(i,l,vy(m,p.visualElement,a),m,d,p.visualElement))}const s=R.forwardRef(o);return s[jy]=i,s}function ky({layoutId:e}){const t=R.useContext(kp).id;return t&&e!==void 0?t+"-"+e:e}function Py(e){function t(r,i={}){return Cy(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const Ey=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Da(e){return typeof e!="string"||e.includes("-")?!1:!!(Ey.indexOf(e)>-1||/[A-Z]/.test(e))}const co={};function Ty(e){Object.assign(co,e)}const ei=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gn=new Set(ei);function Ep(e,{layout:t,layoutId:n}){return gn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!co[e]||e==="opacity")}const Te=e=>!!(e&&e.getVelocity),Ly={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},Ry=ei.length;function Ay(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let o="";for(let s=0;s<Ry;s++){const l=ei[s];if(e[l]!==void 0){const a=Ly[l]||l;o+=`${a}(${e[l]}) `}}return t&&!e.z&&(o+="translateZ(0)"),o=o.trim(),i?o=i(e,r?"":o):n&&r&&(o="none"),o}const Tp=e=>t=>typeof t=="string"&&t.startsWith(e),Lp=Tp("--"),wl=Tp("var(--"),My=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,Dy=(e,t)=>t&&typeof e=="number"?t.transform(e):e,zt=(e,t,n)=>Math.min(Math.max(n,e),t),yn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},kr={...yn,transform:e=>zt(0,1,e)},ji={...yn,default:1},Pr=e=>Math.round(e*1e5)/1e5,Io=/(-)?([\d]*\.?[\d])+/g,Rp=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,Vy=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function ti(e){return typeof e=="string"}const ni=e=>({test:t=>ti(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),jt=ni("deg"),ot=ni("%"),A=ni("px"),Oy=ni("vh"),Ny=ni("vw"),sc={...ot,parse:e=>ot.parse(e)/100,transform:e=>ot.transform(e*100)},lc={...yn,transform:Math.round},Ap={borderWidth:A,borderTopWidth:A,borderRightWidth:A,borderBottomWidth:A,borderLeftWidth:A,borderRadius:A,radius:A,borderTopLeftRadius:A,borderTopRightRadius:A,borderBottomRightRadius:A,borderBottomLeftRadius:A,width:A,maxWidth:A,height:A,maxHeight:A,size:A,top:A,right:A,bottom:A,left:A,padding:A,paddingTop:A,paddingRight:A,paddingBottom:A,paddingLeft:A,margin:A,marginTop:A,marginRight:A,marginBottom:A,marginLeft:A,rotate:jt,rotateX:jt,rotateY:jt,rotateZ:jt,scale:ji,scaleX:ji,scaleY:ji,scaleZ:ji,skew:jt,skewX:jt,skewY:jt,distance:A,translateX:A,translateY:A,translateZ:A,x:A,y:A,z:A,perspective:A,transformPerspective:A,opacity:kr,originX:sc,originY:sc,originZ:A,zIndex:lc,fillOpacity:kr,strokeOpacity:kr,numOctaves:lc};function Va(e,t,n,r){const{style:i,vars:o,transform:s,transformOrigin:l}=e;let a=!1,c=!1,f=!0;for(const d in t){const p=t[d];if(Lp(d)){o[d]=p;continue}const m=Ap[d],v=Dy(p,m);if(gn.has(d)){if(a=!0,s[d]=v,!f)continue;p!==(m.default||0)&&(f=!1)}else d.startsWith("origin")?(c=!0,l[d]=v):i[d]=v}if(t.transform||(a||r?i.transform=Ay(e.transform,n,f,r):i.transform&&(i.transform="none")),c){const{originX:d="50%",originY:p="50%",originZ:m=0}=l;i.transformOrigin=`${d} ${p} ${m}`}}const Oa=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Mp(e,t,n){for(const r in t)!Te(t[r])&&!Ep(r,n)&&(e[r]=t[r])}function Fy({transformTemplate:e},t,n){return R.useMemo(()=>{const r=Oa();return Va(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function By(e,t,n){const r=e.style||{},i={};return Mp(i,r,e),Object.assign(i,Fy(e,t,n)),e.transformValues?e.transformValues(i):i}function _y(e,t,n){const r={},i=By(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const Iy=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function fo(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||Iy.has(e)}let Dp=e=>!fo(e);function by(e){e&&(Dp=t=>t.startsWith("on")?!fo(t):e(t))}try{by(require("@emotion/is-prop-valid").default)}catch{}function zy(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(Dp(i)||n===!0&&fo(i)||!t&&!fo(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function ac(e,t,n){return typeof e=="string"?e:A.transform(t+n*e)}function Uy(e,t,n){const r=ac(t,e.x,e.width),i=ac(n,e.y,e.height);return`${r} ${i}`}const Wy={offset:"stroke-dashoffset",array:"stroke-dasharray"},Hy={offset:"strokeDashoffset",array:"strokeDasharray"};function $y(e,t,n=1,r=0,i=!0){e.pathLength=1;const o=i?Wy:Hy;e[o.offset]=A.transform(-r);const s=A.transform(t),l=A.transform(n);e[o.array]=`${s} ${l}`}function Na(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:o,pathLength:s,pathSpacing:l=1,pathOffset:a=0,...c},f,d,p){if(Va(e,c,f,p),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:m,style:v,dimensions:x}=e;m.transform&&(x&&(v.transform=m.transform),delete m.transform),x&&(i!==void 0||o!==void 0||v.transform)&&(v.transformOrigin=Uy(x,i!==void 0?i:.5,o!==void 0?o:.5)),t!==void 0&&(m.x=t),n!==void 0&&(m.y=n),r!==void 0&&(m.scale=r),s!==void 0&&$y(m,s,l,a,!1)}const Vp=()=>({...Oa(),attrs:{}}),Fa=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Xy(e,t,n,r){const i=R.useMemo(()=>{const o=Vp();return Na(o,t,{enableHardwareAcceleration:!1},Fa(r),e.transformTemplate),{...o.attrs,style:{...o.style}}},[t]);if(e.style){const o={};Mp(o,e.style,e),i.style={...o,...i.style}}return i}function Gy(e=!1){return(n,r,i,{latestValues:o},s)=>{const a=(Da(n)?Xy:_y)(r,o,s,n),f={...zy(r,typeof n=="string",e),...a,ref:i},{children:d}=r,p=R.useMemo(()=>Te(d)?d.get():d,[d]);return R.createElement(n,{...f,children:p})}}function Op(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const o in n)e.style.setProperty(o,n[o])}const Np=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Fp(e,t,n,r){Op(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Np.has(i)?i:Ra(i),t.attrs[i])}function Ba(e,t){const{style:n}=e,r={};for(const i in n)(Te(n[i])||t.style&&Te(t.style[i])||Ep(i,e))&&(r[i]=n[i]);return r}function Bp(e,t){const n=Ba(e,t);for(const r in e)if(Te(e[r])||Te(t[r])){const i=ei.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function _a(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function Ky(e){const t=R.useRef(null);return t.current===null&&(t.current=e()),t.current}const po=e=>Array.isArray(e),Qy=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),Yy=e=>po(e)?e[e.length-1]||0:e;function _i(e){const t=Te(e)?e.get():e;return Qy(t)?t.toValue():t}function Zy({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,o){const s={latestValues:Jy(r,i,o,e),renderState:t()};return n&&(s.mount=l=>n(r,l,s)),s}const _p=e=>(t,n)=>{const r=R.useContext(No),i=R.useContext(La),o=()=>Zy(e,t,r,i);return n?o():Ky(o)};function Jy(e,t,n,r){const i={},o=r(e,{});for(const p in o)i[p]=_i(o[p]);let{initial:s,animate:l}=e;const a=_o(e),c=Cp(e);t&&c&&!a&&e.inherit!==!1&&(s===void 0&&(s=t.initial),l===void 0&&(l=t.animate));let f=n?n.initial===!1:!1;f=f||s===!1;const d=f?l:s;return d&&typeof d!="boolean"&&!Bo(d)&&(Array.isArray(d)?d:[d]).forEach(m=>{const v=_a(e,m);if(!v)return;const{transitionEnd:x,transition:S,...y}=v;for(const h in y){let g=y[h];if(Array.isArray(g)){const w=f?g.length-1:0;g=g[w]}g!==null&&(i[h]=g)}for(const h in x)i[h]=x[h]}),i}const J=e=>e;class uc{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function qy(e){let t=new uc,n=new uc,r=0,i=!1,o=!1;const s=new WeakSet,l={schedule:(a,c=!1,f=!1)=>{const d=f&&i,p=d?t:n;return c&&s.add(a),p.add(a)&&d&&i&&(r=t.order.length),a},cancel:a=>{n.remove(a),s.delete(a)},process:a=>{if(i){o=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let c=0;c<r;c++){const f=t.order[c];f(a),s.has(f)&&(l.schedule(f),e())}i=!1,o&&(o=!1,l.process(a))}};return l}const Ci=["prepare","read","update","preRender","render","postRender"],e0=40;function t0(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=Ci.reduce((d,p)=>(d[p]=qy(()=>n=!0),d),{}),s=d=>o[d].process(i),l=()=>{const d=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(d-i.timestamp,e0),1),i.timestamp=d,i.isProcessing=!0,Ci.forEach(s),i.isProcessing=!1,n&&t&&(r=!1,e(l))},a=()=>{n=!0,r=!0,i.isProcessing||e(l)};return{schedule:Ci.reduce((d,p)=>{const m=o[p];return d[p]=(v,x=!1,S=!1)=>(n||a(),m.schedule(v,x,S)),d},{}),cancel:d=>Ci.forEach(p=>o[p].cancel(d)),state:i,steps:o}}const{schedule:U,cancel:vt,state:pe,steps:ms}=t0(typeof requestAnimationFrame<"u"?requestAnimationFrame:J,!0),n0={useVisualState:_p({scrapeMotionValuesFromProps:Bp,createRenderState:Vp,onMount:(e,t,{renderState:n,latestValues:r})=>{U.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),U.render(()=>{Na(n,r,{enableHardwareAcceleration:!1},Fa(t.tagName),e.transformTemplate),Fp(t,n)})}})},r0={useVisualState:_p({scrapeMotionValuesFromProps:Ba,createRenderState:Oa})};function i0(e,{forwardMotionProps:t=!1},n,r){return{...Da(e)?n0:r0,preloadedFeatures:n,useRender:Gy(t),createVisualElement:r,Component:e}}function ct(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Ip=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function bo(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const o0=e=>t=>Ip(t)&&e(t,bo(t));function ft(e,t,n,r){return ct(e,t,o0(n),r)}const s0=(e,t)=>n=>t(e(n)),_t=(...e)=>e.reduce(s0);function bp(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const cc=bp("dragHorizontal"),dc=bp("dragVertical");function zp(e){let t=!1;if(e==="y")t=dc();else if(e==="x")t=cc();else{const n=cc(),r=dc();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function Up(){const e=zp(!0);return e?(e(),!1):!0}class Xt{constructor(t){this.isMounted=!1,this.node=t}update(){}}function fc(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(o,s)=>{if(o.pointerType==="touch"||Up())return;const l=e.getProps();e.animationState&&l.whileHover&&e.animationState.setActive("whileHover",t),l[r]&&U.update(()=>l[r](o,s))};return ft(e.current,n,i,{passive:!e.getProps()[r]})}class l0 extends Xt{mount(){this.unmount=_t(fc(this.node,!0),fc(this.node,!1))}unmount(){}}class a0 extends Xt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=_t(ct(this.node.current,"focus",()=>this.onFocus()),ct(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const Wp=(e,t)=>t?e===t?!0:Wp(e,t.parentElement):!1;function gs(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,bo(n))}class u0 extends Xt{constructor(){super(...arguments),this.removeStartListeners=J,this.removeEndListeners=J,this.removeAccessibleListeners=J,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),o=ft(window,"pointerup",(l,a)=>{if(!this.checkPressEnd())return;const{onTap:c,onTapCancel:f,globalTapTarget:d}=this.node.getProps();U.update(()=>{!d&&!Wp(this.node.current,l.target)?f&&f(l,a):c&&c(l,a)})},{passive:!(r.onTap||r.onPointerUp)}),s=ft(window,"pointercancel",(l,a)=>this.cancelPress(l,a),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=_t(o,s),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=o=>{if(o.key!=="Enter"||this.isPressing)return;const s=l=>{l.key!=="Enter"||!this.checkPressEnd()||gs("up",(a,c)=>{const{onTap:f}=this.node.getProps();f&&U.update(()=>f(a,c))})};this.removeEndListeners(),this.removeEndListeners=ct(this.node.current,"keyup",s),gs("down",(l,a)=>{this.startPress(l,a)})},n=ct(this.node.current,"keydown",t),r=()=>{this.isPressing&&gs("cancel",(o,s)=>this.cancelPress(o,s))},i=ct(this.node.current,"blur",r);this.removeAccessibleListeners=_t(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&U.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Up()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&U.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=ft(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=ct(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=_t(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const Sl=new WeakMap,ys=new WeakMap,c0=e=>{const t=Sl.get(e.target);t&&t(e)},d0=e=>{e.forEach(c0)};function f0({root:e,...t}){const n=e||document;ys.has(n)||ys.set(n,{});const r=ys.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(d0,{root:e,...t})),r[i]}function p0(e,t,n){const r=f0(t);return Sl.set(e,n),r.observe(e),()=>{Sl.delete(e),r.unobserve(e)}}const h0={some:0,all:1};class m0 extends Xt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:o}=t,s={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:h0[i]},l=a=>{const{isIntersecting:c}=a;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:f,onViewportLeave:d}=this.node.getProps(),p=c?f:d;p&&p(a)};return p0(this.node.current,s,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(g0(t,n))&&this.startObserver()}unmount(){}}function g0({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const y0={inView:{Feature:m0},tap:{Feature:u0},focus:{Feature:a0},hover:{Feature:l0}};function Hp(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function v0(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function x0(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function zo(e,t,n){const r=e.getProps();return _a(r,t,n!==void 0?n:r.custom,v0(e),x0(e))}let Ia=J;const an=e=>e*1e3,pt=e=>e/1e3,w0={current:!1},$p=e=>Array.isArray(e)&&typeof e[0]=="number";function Xp(e){return!!(!e||typeof e=="string"&&Gp[e]||$p(e)||Array.isArray(e)&&e.every(Xp))}const hr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Gp={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:hr([0,.65,.55,1]),circOut:hr([.55,0,1,.45]),backIn:hr([.31,.01,.66,-.59]),backOut:hr([.33,1.53,.69,.99])};function Kp(e){if(e)return $p(e)?hr(e):Array.isArray(e)?e.map(Kp):Gp[e]}function S0(e,t,n,{delay:r=0,duration:i,repeat:o=0,repeatType:s="loop",ease:l,times:a}={}){const c={[t]:n};a&&(c.offset=a);const f=Kp(l);return Array.isArray(f)&&(c.easing=f),e.animate(c,{delay:r,duration:i,easing:Array.isArray(f)?"linear":f,fill:"both",iterations:o+1,direction:s==="reverse"?"alternate":"normal"})}function j0(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Qp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,C0=1e-7,k0=12;function P0(e,t,n,r,i){let o,s,l=0;do s=t+(n-t)/2,o=Qp(s,r,i)-e,o>0?n=s:t=s;while(Math.abs(o)>C0&&++l<k0);return s}function ri(e,t,n,r){if(e===t&&n===r)return J;const i=o=>P0(o,0,1,e,n);return o=>o===0||o===1?o:Qp(i(o),t,r)}const E0=ri(.42,0,1,1),T0=ri(0,0,.58,1),Yp=ri(.42,0,.58,1),L0=e=>Array.isArray(e)&&typeof e[0]!="number",Zp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Jp=e=>t=>1-e(1-t),ba=e=>1-Math.sin(Math.acos(e)),qp=Jp(ba),R0=Zp(ba),eh=ri(.33,1.53,.69,.99),za=Jp(eh),A0=Zp(za),M0=e=>(e*=2)<1?.5*za(e):.5*(2-Math.pow(2,-10*(e-1))),D0={linear:J,easeIn:E0,easeInOut:Yp,easeOut:T0,circIn:ba,circInOut:R0,circOut:qp,backIn:za,backInOut:A0,backOut:eh,anticipate:M0},pc=e=>{if(Array.isArray(e)){Ia(e.length===4);const[t,n,r,i]=e;return ri(t,n,r,i)}else if(typeof e=="string")return D0[e];return e},Ua=(e,t)=>n=>!!(ti(n)&&Vy.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),th=(e,t,n)=>r=>{if(!ti(r))return r;const[i,o,s,l]=r.match(Io);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(s),alpha:l!==void 0?parseFloat(l):1}},V0=e=>zt(0,255,e),vs={...yn,transform:e=>Math.round(V0(e))},on={test:Ua("rgb","red"),parse:th("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+vs.transform(e)+", "+vs.transform(t)+", "+vs.transform(n)+", "+Pr(kr.transform(r))+")"};function O0(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const jl={test:Ua("#"),parse:O0,transform:on.transform},Vn={test:Ua("hsl","hue"),parse:th("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+ot.transform(Pr(t))+", "+ot.transform(Pr(n))+", "+Pr(kr.transform(r))+")"},ye={test:e=>on.test(e)||jl.test(e)||Vn.test(e),parse:e=>on.test(e)?on.parse(e):Vn.test(e)?Vn.parse(e):jl.parse(e),transform:e=>ti(e)?e:e.hasOwnProperty("red")?on.transform(e):Vn.transform(e)},X=(e,t,n)=>-n*e+n*t+e;function xs(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function N0({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,o=0,s=0;if(!t)i=o=s=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=xs(a,l,e+1/3),o=xs(a,l,e),s=xs(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(s*255),alpha:r}}const ws=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},F0=[jl,on,Vn],B0=e=>F0.find(t=>t.test(e));function hc(e){const t=B0(e);let n=t.parse(e);return t===Vn&&(n=N0(n)),n}const nh=(e,t)=>{const n=hc(e),r=hc(t),i={...n};return o=>(i.red=ws(n.red,r.red,o),i.green=ws(n.green,r.green,o),i.blue=ws(n.blue,r.blue,o),i.alpha=X(n.alpha,r.alpha,o),on.transform(i))};function _0(e){var t,n;return isNaN(e)&&ti(e)&&(((t=e.match(Io))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Rp))===null||n===void 0?void 0:n.length)||0)>0}const rh={regex:My,countKey:"Vars",token:"${v}",parse:J},ih={regex:Rp,countKey:"Colors",token:"${c}",parse:ye.parse},oh={regex:Io,countKey:"Numbers",token:"${n}",parse:yn.parse};function Ss(e,{regex:t,countKey:n,token:r,parse:i}){const o=e.tokenised.match(t);o&&(e["num"+n]=o.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...o.map(i)))}function ho(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Ss(n,rh),Ss(n,ih),Ss(n,oh),n}function sh(e){return ho(e).values}function lh(e){const{values:t,numColors:n,numVars:r,tokenised:i}=ho(e),o=t.length;return s=>{let l=i;for(let a=0;a<o;a++)a<r?l=l.replace(rh.token,s[a]):a<r+n?l=l.replace(ih.token,ye.transform(s[a])):l=l.replace(oh.token,Pr(s[a]));return l}}const I0=e=>typeof e=="number"?0:e;function b0(e){const t=sh(e);return lh(e)(t.map(I0))}const Ut={test:_0,parse:sh,createTransformer:lh,getAnimatableNone:b0},ah=(e,t)=>n=>`${n>0?t:e}`;function uh(e,t){return typeof e=="number"?n=>X(e,t,n):ye.test(e)?nh(e,t):e.startsWith("var(")?ah(e,t):dh(e,t)}const ch=(e,t)=>{const n=[...e],r=n.length,i=e.map((o,s)=>uh(o,t[s]));return o=>{for(let s=0;s<r;s++)n[s]=i[s](o);return n}},z0=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=uh(e[i],t[i]));return i=>{for(const o in r)n[o]=r[o](i);return n}},dh=(e,t)=>{const n=Ut.createTransformer(t),r=ho(e),i=ho(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?_t(ch(r.values,i.values),n):ah(e,t)},Gr=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},mc=(e,t)=>n=>X(e,t,n);function U0(e){return typeof e=="number"?mc:typeof e=="string"?ye.test(e)?nh:dh:Array.isArray(e)?ch:typeof e=="object"?z0:mc}function W0(e,t,n){const r=[],i=n||U0(e[0]),o=e.length-1;for(let s=0;s<o;s++){let l=i(e[s],e[s+1]);if(t){const a=Array.isArray(t)?t[s]||J:t;l=_t(a,l)}r.push(l)}return r}function fh(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const o=e.length;if(Ia(o===t.length),o===1)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const s=W0(t,r,i),l=s.length,a=c=>{let f=0;if(l>1)for(;f<e.length-2&&!(c<e[f+1]);f++);const d=Gr(e[f],e[f+1],c);return s[f](d)};return n?c=>a(zt(e[0],e[o-1],c)):a}function H0(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Gr(0,t,r);e.push(X(n,1,i))}}function $0(e){const t=[0];return H0(t,e.length-1),t}function X0(e,t){return e.map(n=>n*t)}function G0(e,t){return e.map(()=>t||Yp).splice(0,e.length-1)}function mo({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=L0(r)?r.map(pc):pc(r),o={done:!1,value:t[0]},s=X0(n&&n.length===t.length?n:$0(t),e),l=fh(s,t,{ease:Array.isArray(i)?i:G0(t,i)});return{calculatedDuration:e,next:a=>(o.value=l(a),o.done=a>=e,o)}}function ph(e,t){return t?e*(1e3/t):0}const K0=5;function hh(e,t,n){const r=Math.max(t-K0,0);return ph(n-e(r),t-r)}const js=.001,Q0=.01,Y0=10,Z0=.05,J0=1;function q0({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,o,s=1-t;s=zt(Z0,J0,s),e=zt(Q0,Y0,pt(e)),s<1?(i=c=>{const f=c*s,d=f*e,p=f-n,m=Cl(c,s),v=Math.exp(-d);return js-p/m*v},o=c=>{const d=c*s*e,p=d*n+n,m=Math.pow(s,2)*Math.pow(c,2)*e,v=Math.exp(-d),x=Cl(Math.pow(c,2),s);return(-i(c)+js>0?-1:1)*((p-m)*v)/x}):(i=c=>{const f=Math.exp(-c*e),d=(c-n)*e+1;return-js+f*d},o=c=>{const f=Math.exp(-c*e),d=(n-c)*(e*e);return f*d});const l=5/e,a=tv(i,o,l);if(e=an(e),isNaN(a))return{stiffness:100,damping:10,duration:e};{const c=Math.pow(a,2)*r;return{stiffness:c,damping:s*2*Math.sqrt(r*c),duration:e}}}const ev=12;function tv(e,t,n){let r=n;for(let i=1;i<ev;i++)r=r-e(r)/t(r);return r}function Cl(e,t){return e*Math.sqrt(1-t*t)}const nv=["duration","bounce"],rv=["stiffness","damping","mass"];function gc(e,t){return t.some(n=>e[n]!==void 0)}function iv(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!gc(e,rv)&&gc(e,nv)){const n=q0(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function mh({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],o=e[e.length-1],s={done:!1,value:i},{stiffness:l,damping:a,mass:c,duration:f,velocity:d,isResolvedFromDuration:p}=iv({...r,velocity:-pt(r.velocity||0)}),m=d||0,v=a/(2*Math.sqrt(l*c)),x=o-i,S=pt(Math.sqrt(l/c)),y=Math.abs(x)<5;n||(n=y?.01:2),t||(t=y?.005:.5);let h;if(v<1){const g=Cl(S,v);h=w=>{const j=Math.exp(-v*S*w);return o-j*((m+v*S*x)/g*Math.sin(g*w)+x*Math.cos(g*w))}}else if(v===1)h=g=>o-Math.exp(-S*g)*(x+(m+S*x)*g);else{const g=S*Math.sqrt(v*v-1);h=w=>{const j=Math.exp(-v*S*w),E=Math.min(g*w,300);return o-j*((m+v*S*x)*Math.sinh(E)+g*x*Math.cosh(E))/g}}return{calculatedDuration:p&&f||null,next:g=>{const w=h(g);if(p)s.done=g>=f;else{let j=m;g!==0&&(v<1?j=hh(h,g,w):j=0);const E=Math.abs(j)<=n,P=Math.abs(o-w)<=t;s.done=E&&P}return s.value=s.done?o:w,s}}}function yc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:s,min:l,max:a,restDelta:c=.5,restSpeed:f}){const d=e[0],p={done:!1,value:d},m=k=>l!==void 0&&k<l||a!==void 0&&k>a,v=k=>l===void 0?a:a===void 0||Math.abs(l-k)<Math.abs(a-k)?l:a;let x=n*t;const S=d+x,y=s===void 0?S:s(S);y!==S&&(x=y-d);const h=k=>-x*Math.exp(-k/r),g=k=>y+h(k),w=k=>{const O=h(k),D=g(k);p.done=Math.abs(O)<=c,p.value=p.done?y:D};let j,E;const P=k=>{m(p.value)&&(j=k,E=mh({keyframes:[p.value,v(p.value)],velocity:hh(g,k,p.value),damping:i,stiffness:o,restDelta:c,restSpeed:f}))};return P(0),{calculatedDuration:null,next:k=>{let O=!1;return!E&&j===void 0&&(O=!0,w(k),P(k)),j!==void 0&&k>j?E.next(k-j):(!O&&w(k),p)}}}const ov=e=>{const t=({timestamp:n})=>e(n);return{start:()=>U.update(t,!0),stop:()=>vt(t),now:()=>pe.isProcessing?pe.timestamp:performance.now()}},vc=2e4;function xc(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<vc;)t+=n,r=e.next(t);return t>=vc?1/0:t}const sv={decay:yc,inertia:yc,tween:mo,keyframes:mo,spring:mh};function go({autoplay:e=!0,delay:t=0,driver:n=ov,keyframes:r,type:i="keyframes",repeat:o=0,repeatDelay:s=0,repeatType:l="loop",onPlay:a,onStop:c,onComplete:f,onUpdate:d,...p}){let m=1,v=!1,x,S;const y=()=>{S=new Promise(V=>{x=V})};y();let h;const g=sv[i]||mo;let w;g!==mo&&typeof r[0]!="number"&&(w=fh([0,100],r,{clamp:!1}),r=[0,100]);const j=g({...p,keyframes:r});let E;l==="mirror"&&(E=g({...p,keyframes:[...r].reverse(),velocity:-(p.velocity||0)}));let P="idle",k=null,O=null,D=null;j.calculatedDuration===null&&o&&(j.calculatedDuration=xc(j));const{calculatedDuration:re}=j;let le=1/0,ge=1/0;re!==null&&(le=re+s,ge=le*(o+1)-s);let ie=0;const wt=V=>{if(O===null)return;m>0&&(O=Math.min(O,V)),m<0&&(O=Math.min(V-ge/m,O)),k!==null?ie=k:ie=Math.round(V-O)*m;const W=ie-t*(m>=0?1:-1),Gt=m>=0?W<0:W>ge;ie=Math.max(W,0),P==="finished"&&k===null&&(ie=ge);let qe=ie,vn=j;if(o){const Uo=Math.min(ie,ge)/le;let ii=Math.floor(Uo),Qt=Uo%1;!Qt&&Uo>=1&&(Qt=1),Qt===1&&ii--,ii=Math.min(ii,o+1),!!(ii%2)&&(l==="reverse"?(Qt=1-Qt,s&&(Qt-=s/le)):l==="mirror"&&(vn=E)),qe=zt(0,1,Qt)*le}const Le=Gt?{done:!1,value:r[0]}:vn.next(qe);w&&(Le.value=w(Le.value));let{done:Kt}=Le;!Gt&&re!==null&&(Kt=m>=0?ie>=ge:ie<=0);const zh=k===null&&(P==="finished"||P==="running"&&Kt);return d&&d(Le.value),zh&&T(),Le},Y=()=>{h&&h.stop(),h=void 0},Fe=()=>{P="idle",Y(),x(),y(),O=D=null},T=()=>{P="finished",f&&f(),Y(),x()},M=()=>{if(v)return;h||(h=n(wt));const V=h.now();a&&a(),k!==null?O=V-k:(!O||P==="finished")&&(O=V),P==="finished"&&y(),D=O,k=null,P="running",h.start()};e&&M();const N={then(V,W){return S.then(V,W)},get time(){return pt(ie)},set time(V){V=an(V),ie=V,k!==null||!h||m===0?k=V:O=h.now()-V/m},get duration(){const V=j.calculatedDuration===null?xc(j):j.calculatedDuration;return pt(V)},get speed(){return m},set speed(V){V===m||!h||(m=V,N.time=pt(ie))},get state(){return P},play:M,pause:()=>{P="paused",k=ie},stop:()=>{v=!0,P!=="idle"&&(P="idle",c&&c(),Fe())},cancel:()=>{D!==null&&wt(D),Fe()},complete:()=>{P="finished"},sample:V=>(O=0,wt(V))};return N}function lv(e){let t;return()=>(t===void 0&&(t=e()),t)}const av=lv(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),uv=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),ki=10,cv=2e4,dv=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Xp(t.ease);function fv(e,t,{onUpdate:n,onComplete:r,...i}){if(!(av()&&uv.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let s=!1,l,a,c=!1;const f=()=>{a=new Promise(g=>{l=g})};f();let{keyframes:d,duration:p=300,ease:m,times:v}=i;if(dv(t,i)){const g=go({...i,repeat:0,delay:0});let w={done:!1,value:d[0]};const j=[];let E=0;for(;!w.done&&E<cv;)w=g.sample(E),j.push(w.value),E+=ki;v=void 0,d=j,p=E-ki,m="linear"}const x=S0(e.owner.current,t,d,{...i,duration:p,ease:m,times:v}),S=()=>{c=!1,x.cancel()},y=()=>{c=!0,U.update(S),l(),f()};return x.onfinish=()=>{c||(e.set(j0(d,i)),r&&r(),y())},{then(g,w){return a.then(g,w)},attachTimeline(g){return x.timeline=g,x.onfinish=null,J},get time(){return pt(x.currentTime||0)},set time(g){x.currentTime=an(g)},get speed(){return x.playbackRate},set speed(g){x.playbackRate=g},get duration(){return pt(p)},play:()=>{s||(x.play(),vt(S))},pause:()=>x.pause(),stop:()=>{if(s=!0,x.playState==="idle")return;const{currentTime:g}=x;if(g){const w=go({...i,autoplay:!1});e.setWithVelocity(w.sample(g-ki).value,w.sample(g).value,ki)}y()},complete:()=>{c||x.finish()},cancel:y}}function pv({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:J,pause:J,stop:J,then:o=>(o(),Promise.resolve()),cancel:J,complete:J});return t?go({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const hv={type:"spring",stiffness:500,damping:25,restSpeed:10},mv=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),gv={type:"keyframes",duration:.8},yv={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},vv=(e,{keyframes:t})=>t.length>2?gv:gn.has(e)?e.startsWith("scale")?mv(t[1]):hv:yv,kl=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Ut.test(t)||t==="0")&&!t.startsWith("url(")),xv=new Set(["brightness","contrast","saturate","opacity"]);function wv(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(Io)||[];if(!r)return e;const i=n.replace(r,"");let o=xv.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const Sv=/([a-z-]*)\(.*?\)/g,Pl={...Ut,getAnimatableNone:e=>{const t=e.match(Sv);return t?t.map(wv).join(" "):e}},jv={...Ap,color:ye,backgroundColor:ye,outlineColor:ye,fill:ye,stroke:ye,borderColor:ye,borderTopColor:ye,borderRightColor:ye,borderBottomColor:ye,borderLeftColor:ye,filter:Pl,WebkitFilter:Pl},Wa=e=>jv[e];function gh(e,t){let n=Wa(e);return n!==Pl&&(n=Ut),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const yh=e=>/^0[^.\s]+$/.test(e);function Cv(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||yh(e)}function kv(e,t,n,r){const i=kl(t,n);let o;Array.isArray(n)?o=[...n]:o=[null,n];const s=r.from!==void 0?r.from:e.get();let l;const a=[];for(let c=0;c<o.length;c++)o[c]===null&&(o[c]=c===0?s:o[c-1]),Cv(o[c])&&a.push(c),typeof o[c]=="string"&&o[c]!=="none"&&o[c]!=="0"&&(l=o[c]);if(i&&a.length&&l)for(let c=0;c<a.length;c++){const f=a[c];o[f]=gh(t,l)}return o}function Pv({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:o,repeatType:s,repeatDelay:l,from:a,elapsed:c,...f}){return!!Object.keys(f).length}function Ha(e,t){return e[t]||e.default||e}const Ev={skipAnimations:!1},$a=(e,t,n,r={})=>i=>{const o=Ha(r,e)||{},s=o.delay||r.delay||0;let{elapsed:l=0}=r;l=l-an(s);const a=kv(t,e,n,o),c=a[0],f=a[a.length-1],d=kl(e,c),p=kl(e,f);let m={keyframes:a,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-l,onUpdate:v=>{t.set(v),o.onUpdate&&o.onUpdate(v)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(Pv(o)||(m={...m,...vv(e,m)}),m.duration&&(m.duration=an(m.duration)),m.repeatDelay&&(m.repeatDelay=an(m.repeatDelay)),!d||!p||w0.current||o.type===!1||Ev.skipAnimations)return pv(m);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const v=fv(t,e,m);if(v)return v}return go(m)};function yo(e){return!!(Te(e)&&e.add)}const vh=e=>/^\-?\d*\.?\d+$/.test(e);function Xa(e,t){e.indexOf(t)===-1&&e.push(t)}function Ga(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Ka{constructor(){this.subscriptions=[]}add(t){return Xa(this.subscriptions,t),()=>Ga(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let o=0;o<i;o++){const s=this.subscriptions[o];s&&s(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Tv=e=>!isNaN(parseFloat(e));class Lv{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:o,timestamp:s}=pe;this.lastUpdated!==s&&(this.timeDelta=o,this.lastUpdated=s,U.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>U.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=Tv(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new Ka);const r=this.events[t].add(n);return t==="change"?()=>{r(),U.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?ph(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Qn(e,t){return new Lv(e,t)}const xh=e=>t=>t.test(e),Rv={test:e=>e==="auto",parse:e=>e},wh=[yn,A,ot,jt,Ny,Oy,Rv],ar=e=>wh.find(xh(e)),Av=[...wh,ye,Ut],Mv=e=>Av.find(xh(e));function Dv(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Qn(n))}function Vv(e,t){const n=zo(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const s in o){const l=Yy(o[s]);Dv(e,s,l)}}function Ov(e,t,n){var r,i;const o=Object.keys(t).filter(l=>!e.hasValue(l)),s=o.length;if(s)for(let l=0;l<s;l++){const a=o[l],c=t[a];let f=null;Array.isArray(c)&&(f=c[0]),f===null&&(f=(i=(r=n[a])!==null&&r!==void 0?r:e.readValue(a))!==null&&i!==void 0?i:t[a]),f!=null&&(typeof f=="string"&&(vh(f)||yh(f))?f=parseFloat(f):!Mv(f)&&Ut.test(c)&&(f=gh(a,c)),e.addValue(a,Qn(f,{owner:e})),n[a]===void 0&&(n[a]=f),f!==null&&e.setBaseTarget(a,f))}}function Nv(e,t){return t?(t[e]||t.default||t).from:void 0}function Fv(e,t,n){const r={};for(const i in e){const o=Nv(i,t);if(o!==void 0)r[i]=o;else{const s=n.getValue(i);s&&(r[i]=s.get())}}return r}function Bv({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function _v(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Sh(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:s,...l}=e.makeTargetAnimatable(t);const a=e.getValue("willChange");r&&(o=r);const c=[],f=i&&e.animationState&&e.animationState.getState()[i];for(const d in l){const p=e.getValue(d),m=l[d];if(!p||m===void 0||f&&Bv(f,d))continue;const v={delay:n,elapsed:0,...Ha(o||{},d)};if(window.HandoffAppearAnimations){const y=e.getProps()[jp];if(y){const h=window.HandoffAppearAnimations(y,d,p,U);h!==null&&(v.elapsed=h,v.isHandoff=!0)}}let x=!v.isHandoff&&!_v(p,m);if(v.type==="spring"&&(p.getVelocity()||v.velocity)&&(x=!1),p.animation&&(x=!1),x)continue;p.start($a(d,p,m,e.shouldReduceMotion&&gn.has(d)?{type:!1}:v));const S=p.animation;yo(a)&&(a.add(d),S.then(()=>a.remove(d))),c.push(S)}return s&&Promise.all(c).then(()=>{s&&Vv(e,s)}),c}function El(e,t,n={}){const r=zo(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(Sh(e,r,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(a=0)=>{const{delayChildren:c=0,staggerChildren:f,staggerDirection:d}=i;return Iv(e,t,c+a,f,d,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[a,c]=l==="beforeChildren"?[o,s]:[s,o];return a().then(()=>c())}else return Promise.all([o(),s(n.delay)])}function Iv(e,t,n=0,r=0,i=1,o){const s=[],l=(e.variantChildren.size-1)*r,a=i===1?(c=0)=>c*r:(c=0)=>l-c*r;return Array.from(e.variantChildren).sort(bv).forEach((c,f)=>{c.notify("AnimationStart",t),s.push(El(c,t,{...o,delay:n+a(f)}).then(()=>c.notify("AnimationComplete",t)))}),Promise.all(s)}function bv(e,t){return e.sortNodePosition(t)}function zv(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(o=>El(e,o,n));r=Promise.all(i)}else if(typeof t=="string")r=El(e,t,n);else{const i=typeof t=="function"?zo(e,t,n.custom):t;r=Promise.all(Sh(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const Uv=[...Aa].reverse(),Wv=Aa.length;function Hv(e){return t=>Promise.all(t.map(({animation:n,options:r})=>zv(e,n,r)))}function $v(e){let t=Hv(e);const n=Gv();let r=!0;const i=(a,c)=>{const f=zo(e,c);if(f){const{transition:d,transitionEnd:p,...m}=f;a={...a,...m,...p}}return a};function o(a){t=a(e)}function s(a,c){const f=e.getProps(),d=e.getVariantContext(!0)||{},p=[],m=new Set;let v={},x=1/0;for(let y=0;y<Wv;y++){const h=Uv[y],g=n[h],w=f[h]!==void 0?f[h]:d[h],j=$r(w),E=h===c?g.isActive:null;E===!1&&(x=y);let P=w===d[h]&&w!==f[h]&&j;if(P&&r&&e.manuallyAnimateOnMount&&(P=!1),g.protectedKeys={...v},!g.isActive&&E===null||!w&&!g.prevProp||Bo(w)||typeof w=="boolean")continue;let O=Xv(g.prevProp,w)||h===c&&g.isActive&&!P&&j||y>x&&j,D=!1;const re=Array.isArray(w)?w:[w];let le=re.reduce(i,{});E===!1&&(le={});const{prevResolvedValues:ge={}}=g,ie={...ge,...le},wt=Y=>{O=!0,m.has(Y)&&(D=!0,m.delete(Y)),g.needsAnimating[Y]=!0};for(const Y in ie){const Fe=le[Y],T=ge[Y];if(v.hasOwnProperty(Y))continue;let M=!1;po(Fe)&&po(T)?M=!Hp(Fe,T):M=Fe!==T,M?Fe!==void 0?wt(Y):m.add(Y):Fe!==void 0&&m.has(Y)?wt(Y):g.protectedKeys[Y]=!0}g.prevProp=w,g.prevResolvedValues=le,g.isActive&&(v={...v,...le}),r&&e.blockInitialAnimation&&(O=!1),O&&(!P||D)&&p.push(...re.map(Y=>({animation:Y,options:{type:h,...a}})))}if(m.size){const y={};m.forEach(h=>{const g=e.getBaseTarget(h);g!==void 0&&(y[h]=g)}),p.push({animation:y})}let S=!!p.length;return r&&(f.initial===!1||f.initial===f.animate)&&!e.manuallyAnimateOnMount&&(S=!1),r=!1,S?t(p):Promise.resolve()}function l(a,c,f){var d;if(n[a].isActive===c)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(m=>{var v;return(v=m.animationState)===null||v===void 0?void 0:v.setActive(a,c)}),n[a].isActive=c;const p=s(f,a);for(const m in n)n[m].protectedKeys={};return p}return{animateChanges:s,setActive:l,setAnimateFunction:o,getState:()=>n}}function Xv(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Hp(t,e):!1}function Yt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Gv(){return{animate:Yt(!0),whileInView:Yt(),whileHover:Yt(),whileTap:Yt(),whileDrag:Yt(),whileFocus:Yt(),exit:Yt()}}class Kv extends Xt{constructor(t){super(t),t.animationState||(t.animationState=$v(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),Bo(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let Qv=0;class Yv extends Xt{constructor(){super(...arguments),this.id=Qv++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const o=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&o.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const Zv={animation:{Feature:Kv},exit:{Feature:Yv}},wc=(e,t)=>Math.abs(e-t);function Jv(e,t){const n=wc(e.x,t.x),r=wc(e.y,t.y);return Math.sqrt(n**2+r**2)}class jh{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=ks(this.lastMoveEventInfo,this.history),p=this.startEvent!==null,m=Jv(d.offset,{x:0,y:0})>=3;if(!p&&!m)return;const{point:v}=d,{timestamp:x}=pe;this.history.push({...v,timestamp:x});const{onStart:S,onMove:y}=this.handlers;p||(S&&S(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,d)},this.handlePointerMove=(d,p)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Cs(p,this.transformPagePoint),U.update(this.updatePoint,!0)},this.handlePointerUp=(d,p)=>{this.end();const{onEnd:m,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=ks(d.type==="pointercancel"?this.lastMoveEventInfo:Cs(p,this.transformPagePoint),this.history);this.startEvent&&m&&m(d,S),v&&v(d,S)},!Ip(t))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const s=bo(t),l=Cs(s,this.transformPagePoint),{point:a}=l,{timestamp:c}=pe;this.history=[{...a,timestamp:c}];const{onSessionStart:f}=n;f&&f(t,ks(l,this.history)),this.removeListeners=_t(ft(this.contextWindow,"pointermove",this.handlePointerMove),ft(this.contextWindow,"pointerup",this.handlePointerUp),ft(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),vt(this.updatePoint)}}function Cs(e,t){return t?{point:t(e.point)}:e}function Sc(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ks({point:e},t){return{point:e,delta:Sc(e,Ch(t)),offset:Sc(e,qv(t)),velocity:ex(t,.1)}}function qv(e){return e[0]}function Ch(e){return e[e.length-1]}function ex(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=Ch(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>an(t)));)n--;if(!r)return{x:0,y:0};const o=pt(i.timestamp-r.timestamp);if(o===0)return{x:0,y:0};const s={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return s.x===1/0&&(s.x=0),s.y===1/0&&(s.y=0),s}function Ve(e){return e.max-e.min}function Tl(e,t=0,n=.01){return Math.abs(e-t)<=n}function jc(e,t,n,r=.5){e.origin=r,e.originPoint=X(t.min,t.max,e.origin),e.scale=Ve(n)/Ve(t),(Tl(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=X(n.min,n.max,e.origin)-e.originPoint,(Tl(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Er(e,t,n,r){jc(e.x,t.x,n.x,r?r.originX:void 0),jc(e.y,t.y,n.y,r?r.originY:void 0)}function Cc(e,t,n){e.min=n.min+t.min,e.max=e.min+Ve(t)}function tx(e,t,n){Cc(e.x,t.x,n.x),Cc(e.y,t.y,n.y)}function kc(e,t,n){e.min=t.min-n.min,e.max=e.min+Ve(t)}function Tr(e,t,n){kc(e.x,t.x,n.x),kc(e.y,t.y,n.y)}function nx(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?X(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?X(n,e,r.max):Math.min(e,n)),e}function Pc(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function rx(e,{top:t,left:n,bottom:r,right:i}){return{x:Pc(e.x,n,i),y:Pc(e.y,t,r)}}function Ec(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function ix(e,t){return{x:Ec(e.x,t.x),y:Ec(e.y,t.y)}}function ox(e,t){let n=.5;const r=Ve(e),i=Ve(t);return i>r?n=Gr(t.min,t.max-r,e.min):r>i&&(n=Gr(e.min,e.max-i,t.min)),zt(0,1,n)}function sx(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Ll=.35;function lx(e=Ll){return e===!1?e=0:e===!0&&(e=Ll),{x:Tc(e,"left","right"),y:Tc(e,"top","bottom")}}function Tc(e,t,n){return{min:Lc(e,t),max:Lc(e,n)}}function Lc(e,t){return typeof e=="number"?e:e[t]||0}const Rc=()=>({translate:0,scale:1,origin:0,originPoint:0}),On=()=>({x:Rc(),y:Rc()}),Ac=()=>({min:0,max:0}),q=()=>({x:Ac(),y:Ac()});function _e(e){return[e("x"),e("y")]}function kh({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function ax({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function ux(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Ps(e){return e===void 0||e===1}function Rl({scale:e,scaleX:t,scaleY:n}){return!Ps(e)||!Ps(t)||!Ps(n)}function qt(e){return Rl(e)||Ph(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Ph(e){return Mc(e.x)||Mc(e.y)}function Mc(e){return e&&e!=="0%"}function vo(e,t,n){const r=e-n,i=t*r;return n+i}function Dc(e,t,n,r,i){return i!==void 0&&(e=vo(e,i,r)),vo(e,n,r)+t}function Al(e,t=0,n=1,r,i){e.min=Dc(e.min,t,n,r,i),e.max=Dc(e.max,t,n,r,i)}function Eh(e,{x:t,y:n}){Al(e.x,t.translate,t.scale,t.originPoint),Al(e.y,n.translate,n.scale,n.originPoint)}function cx(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let o,s;for(let l=0;l<i;l++){o=n[l],s=o.projectionDelta;const a=o.instance;a&&a.style&&a.style.display==="contents"||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&Nn(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,Eh(e,s)),r&&qt(o.latestValues)&&Nn(e,o.latestValues))}t.x=Vc(t.x),t.y=Vc(t.y)}function Vc(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Pt(e,t){e.min=e.min+t,e.max=e.max+t}function Oc(e,t,[n,r,i]){const o=t[i]!==void 0?t[i]:.5,s=X(e.min,e.max,o);Al(e,t[n],t[r],s,t.scale)}const dx=["x","scaleX","originX"],fx=["y","scaleY","originY"];function Nn(e,t){Oc(e.x,t,dx),Oc(e.y,t,fx)}function Th(e,t){return kh(ux(e.getBoundingClientRect(),t))}function px(e,t,n){const r=Th(e,n),{scroll:i}=t;return i&&(Pt(r.x,i.offset.x),Pt(r.y,i.offset.y)),r}const Lh=({current:e})=>e?e.ownerDocument.defaultView:null,hx=new WeakMap;class mx{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=q(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=f=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(bo(f,"page").point)},o=(f,d)=>{const{drag:p,dragPropagation:m,onDragStart:v}=this.getProps();if(p&&!m&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=zp(p),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),_e(S=>{let y=this.getAxisMotionValue(S).get()||0;if(ot.test(y)){const{projection:h}=this.visualElement;if(h&&h.layout){const g=h.layout.layoutBox[S];g&&(y=Ve(g)*(parseFloat(y)/100))}}this.originPoint[S]=y}),v&&U.update(()=>v(f,d),!1,!0);const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},s=(f,d)=>{const{dragPropagation:p,dragDirectionLock:m,onDirectionLock:v,onDrag:x}=this.getProps();if(!p&&!this.openGlobalLock)return;const{offset:S}=d;if(m&&this.currentDirection===null){this.currentDirection=gx(S),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,S),this.updateAxis("y",d.point,S),this.visualElement.render(),x&&x(f,d)},l=(f,d)=>this.stop(f,d),a=()=>_e(f=>{var d;return this.getAnimationState(f)==="paused"&&((d=this.getAxisMotionValue(f).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new jh(t,{onSessionStart:i,onStart:o,onMove:s,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Lh(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&U.update(()=>o(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Pi(t,i,this.currentDirection))return;const o=this.getAxisMotionValue(t);let s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=nx(s,this.constraints[t],this.elastic[t])),o.set(s)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,o=this.constraints;n&&Dn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=rx(i.layoutBox,n):this.constraints=!1,this.elastic=lx(r),o!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&_e(s=>{this.getAxisMotionValue(s)&&(this.constraints[s]=sx(i.layoutBox[s],this.constraints[s]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Dn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=px(r,i.root,this.visualElement.getTransformPagePoint());let s=ix(i.layout.layoutBox,o);if(n){const l=n(ax(s));this.hasMutatedConstraints=!!l,l&&(s=kh(l))}return s}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:o,dragSnapToOrigin:s,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},c=_e(f=>{if(!Pi(f,n,this.currentDirection))return;let d=a&&a[f]||{};s&&(d={min:0,max:0});const p=i?200:1e6,m=i?40:1e7,v={type:"inertia",velocity:r?t[f]:0,bounceStiffness:p,bounceDamping:m,timeConstant:750,restDelta:1,restSpeed:10,...o,...d};return this.startAxisValueAnimation(f,v)});return Promise.all(c).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start($a(t,r,0,n))}stopAnimation(){_e(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){_e(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){_e(n=>{const{drag:r}=this.getProps();if(!Pi(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:s,max:l}=i.layout.layoutBox[n];o.set(t[n]-X(s,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Dn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};_e(s=>{const l=this.getAxisMotionValue(s);if(l){const a=l.get();i[s]=ox({min:a,max:a},this.constraints[s])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),_e(s=>{if(!Pi(s,t,null))return;const l=this.getAxisMotionValue(s),{min:a,max:c}=this.constraints[s];l.set(X(a,c,i[s]))})}addListeners(){if(!this.visualElement.current)return;hx.set(this.visualElement,this);const t=this.visualElement.current,n=ft(t,"pointerdown",a=>{const{drag:c,dragListener:f=!0}=this.getProps();c&&f&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();Dn(a)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const s=ct(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:c})=>{this.isDragging&&c&&(_e(f=>{const d=this.getAxisMotionValue(f);d&&(this.originPoint[f]+=a[f].translate,d.set(d.get()+a[f].translate))}),this.visualElement.render())});return()=>{s(),n(),o(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:s=Ll,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:o,dragElastic:s,dragMomentum:l}}}function Pi(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function gx(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class yx extends Xt{constructor(t){super(t),this.removeGroupControls=J,this.removeListeners=J,this.controls=new mx(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||J}unmount(){this.removeGroupControls(),this.removeListeners()}}const Nc=e=>(t,n)=>{e&&U.update(()=>e(t,n))};class vx extends Xt{constructor(){super(...arguments),this.removePointerDownListener=J}onPointerDown(t){this.session=new jh(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Lh(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:Nc(t),onStart:Nc(n),onMove:r,onEnd:(o,s)=>{delete this.session,i&&U.update(()=>i(o,s))}}}mount(){this.removePointerDownListener=ft(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function xx(){const e=R.useContext(La);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=R.useId();return R.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Ii={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Fc(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ur={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(A.test(e))e=parseFloat(e);else return e;const n=Fc(e,t.target.x),r=Fc(e,t.target.y);return`${n}% ${r}%`}},wx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=Ut.parse(e);if(i.length>5)return r;const o=Ut.createTransformer(e),s=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+s]/=l,i[1+s]/=a;const c=X(l,a,.5);return typeof i[2+s]=="number"&&(i[2+s]/=c),typeof i[3+s]=="number"&&(i[3+s]/=c),o(i)}};class Sx extends Bl.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:o}=t;Ty(jx),o&&(n.group&&n.group.add(o),r&&r.register&&i&&r.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ii.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:o}=this.props,s=r.projection;return s&&(s.isPresent=o,i||t.layoutDependency!==n||n===void 0?s.willUpdate():this.safeToRemove(),t.isPresent!==o&&(o?s.promote():s.relegate()||U.postRender(()=>{const l=s.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Rh(e){const[t,n]=xx(),r=R.useContext(kp);return Bl.createElement(Sx,{...e,layoutGroup:r,switchLayoutGroup:R.useContext(Pp),isPresent:t,safeToRemove:n})}const jx={borderRadius:{...ur,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ur,borderTopRightRadius:ur,borderBottomLeftRadius:ur,borderBottomRightRadius:ur,boxShadow:wx},Ah=["TopLeft","TopRight","BottomLeft","BottomRight"],Cx=Ah.length,Bc=e=>typeof e=="string"?parseFloat(e):e,_c=e=>typeof e=="number"||A.test(e);function kx(e,t,n,r,i,o){i?(e.opacity=X(0,n.opacity!==void 0?n.opacity:1,Px(r)),e.opacityExit=X(t.opacity!==void 0?t.opacity:1,0,Ex(r))):o&&(e.opacity=X(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let s=0;s<Cx;s++){const l=`border${Ah[s]}Radius`;let a=Ic(t,l),c=Ic(n,l);if(a===void 0&&c===void 0)continue;a||(a=0),c||(c=0),a===0||c===0||_c(a)===_c(c)?(e[l]=Math.max(X(Bc(a),Bc(c),r),0),(ot.test(c)||ot.test(a))&&(e[l]+="%")):e[l]=c}(t.rotate||n.rotate)&&(e.rotate=X(t.rotate||0,n.rotate||0,r))}function Ic(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const Px=Mh(0,.5,qp),Ex=Mh(.5,.95,J);function Mh(e,t,n){return r=>r<e?0:r>t?1:n(Gr(e,t,r))}function bc(e,t){e.min=t.min,e.max=t.max}function Be(e,t){bc(e.x,t.x),bc(e.y,t.y)}function zc(e,t,n,r,i){return e-=t,e=vo(e,1/n,r),i!==void 0&&(e=vo(e,1/i,r)),e}function Tx(e,t=0,n=1,r=.5,i,o=e,s=e){if(ot.test(t)&&(t=parseFloat(t),t=X(s.min,s.max,t/100)-s.min),typeof t!="number")return;let l=X(o.min,o.max,r);e===o&&(l-=t),e.min=zc(e.min,t,n,l,i),e.max=zc(e.max,t,n,l,i)}function Uc(e,t,[n,r,i],o,s){Tx(e,t[n],t[r],t[i],t.scale,o,s)}const Lx=["x","scaleX","originX"],Rx=["y","scaleY","originY"];function Wc(e,t,n,r){Uc(e.x,t,Lx,n?n.x:void 0,r?r.x:void 0),Uc(e.y,t,Rx,n?n.y:void 0,r?r.y:void 0)}function Hc(e){return e.translate===0&&e.scale===1}function Dh(e){return Hc(e.x)&&Hc(e.y)}function Ax(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function Vh(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function $c(e){return Ve(e.x)/Ve(e.y)}class Mx{constructor(){this.members=[]}add(t){Xa(this.members,t),t.scheduleRender()}remove(t){if(Ga(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){r=o;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Xc(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:a,rotateX:c,rotateY:f}=n;a&&(r+=`rotate(${a}deg) `),c&&(r+=`rotateX(${c}deg) `),f&&(r+=`rotateY(${f}deg) `)}const s=e.x.scale*t.x,l=e.y.scale*t.y;return(s!==1||l!==1)&&(r+=`scale(${s}, ${l})`),r||"none"}const Dx=(e,t)=>e.depth-t.depth;class Vx{constructor(){this.children=[],this.isDirty=!1}add(t){Xa(this.children,t),this.isDirty=!0}remove(t){Ga(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Dx),this.isDirty=!1,this.children.forEach(t)}}function Ox(e,t){const n=performance.now(),r=({timestamp:i})=>{const o=i-n;o>=t&&(vt(r),e(o-t))};return U.read(r,!0),()=>vt(r)}function Nx(e){window.MotionDebug&&window.MotionDebug.record(e)}function Fx(e){return e instanceof SVGElement&&e.tagName!=="svg"}function Bx(e,t,n){const r=Te(e)?e:Qn(e);return r.start($a("",r,t,n)),r.animation}const Gc=["","X","Y","Z"],_x={visibility:"hidden"},Kc=1e3;let Ix=0;const en={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Oh({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(s={},l=t==null?void 0:t()){this.id=Ix++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,en.totalNodes=en.resolvedTargetDeltas=en.recalculatedProjection=0,this.nodes.forEach(Ux),this.nodes.forEach(Gx),this.nodes.forEach(Kx),this.nodes.forEach(Wx),Nx(en)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=s,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new Vx)}addEventListener(s,l){return this.eventHandlers.has(s)||this.eventHandlers.set(s,new Ka),this.eventHandlers.get(s).add(l)}notifyListeners(s,...l){const a=this.eventHandlers.get(s);a&&a.notify(...l)}hasListeners(s){return this.eventHandlers.has(s)}mount(s,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Fx(s),this.instance=s;const{layoutId:a,layout:c,visualElement:f}=this.options;if(f&&!f.current&&f.mount(s),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(c||a)&&(this.isLayoutDirty=!0),e){let d;const p=()=>this.root.updateBlockedByResize=!1;e(s,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=Ox(p,250),Ii.hasAnimatedSinceResize&&(Ii.hasAnimatedSinceResize=!1,this.nodes.forEach(Yc))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&f&&(a||c)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:p,hasRelativeTargetChanged:m,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||f.getDefaultTransition()||qx,{onLayoutAnimationStart:S,onLayoutAnimationComplete:y}=f.getProps(),h=!this.targetLayout||!Vh(this.targetLayout,v)||m,g=!p&&m;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||p&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,g);const w={...Ha(x,"layout"),onPlay:S,onComplete:y};(f.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else p||Yc(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const s=this.getStack();s&&s.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,vt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Qx),this.animationId++)}getTransformTemplate(){const{visualElement:s}=this.options;return s&&s.getProps().transformTemplate}willUpdate(s=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let f=0;f<this.path.length;f++){const d=this.path[f];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),s&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Qc);return}this.isUpdating||this.nodes.forEach($x),this.isUpdating=!1,this.nodes.forEach(Xx),this.nodes.forEach(bx),this.nodes.forEach(zx),this.clearAllSnapshots();const l=performance.now();pe.delta=zt(0,1e3/60,l-pe.timestamp),pe.timestamp=l,pe.isProcessing=!0,ms.update.process(pe),ms.preRender.process(pe),ms.render.process(pe),pe.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Hx),this.sharedNodes.forEach(Yx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,U.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){U.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const s=this.layout;this.layout=this.measure(!1),this.layoutCorrected=q(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,s?s.layoutBox:void 0)}updateScroll(s="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===s&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:s,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const s=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!Dh(this.projectionDelta),a=this.getTransformTemplate(),c=a?a(this.latestValues,""):void 0,f=c!==this.prevTransformTemplateValue;s&&(l||qt(this.latestValues)||f)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(s=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return s&&(a=this.removeTransform(a)),e1(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:s}=this.options;if(!s)return q();const l=s.measureViewportBox(),{scroll:a}=this.root;return a&&(Pt(l.x,a.offset.x),Pt(l.y,a.offset.y)),l}removeElementScroll(s){const l=q();Be(l,s);for(let a=0;a<this.path.length;a++){const c=this.path[a],{scroll:f,options:d}=c;if(c!==this.root&&f&&d.layoutScroll){if(f.isRoot){Be(l,s);const{scroll:p}=this.root;p&&(Pt(l.x,-p.offset.x),Pt(l.y,-p.offset.y))}Pt(l.x,f.offset.x),Pt(l.y,f.offset.y)}}return l}applyTransform(s,l=!1){const a=q();Be(a,s);for(let c=0;c<this.path.length;c++){const f=this.path[c];!l&&f.options.layoutScroll&&f.scroll&&f!==f.root&&Nn(a,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),qt(f.latestValues)&&Nn(a,f.latestValues)}return qt(this.latestValues)&&Nn(a,this.latestValues),a}removeTransform(s){const l=q();Be(l,s);for(let a=0;a<this.path.length;a++){const c=this.path[a];if(!c.instance||!qt(c.latestValues))continue;Rl(c.latestValues)&&c.updateSnapshot();const f=q(),d=c.measurePageBox();Be(f,d),Wc(l,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,f)}return qt(this.latestValues)&&Wc(l,this.latestValues),l}setTargetDelta(s){this.targetDelta=s,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(s){this.options={...this.options,...s,crossfade:s.crossfade!==void 0?s.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==pe.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(s=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const c=!!this.resumingFrom||this!==a;if(!(s||c&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:p}=this.options;if(!(!this.layout||!(d||p))){if(this.resolvedRelativeTargetAt=pe.timestamp,!this.targetDelta&&!this.relativeTarget){const m=this.getClosestProjectingParent();m&&m.layout&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=q(),this.relativeTargetOrigin=q(),Tr(this.relativeTargetOrigin,this.layout.layoutBox,m.layout.layoutBox),Be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=q(),this.targetWithTransforms=q()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),tx(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Be(this.target,this.layout.layoutBox),Eh(this.target,this.targetDelta)):Be(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const m=this.getClosestProjectingParent();m&&!!m.resumingFrom==!!this.resumingFrom&&!m.options.layoutScroll&&m.target&&this.animationProgress!==1?(this.relativeParent=m,this.forceRelativeParentToResolveTarget(),this.relativeTarget=q(),this.relativeTargetOrigin=q(),Tr(this.relativeTargetOrigin,this.target,m.target),Be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}en.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Rl(this.parent.latestValues)||Ph(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var s;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let c=!0;if((this.isProjectionDirty||!((s=this.parent)===null||s===void 0)&&s.isProjectionDirty)&&(c=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===pe.timestamp&&(c=!1),c)return;const{layout:f,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||d))return;Be(this.layoutCorrected,this.layout.layoutBox);const p=this.treeScale.x,m=this.treeScale.y;cx(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox);const{target:v}=l;if(!v){this.projectionTransform&&(this.projectionDelta=On(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=On(),this.projectionDeltaWithTransform=On());const x=this.projectionTransform;Er(this.projectionDelta,this.layoutCorrected,v,this.latestValues),this.projectionTransform=Xc(this.projectionDelta,this.treeScale),(this.projectionTransform!==x||this.treeScale.x!==p||this.treeScale.y!==m)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),en.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(s=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),s){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(s,l=!1){const a=this.snapshot,c=a?a.latestValues:{},f={...this.latestValues},d=On();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const p=q(),m=a?a.source:void 0,v=this.layout?this.layout.source:void 0,x=m!==v,S=this.getStack(),y=!S||S.members.length<=1,h=!!(x&&!y&&this.options.crossfade===!0&&!this.path.some(Jx));this.animationProgress=0;let g;this.mixTargetDelta=w=>{const j=w/1e3;Zc(d.x,s.x,j),Zc(d.y,s.y,j),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Tr(p,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Zx(this.relativeTarget,this.relativeTargetOrigin,p,j),g&&Ax(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=q()),Be(g,this.relativeTarget)),x&&(this.animationValues=f,kx(f,c,this.latestValues,j,h,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=j},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(s){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(vt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=U.update(()=>{Ii.hasAnimatedSinceResize=!0,this.currentAnimation=Bx(0,Kc,{...s,onUpdate:l=>{this.mixTargetDelta(l),s.onUpdate&&s.onUpdate(l)},onComplete:()=>{s.onComplete&&s.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const s=this.getStack();s&&s.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Kc),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const s=this.getLead();let{targetWithTransforms:l,target:a,layout:c,latestValues:f}=s;if(!(!l||!a||!c)){if(this!==s&&this.layout&&c&&Nh(this.options.animationType,this.layout.layoutBox,c.layoutBox)){a=this.target||q();const d=Ve(this.layout.layoutBox.x);a.x.min=s.target.x.min,a.x.max=a.x.min+d;const p=Ve(this.layout.layoutBox.y);a.y.min=s.target.y.min,a.y.max=a.y.min+p}Be(l,a),Nn(l,f),Er(this.projectionDeltaWithTransform,this.layoutCorrected,l,f)}}registerSharedNode(s,l){this.sharedNodes.has(s)||this.sharedNodes.set(s,new Mx),this.sharedNodes.get(s).add(l);const c=l.options.initialPromotionConfig;l.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(l):void 0})}isLead(){const s=this.getStack();return s?s.lead===this:!0}getLead(){var s;const{layoutId:l}=this.options;return l?((s=this.getStack())===null||s===void 0?void 0:s.lead)||this:this}getPrevLead(){var s;const{layoutId:l}=this.options;return l?(s=this.getStack())===null||s===void 0?void 0:s.prevLead:void 0}getStack(){const{layoutId:s}=this.options;if(s)return this.root.sharedNodes.get(s)}promote({needsReset:s,transition:l,preserveFollowOpacity:a}={}){const c=this.getStack();c&&c.promote(this,a),s&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const s=this.getStack();return s?s.relegate(this):!1}resetRotation(){const{visualElement:s}=this.options;if(!s)return;let l=!1;const{latestValues:a}=s;if((a.rotate||a.rotateX||a.rotateY||a.rotateZ)&&(l=!0),!l)return;const c={};for(let f=0;f<Gc.length;f++){const d="rotate"+Gc[f];a[d]&&(c[d]=a[d],s.setStaticValue(d,0))}s.render();for(const f in c)s.setStaticValue(f,c[f]);s.scheduleRender()}getProjectionStyles(s){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return _x;const c={visibility:""},f=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,c.opacity="",c.pointerEvents=_i(s==null?void 0:s.pointerEvents)||"",c.transform=f?f(this.latestValues,""):"none",c;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=_i(s==null?void 0:s.pointerEvents)||""),this.hasProjected&&!qt(this.latestValues)&&(x.transform=f?f({},""):"none",this.hasProjected=!1),x}const p=d.animationValues||d.latestValues;this.applyTransformsToTarget(),c.transform=Xc(this.projectionDeltaWithTransform,this.treeScale,p),f&&(c.transform=f(p,c.transform));const{x:m,y:v}=this.projectionDelta;c.transformOrigin=`${m.origin*100}% ${v.origin*100}% 0`,d.animationValues?c.opacity=d===this?(a=(l=p.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:p.opacityExit:c.opacity=d===this?p.opacity!==void 0?p.opacity:"":p.opacityExit!==void 0?p.opacityExit:0;for(const x in co){if(p[x]===void 0)continue;const{correct:S,applyTo:y}=co[x],h=c.transform==="none"?p[x]:S(p[x],d);if(y){const g=y.length;for(let w=0;w<g;w++)c[y[w]]=h}else c[x]=h}return this.options.layoutId&&(c.pointerEvents=d===this?_i(s==null?void 0:s.pointerEvents)||"":"none"),c}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(s=>{var l;return(l=s.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Qc),this.root.sharedNodes.clear()}}}function bx(e){e.updateLayout()}function zx(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:o}=e.options,s=n.source!==e.layout.source;o==="size"?_e(d=>{const p=s?n.measuredBox[d]:n.layoutBox[d],m=Ve(p);p.min=r[d].min,p.max=p.min+m}):Nh(o,n.layoutBox,r)&&_e(d=>{const p=s?n.measuredBox[d]:n.layoutBox[d],m=Ve(r[d]);p.max=p.min+m,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+m)});const l=On();Er(l,r,n.layoutBox);const a=On();s?Er(a,e.applyTransform(i,!0),n.measuredBox):Er(a,r,n.layoutBox);const c=!Dh(l);let f=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:p,layout:m}=d;if(p&&m){const v=q();Tr(v,n.layoutBox,p.layoutBox);const x=q();Tr(x,r,m.layoutBox),Vh(v,x)||(f=!0),d.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:c,hasRelativeTargetChanged:f})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function Ux(e){en.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Wx(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Hx(e){e.clearSnapshot()}function Qc(e){e.clearMeasurements()}function $x(e){e.isLayoutDirty=!1}function Xx(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Yc(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Gx(e){e.resolveTargetDelta()}function Kx(e){e.calcProjection()}function Qx(e){e.resetRotation()}function Yx(e){e.removeLeadSnapshot()}function Zc(e,t,n){e.translate=X(t.translate,0,n),e.scale=X(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Jc(e,t,n,r){e.min=X(t.min,n.min,r),e.max=X(t.max,n.max,r)}function Zx(e,t,n,r){Jc(e.x,t.x,n.x,r),Jc(e.y,t.y,n.y,r)}function Jx(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const qx={duration:.45,ease:[.4,0,.1,1]},qc=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),ed=qc("applewebkit/")&&!qc("chrome/")?Math.round:J;function td(e){e.min=ed(e.min),e.max=ed(e.max)}function e1(e){td(e.x),td(e.y)}function Nh(e,t,n){return e==="position"||e==="preserve-aspect"&&!Tl($c(t),$c(n),.2)}const t1=Oh({attachResizeListener:(e,t)=>ct(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Es={current:void 0},Fh=Oh({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Es.current){const e=new t1({});e.mount(window),e.setOptions({layoutScroll:!0}),Es.current=e}return Es.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),n1={pan:{Feature:vx},drag:{Feature:yx,ProjectionNode:Fh,MeasureLayout:Rh}},r1=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function i1(e){const t=r1.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Ml(e,t,n=1){const[r,i]=i1(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const s=o.trim();return vh(s)?parseFloat(s):s}else return wl(i)?Ml(i,t,n+1):i}function o1(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const o=i.get();if(!wl(o))return;const s=Ml(o,r);s&&i.set(s)});for(const i in t){const o=t[i];if(!wl(o))continue;const s=Ml(o,r);s&&(t[i]=s,n||(n={}),n[i]===void 0&&(n[i]=o))}return{target:t,transitionEnd:n}}const s1=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),Bh=e=>s1.has(e),l1=e=>Object.keys(e).some(Bh),nd=e=>e===yn||e===A,rd=(e,t)=>parseFloat(e.split(", ")[t]),id=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return rd(i[1],t);{const o=r.match(/^matrix\((.+)\)$/);return o?rd(o[1],e):0}},a1=new Set(["x","y","z"]),u1=ei.filter(e=>!a1.has(e));function c1(e){const t=[];return u1.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Yn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:id(4,13),y:id(5,14)};Yn.translateX=Yn.x;Yn.translateY=Yn.y;const d1=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:s}=o,l={};s==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(c=>{l[c]=Yn[c](r,o)}),t.render();const a=t.measureViewportBox();return n.forEach(c=>{const f=t.getValue(c);f&&f.jump(l[c]),e[c]=Yn[c](a,o)}),e},f1=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(Bh);let o=[],s=!1;const l=[];if(i.forEach(a=>{const c=e.getValue(a);if(!e.hasValue(a))return;let f=n[a],d=ar(f);const p=t[a];let m;if(po(p)){const v=p.length,x=p[0]===null?1:0;f=p[x],d=ar(f);for(let S=x;S<v&&p[S]!==null;S++)m?Ia(ar(p[S])===m):m=ar(p[S])}else m=ar(p);if(d!==m)if(nd(d)&&nd(m)){const v=c.get();typeof v=="string"&&c.set(parseFloat(v)),typeof p=="string"?t[a]=parseFloat(p):Array.isArray(p)&&m===A&&(t[a]=p.map(parseFloat))}else d!=null&&d.transform&&(m!=null&&m.transform)&&(f===0||p===0)?f===0?c.set(m.transform(f)):t[a]=d.transform(p):(s||(o=c1(e),s=!0),l.push(a),r[a]=r[a]!==void 0?r[a]:t[a],c.jump(p))}),l.length){const a=l.indexOf("height")>=0?window.pageYOffset:null,c=d1(t,e,l);return o.length&&o.forEach(([f,d])=>{e.getValue(f).set(d)}),e.render(),Fo&&a!==null&&window.scrollTo({top:a}),{target:c,transitionEnd:r}}else return{target:t,transitionEnd:r}};function p1(e,t,n,r){return l1(t)?f1(e,t,n,r):{target:t,transitionEnd:r}}const h1=(e,t,n,r)=>{const i=o1(e,t,r);return t=i.target,r=i.transitionEnd,p1(e,t,n,r)},Dl={current:null},_h={current:!1};function m1(){if(_h.current=!0,!!Fo)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Dl.current=e.matches;e.addListener(t),t()}else Dl.current=!1}function g1(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],s=n[i];if(Te(o))e.addValue(i,o),yo(r)&&r.add(i);else if(Te(s))e.addValue(i,Qn(o,{owner:e})),yo(r)&&r.remove(i);else if(s!==o)if(e.hasValue(i)){const l=e.getValue(i);!l.hasAnimated&&l.set(o)}else{const l=e.getStaticValue(i);e.addValue(i,Qn(l!==void 0?l:o,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const od=new WeakMap,Ih=Object.keys(Xr),y1=Ih.length,sd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],v1=Ma.length;class x1{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>U.render(this.render,!1,!0);const{latestValues:l,renderState:a}=o;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=a,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=_o(n),this.isVariantNode=Cp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:c,...f}=this.scrapeMotionValuesFromProps(n,{});for(const d in f){const p=f[d];l[d]!==void 0&&Te(p)&&(p.set(l[d],!1),yo(c)&&c.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,od.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),_h.current||m1(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Dl.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){od.delete(this.current),this.projection&&this.projection.unmount(),vt(this.notifyUpdate),vt(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=gn.has(t),i=n.on("change",s=>{this.latestValues[t]=s,this.props.onUpdate&&U.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),o()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,o){let s,l;for(let a=0;a<y1;a++){const c=Ih[a],{isEnabled:f,Feature:d,ProjectionNode:p,MeasureLayout:m}=Xr[c];p&&(s=p),f(n)&&(!this.features[c]&&d&&(this.features[c]=new d(this)),m&&(l=m))}if((this.type==="html"||this.type==="svg")&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);const{layoutId:a,layout:c,drag:f,dragConstraints:d,layoutScroll:p,layoutRoot:m}=n;this.projection.setOptions({layoutId:a,layout:c,alwaysMeasureLayout:!!f||d&&Dn(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof c=="string"?c:"both",initialPromotionConfig:o,layoutScroll:p,layoutRoot:m})}return l}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):q()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<sd.length;r++){const i=sd[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o=t["on"+i];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=g1(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<v1;r++){const i=Ma[r],o=this.props[i];($r(o)||o===!1)&&(n[i]=o)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Qn(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=_a(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const o=this.getBaseTargetFromProps(this.props,t);return o!==void 0&&!Te(o)?o:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new Ka),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class bh extends x1{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},o){let s=Fv(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),s&&(s=i(s))),o){Ov(this,r,s);const l=h1(this,r,s,n);n=l.transitionEnd,r=l.target}return{transition:t,transitionEnd:n,...r}}}function w1(e){return window.getComputedStyle(e)}class S1 extends bh{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(gn.has(n)){const r=Wa(n);return r&&r.default||0}else{const r=w1(t),i=(Lp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Th(t,n)}build(t,n,r,i){Va(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Ba(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Te(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){Op(t,n,r,i)}}class j1 extends bh{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(gn.has(n)){const r=Wa(n);return r&&r.default||0}return n=Np.has(n)?n:Ra(n),t.getAttribute(n)}measureInstanceViewportBox(){return q()}scrapeMotionValuesFromProps(t,n){return Bp(t,n)}build(t,n,r,i){Na(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){Fp(t,n,r,i)}mount(t){this.isSVGTag=Fa(t.tagName),super.mount(t)}}const C1=(e,t)=>Da(e)?new j1(t,{enableHardwareAcceleration:!1}):new S1(t,{enableHardwareAcceleration:!0}),k1={layout:{ProjectionNode:Fh,MeasureLayout:Rh}},P1={...Zv,...y0,...n1,...k1},Kr=Py((e,t)=>i0(e,t,P1,C1)),Ue={BEE_DIRECTIONS:["N","NE","E","SE","S","SW","W","NW"],PHEROMONE_LEVELS:[1,2,3,4,5],WEB_STRUCTURE:{center:[2,2],rings:[[[2,2]],[[1,1],[1,2],[1,3],[2,1],[2,3],[3,1],[3,2],[3,3]],[[0,0],[0,1],[0,2],[0,3],[0,4],[1,0],[1,4],[2,0],[2,4],[3,0],[3,4],[4,0],[4,1],[4,2],[4,3],[4,4]]]},letterToBeeAngle:e=>(e.charCodeAt(0)-65)*45%360,letterToPheromone:e=>(e.charCodeAt(0)-65)%5+1,getWebPosition:e=>{const t=Ue.WEB_STRUCTURE.rings;let n=0;for(let r=0;r<t.length;r++){if(e<n+t[r].length)return{ring:r,position:t[r][e-n],isCenter:r===0};n+=t[r].length}return{ring:2,position:[e%5,Math.floor(e/5)],isCenter:!1}},encryptWithSpiderOnly:(e,t)=>{const n=t*t,r=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(n,"X").slice(0,n);let i=Array.from({length:t},()=>Array(t).fill("")),o=0;for(let s=0;s<t;s++)for(let l=0;l<t;l++)o<r.length&&(i[s][l]=r[o++]);return Ue.readSpiderWebPattern(i,t)},encryptWithBeeOnly:(e,t)=>{const n=t*t,r=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(n,"X").slice(0,n);let i=[];for(let o=0;o<r.length;o++)i.push({letter:r[o],angle:Ue.letterToBeeAngle(r[o]),originalIndex:o});return i.sort((o,s)=>o.angle-s.angle),i.map(o=>o.letter).join("")},encryptWithAntOnly:(e,t)=>{const n=t*t,r=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(n,"X").slice(0,n);let i=[];for(let o=0;o<r.length;o++)i.push({letter:r[o],pheromone:Ue.letterToPheromone(r[o]),originalIndex:o});return i.sort((o,s)=>o.pheromone-s.pheromone||o.originalIndex-s.originalIndex),i.map(o=>o.letter).join("")},readSpiderWebPattern:(e,t)=>{let n="";const r=Math.floor(t/2);let i=Array.from({length:t},()=>Array(t).fill(!1));e[r][r]&&(n+=e[r][r],i[r][r]=!0);for(let o=1;o<=r;o++){let s=[];for(let l=0;l<t;l++)for(let a=0;a<t;a++)Math.max(Math.abs(l-r),Math.abs(a-r))===o&&!i[l][a]&&s.push([l,a]);s.sort((l,a)=>{const[c,f]=l,[d,p]=a,m=Math.atan2(c-r,f-r),v=Math.atan2(d-r,p-r);return m-v});for(let[l,a]of s)e[l][a]&&(n+=e[l][a],i[l][a]=!0)}return n.replace(/X+$/,"")}};function E1({message:e,gridSize:t,cipherType:n}){const[r,i]=R.useState([]),[o,s]=R.useState([]);R.useEffect(()=>{const a=t*t,c=e.toUpperCase().replace(/[^A-Z]/g,"").padEnd(a,"X").slice(0,a);let f=Array(a).fill(""),d=[];for(let p=0;p<c.length;p++){const m=c[p],v=Math.floor(p/t),x=p%t,S=v*t+x,y=Ue.letterToBeeAngle(m),h=Ue.letterToPheromone(m);f[S]=m,d.push({letter:m,beeAngle:y,pheromoneLevel:h,gridIndex:S,row:v,col:x})}i(f),s(d)},[e,t,n]);const l=a=>{const c=o.find(w=>w.gridIndex===a),f=r[a]&&r[a]!=="",d=Math.floor(a/t),p=a%t,m=Math.floor(t/2),v=d===m&&p===m,x=Math.abs(d-m)+Math.abs(p-m);let S="#fef3c7",y="#f59e0b",h="2px";if((n==="spider"||n==="combined")&&(v?(S="#dc2626",y="#991b1b",h="3px"):x===1&&(S="#16a34a",y="#15803d")),(n==="bee"||n==="combined")&&f&&c&&(S=`rgba(251, 191, 36, ${.3+c.beeAngle/360*.7})`,y="#f59e0b"),(n==="ant"||n==="combined")&&f&&c){const w=c.pheromoneLevel/5;n==="ant"?(S=`rgba(34, 197, 94, ${w})`,y="#16a34a"):n==="combined"&&!v&&x>1&&(S=`rgba(34, 197, 94, ${w})`)}const g=Math.max(30,Math.min(60,300/t));return{width:`${g}px`,height:`${g}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`${h} solid ${y}`,borderRadius:v&&(n==="spider"||n==="combined")?"50%":"8px",backgroundColor:S,color:v&&(n==="spider"||n==="combined")?"white":"#92400e",fontWeight:"bold",fontSize:`${Math.max(10,g/3)}px`,position:"relative"}};return u.jsxs("div",{children:[u.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:r.map((a,c)=>{const f=o.find(d=>d.gridIndex===c);return u.jsxs(Kr.div,{style:l(c),initial:{scale:0,rotate:f?f.beeAngle:0},animate:{scale:1,rotate:0},transition:{delay:c*.1,type:"spring",stiffness:200,damping:10},title:f?`🐝 Angle: ${f.beeAngle}° | 🐜 Pheromone: ${f.pheromoneLevel}`:"",children:[a,f&&u.jsx("div",{style:{position:"absolute",top:"-8px",right:"-8px",width:"16px",height:"16px",borderRadius:"50%",backgroundColor:"#dc2626",color:"white",fontSize:"10px",display:"flex",alignItems:"center",justifyContent:"center"},children:f.pheromoneLevel})]},c)})}),u.jsxs("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#f9fafb",borderRadius:"8px",fontSize:"12px"},children:[u.jsxs("div",{style:{fontWeight:"bold",marginBottom:"8px",color:"#374151"},children:["Active Cipher: ",n==="combined"?"All Three Combined":n==="spider"?"Spider Web Only":n==="bee"?"Bee Dance Only":"Ant Trails Only"]}),(n==="spider"||n==="combined")&&u.jsxs("div",{children:[u.jsx("strong",{children:"🕷️ Spider Web:"})," Red center, green inner ring - reads center outward"]}),(n==="bee"||n==="combined")&&u.jsxs("div",{children:[u.jsx("strong",{children:"🐝 Bee Dance:"})," Rotation angle encodes letter direction (A=0°, B=45°, etc.)"]}),(n==="ant"||n==="combined")&&u.jsxs("div",{children:[u.jsx("strong",{children:"🐜 Ant Trails:"})," Pheromone strength (1-5) shown in opacity and red circles"]})]})]})}function T1({message:e,gridSize:t,cipherType:n}){const[r,i]=R.useState([]),[o,s]=R.useState("");return R.useEffect(()=>{const l=e.toUpperCase().replace(/[^A-Z]/g,""),a=t*t;if(l.length===0){i(Array(a).fill("")),s("");return}let c="",f=Array(a).fill("");if(n==="spider"||n==="combined"){let d=Array.from({length:t},()=>Array(t).fill("")),p=0;const m=Math.floor(t/2);p<l.length&&(d[m][m]=l[p++]);for(let v=1;v<=m;v++){let x=[];for(let S=0;S<t;S++)for(let y=0;y<t;y++)Math.max(Math.abs(S-m),Math.abs(y-m))===v&&x.push([S,y]);x.sort((S,y)=>{const[h,g]=S,[w,j]=y,E=Math.atan2(h-m,g-m),P=Math.atan2(w-m,j-m);return E-P});for(let[S,y]of x)p<l.length&&(d[S][y]=l[p++])}for(let v=0;v<t;v++)for(let x=0;x<t;x++){const S=d[v][x]||"",y=v*t+x;f[y]=S,S&&(c+=S)}}else if(n==="bee"){let d=[];for(let p=0;p<l.length;p++)d.push({letter:l[p],angle:Ue.letterToBeeAngle(l[p]),encryptedIndex:p});d.sort((p,m)=>p.encryptedIndex-m.encryptedIndex);for(let p=0;p<d.length&&p<a;p++)f[p]=d[p].letter,c+=d[p].letter}else if(n==="ant"){let d=[];for(let p=0;p<l.length;p++)d.push({letter:l[p],pheromone:Ue.letterToPheromone(l[p]),encryptedIndex:p});d.sort((p,m)=>p.encryptedIndex-m.encryptedIndex);for(let p=0;p<d.length&&p<a;p++)f[p]=d[p].letter,c+=d[p].letter}i(f),s(c.replace(/X+$/,""))},[e,t,n]),u.jsxs("div",{children:[u.jsx("div",{style:{display:"grid",gridTemplateColumns:`repeat(${t}, 1fr)`,gap:"4px",marginTop:"20px",maxWidth:`${Math.min(400,t*70)}px`,margin:"20px auto"},children:r.map((l,a)=>{const c=Math.floor(a/t),f=a%t,d=Math.floor(t/2),p=c===d&&f===d,m=Math.abs(c-d)+Math.abs(f-d),v=Math.max(30,Math.min(60,300/t));let x="#dbeafe",S="#3b82f6";return p?(x="#7c3aed",S="#5b21b6"):m===1&&(x="#059669",S="#047857"),u.jsx(Kr.div,{style:{width:`${v}px`,height:`${v}px`,display:"flex",alignItems:"center",justifyContent:"center",border:`2px solid ${S}`,borderRadius:p?"50%":"8px",backgroundColor:x,color:p||m===1?"white":"#1e40af",fontWeight:"bold",fontSize:`${Math.max(10,v/3)}px`},initial:{scale:0,rotate:180},animate:{scale:1,rotate:0},transition:{delay:a*.08,type:"spring",stiffness:150,damping:12},children:l},a)})}),o&&u.jsx(Kr.div,{style:{marginTop:"20px",padding:"15px",backgroundColor:"#dcfce7",border:"2px solid #16a34a",borderRadius:"8px",textAlign:"center"},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:2.5},children:u.jsxs("strong",{style:{color:"#15803d"},children:["Decrypted Message: ",o]})})]})}function L1({isVisible:e,onToggle:t}){return e?u.jsxs(Kr.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},style:{backgroundColor:"#f0fdf4",border:"2px solid #16a34a",borderRadius:"12px",padding:"20px",marginBottom:"30px",fontSize:"14px",lineHeight:"1.6"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[u.jsx("h3",{style:{color:"#15803d",margin:0},children:"📝 Manual Pen & Paper Guide"}),u.jsx("button",{onClick:t,style:{padding:"5px 10px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"✕ Close"})]}),u.jsxs("div",{style:{display:"grid",gap:"25px"},children:[u.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[u.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"📋 MATERIALS NEEDED"}),u.jsxs("ul",{style:{marginLeft:"20px"},children:[u.jsx("li",{children:"✏️ Pencil and eraser"}),u.jsx("li",{children:"📄 Graph paper or ruled paper"}),u.jsx("li",{children:"📐 Ruler (optional, for neat grids)"}),u.jsx("li",{children:"🎨 Colored pens/pencils (optional, for visual coding)"})]})]}),u.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[u.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"🔐 MANUAL ENCRYPTION STEPS"}),u.jsxs("div",{style:{marginLeft:"10px"},children:[u.jsx("p",{children:u.jsx("strong",{children:"Step 1: Prepare Your Message"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsxs("p",{style:{margin:0},children:["✍️ ",u.jsx("strong",{children:"Example:"}),' "MEET AT NOON"']}),u.jsxs("p",{style:{margin:0},children:["🧹 ",u.jsx("strong",{children:"Clean:"}),' Remove spaces → "MEETATNOON"']}),u.jsxs("p",{style:{margin:0},children:["📏 ",u.jsx("strong",{children:"Count:"})," 10 letters → Need at least 4×4 grid (16 spaces)"]}),u.jsxs("p",{style:{margin:0},children:["➕ ",u.jsx("strong",{children:"Pad:"}),' "MEETATNOONXXXXXX" (16 letters total)']})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 2: Draw Your Grid"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0"},children:"📐 Draw a 4×4 grid (or your chosen size):"}),u.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",u.jsx("br",{}),"│   │   │   │   │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│   │   │   │   │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│   │   │   │   │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│   │   │   │   │",u.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 3: Fill Grid Row by Row"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0"},children:"✍️ Write letters left to right, top to bottom:"}),u.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",u.jsx("br",{}),"│ M │ E │ E │ T │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ A │ T │ N │ O │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ O │ N │ X │ X │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ X │ X │ X │ X │",u.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 4: Mark the Spider Web Pattern"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0"},children:"🕷️ Number the cells in spider web order (center outward):"}),u.jsxs("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",u.jsx("br",{}),"│ 9 │ 2 │ 3 │10 │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ 8 │ 1 │ 4 │11 │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ 7 │ 6 │ 5 │12 │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│16 │15 │14 │13 │",u.jsx("br",{}),"└───┴───┴───┴───┘"]}),u.jsxs("p",{style:{margin:"10px 0 0 0",fontSize:"12px"},children:["🎯 ",u.jsx("strong",{children:"Pattern:"})," Start at center (1), then spiral outward clockwise"]})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 5: Read in Spider Web Order"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0"},children:"📖 Follow the numbers to read letters:"}),u.jsx("p",{style:{margin:0,fontFamily:"monospace"},children:"1→T, 2→E, 3→E, 4→N, 5→X, 6→N, 7→O, 8→A, 9→M, 10→T, 11→O, 12→X, 13→X, 14→X, 15→X, 16→X"}),u.jsxs("p",{style:{margin:"10px 0 0 0",fontWeight:"bold",color:"#dc2626"},children:["🔐 ",u.jsx("strong",{children:"Encrypted Result:"})," TEENXNOAMTOXXX"]})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#dbeafe",padding:"15px",borderRadius:"8px",border:"1px solid #3b82f6"},children:[u.jsx("h4",{style:{color:"#1e40af",marginTop:0},children:"🔓 MANUAL DECRYPTION STEPS"}),u.jsxs("div",{style:{marginLeft:"10px"},children:[u.jsx("p",{children:u.jsx("strong",{children:"Step 1: Prepare the Encrypted Message"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsxs("p",{style:{margin:0},children:["📝 ",u.jsx("strong",{children:"Given:"}),' "TEENXNOAMTOXXX"']}),u.jsxs("p",{style:{margin:0},children:["📏 ",u.jsx("strong",{children:"Count:"})," 14 letters → Use 4×4 grid (16 spaces)"]})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 2: Draw Empty Grid with Spider Numbers"})}),u.jsx("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:u.jsxs("div",{style:{fontFamily:"monospace",fontSize:"14px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",u.jsx("br",{}),"│ 9 │ 2 │ 3 │10 │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ 8 │ 1 │ 4 │11 │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ 7 │ 6 │ 5 │12 │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│16 │15 │14 │13 │",u.jsx("br",{}),"└───┴───┴───┴───┘"]})}),u.jsx("p",{children:u.jsx("strong",{children:"Step 3: Place Letters in Spider Web Order"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0"},children:"🕷️ Place each letter according to its spider web number:"}),u.jsx("p",{style:{margin:"0 0 10px 0",fontFamily:"monospace",fontSize:"12px"},children:"T→1, E→2, E→3, N→4, X→5, N→6, O→7, A→8, M→9, T→10, O→11, X→12, X→13, X→14, X→15, X→16"}),u.jsxs("div",{style:{fontFamily:"monospace",fontSize:"16px",lineHeight:"1.2"},children:["┌───┬───┬───┬───┐",u.jsx("br",{}),"│ M │ E │ E │ T │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ A │ T │ N │ O │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ O │ N │ X │ X │",u.jsx("br",{}),"├───┼───┼───┼───┤",u.jsx("br",{}),"│ X │ X │ X │ X │",u.jsx("br",{}),"└───┴───┴───┴───┘"]})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 4: Read Row by Row"})}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"4px",marginBottom:"10px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0"},children:"📖 Read left to right, top to bottom:"}),u.jsxs("p",{style:{margin:0,fontFamily:"monospace"},children:["Row 1: M-E-E-T",u.jsx("br",{}),"Row 2: A-T-N-O",u.jsx("br",{}),"Row 3: O-N-X-X",u.jsx("br",{}),"Row 4: X-X-X-X"]}),u.jsxs("p",{style:{margin:"10px 0 0 0"},children:["🧹 ",u.jsx("strong",{children:"Remove padding X's:"})," MEETATNOON"]}),u.jsxs("p",{style:{margin:"10px 0 0 0",fontWeight:"bold",color:"#16a34a"},children:["🎉 ",u.jsx("strong",{children:"Decrypted Message:"}),' "MEET AT NOON"']})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"1px solid #a855f7"},children:[u.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"⚡ QUICK REFERENCE CARD"}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px"},children:[u.jsxs("div",{children:[u.jsx("p",{children:u.jsx("strong",{children:"🔐 Encryption:"})}),u.jsxs("ol",{style:{fontSize:"12px",marginLeft:"15px"},children:[u.jsx("li",{children:"Clean message (letters only)"}),u.jsx("li",{children:"Fill grid row by row"}),u.jsx("li",{children:"Read in spider web pattern"})]})]}),u.jsxs("div",{children:[u.jsx("p",{children:u.jsx("strong",{children:"🔓 Decryption:"})}),u.jsxs("ol",{style:{fontSize:"12px",marginLeft:"15px"},children:[u.jsx("li",{children:"Place letters in spider web order"}),u.jsx("li",{children:"Read grid row by row"}),u.jsx("li",{children:"Remove padding X's"})]})]})]}),u.jsxs("div",{style:{marginTop:"15px",padding:"10px",backgroundColor:"white",borderRadius:"4px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0",fontWeight:"bold"},children:"🕷️ Spider Web Pattern (for any grid size):"}),u.jsxs("p",{style:{margin:0,fontSize:"12px"},children:["1. Find center of grid",u.jsx("br",{}),'2. Number center as "1"',u.jsx("br",{}),"3. Move outward in rings",u.jsx("br",{}),"4. Within each ring, go clockwise",u.jsx("br",{}),"5. Continue until all cells numbered"]})]})]}),u.jsxs("div",{style:{backgroundColor:"#f0f9ff",padding:"15px",borderRadius:"8px",border:"1px solid #0ea5e9"},children:[u.jsx("h4",{style:{color:"#0369a1",marginTop:0},children:"🔧 INDIVIDUAL CIPHER METHODS"}),u.jsxs("div",{style:{marginBottom:"20px"},children:[u.jsx("h5",{style:{color:"#dc2626",margin:"10px 0 5px 0"},children:"🐝 BEE DANCE CIPHER (Manual Steps)"}),u.jsxs("ol",{style:{marginLeft:"20px",fontSize:"12px"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Assign angles:"})," A=0°, B=45°, C=90°, D=135°, E=180°, F=225°, G=270°, H=315°, then repeat (I=0°, J=45°, etc.)"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"List letters with angles:"}),' For "HELLO" → H=315°, E=180°, L=90°, L=90°, O=135°']}),u.jsxs("li",{children:[u.jsx("strong",{children:"Sort by angle:"})," L=90°, L=90°, O=135°, E=180°, H=315°"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Encrypted result:"}),' "LLOEH"']}),u.jsxs("li",{children:[u.jsx("strong",{children:"To decrypt:"})," Reverse the process by restoring original order"]})]})]}),u.jsxs("div",{style:{marginBottom:"20px"},children:[u.jsx("h5",{style:{color:"#16a34a",margin:"10px 0 5px 0"},children:"🐜 ANT PHEROMONE CIPHER (Manual Steps)"}),u.jsxs("ol",{style:{marginLeft:"20px",fontSize:"12px"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Assign pheromone levels:"})," A,F,K,P,U=1; B,G,L,Q,V=2; C,H,M,R,W=3; D,I,N,S,X=4; E,J,O,T,Y,Z=5"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"List letters with levels:"}),' For "HELLO" → H=3, E=5, L=2, L=2, O=5']}),u.jsxs("li",{children:[u.jsx("strong",{children:"Sort by pheromone level:"})," L=2, L=2, H=3, E=5, O=5"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Encrypted result:"}),' "LLHEO"']}),u.jsxs("li",{children:[u.jsx("strong",{children:"To decrypt:"})," Reverse the process by restoring original order"]})]})]}),u.jsxs("div",{style:{marginBottom:"20px"},children:[u.jsx("h5",{style:{color:"#7c3aed",margin:"10px 0 5px 0"},children:"🕷️ SPIDER WEB CIPHER (Manual Steps)"}),u.jsxs("p",{style:{fontSize:"12px",marginLeft:"20px"},children:[u.jsx("strong",{children:"This is the main method shown in the detailed steps above."})," Fill grid row-by-row, then read in spider web pattern (center outward, clockwise in each ring)."]})]}),u.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"8px",border:"2px solid #16a34a"},children:[u.jsx("h5",{style:{color:"#15803d",margin:"0 0 10px 0"},children:"🕷️🐝🐜 COMBINED CIPHER (All Three Together)"}),u.jsxs("div",{style:{marginBottom:"15px"},children:[u.jsx("p",{style:{fontSize:"13px",fontWeight:"bold",margin:"0 0 5px 0"},children:"📋 MATERIALS NEEDED (Enhanced):"}),u.jsxs("ul",{style:{marginLeft:"20px",fontSize:"12px"},children:[u.jsx("li",{children:"📄 Graph paper or ruled paper"}),u.jsx("li",{children:"✏️ Pencil and eraser"}),u.jsxs("li",{children:["🎨 ",u.jsx("strong",{children:"3 different colored pens:"})," Red (spider), Yellow (bee), Green (ant)"]}),u.jsx("li",{children:"📐 Protractor (optional, for bee angles)"}),u.jsx("li",{children:"📊 Reference charts (bee angles & ant pheromone levels)"})]})]}),u.jsxs("div",{style:{marginBottom:"15px"},children:[u.jsx("p",{style:{fontSize:"13px",fontWeight:"bold",margin:"0 0 5px 0"},children:"🔧 STEP-BY-STEP COMBINED PROCESS:"}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 1: Prepare Message & Reference Charts"}),u.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Message:"}),' "HELLO" → Clean: "HELLOX..." (pad to fill grid)']}),u.jsxs("li",{children:[u.jsx("strong",{children:"Bee Angles:"})," A=0°, B=45°, C=90°, D=135°, E=180°, F=225°, G=270°, H=315°"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Ant Pheromones:"})," A,F,K,P,U=1; B,G,L,Q,V=2; C,H,M,R,W=3; D,I,N,S,X=4; E,J,O,T,Y,Z=5"]})]})]}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 2: Create Enhanced Grid with Bio-Data"}),u.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[u.jsx("li",{children:'Draw your grid (3×3 for "HELLO")'}),u.jsx("li",{children:"Fill row-by-row: H-E-L / L-O-X / X-X-X"}),u.jsxs("li",{children:[u.jsx("strong",{children:"Add bee angles:"})," H=315°, E=180°, L=90°, L=90°, O=135°"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Add ant pheromones:"})," H=3, E=5, L=2, L=2, O=5"]})]}),u.jsxs("div",{style:{fontFamily:"monospace",fontSize:"10px",backgroundColor:"#f9fafb",padding:"5px",borderRadius:"4px",marginTop:"5px"},children:["Enhanced Grid Example:",u.jsx("br",{}),"┌─────────┬─────────┬─────────┐",u.jsx("br",{}),"│ H(315°,3)│ E(180°,5)│ L(90°,2) │",u.jsx("br",{}),"├─────────┼─────────┼─────────┤",u.jsx("br",{}),"│ L(90°,2) │ O(135°,5)│ X(270°,4)│",u.jsx("br",{}),"├─────────┼─────────┼─────────┤",u.jsx("br",{}),"│ X(270°,4)│ X(270°,4)│ X(270°,4)│",u.jsx("br",{}),"└─────────┴─────────┴─────────┘"]})]}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 3: Apply Visual Bio-Encoding"}),u.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"🕷️ Spider Web:"})," Mark center with red circle, draw red rings around it"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"🐝 Bee Dance:"})," Draw yellow arrows showing dance angles for each letter"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"🐜 Ant Trails:"})," Use green dots (1-5) to show pheromone strength"]})]})]}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px",marginBottom:"10px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 4: Read in Spider Web Pattern"}),u.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[u.jsx("li",{children:"Follow spider web numbering: Center(1) → Ring 1 clockwise → Ring 2 clockwise"}),u.jsx("li",{children:"For 3×3 grid: O(center) → H,E,L,L(ring1) → X,X,X,X(ring2)"}),u.jsxs("li",{children:[u.jsx("strong",{children:"🔐 Final Result:"}),' "OHELLXXXX"']})]})]}),u.jsxs("div",{style:{backgroundColor:"white",padding:"10px",borderRadius:"6px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0"},children:"Step 5: Document Bio-Patterns"}),u.jsxs("ul",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Record bee sequence:"})," 135°→315°→180°→90°→90°→270°→270°→270°→270°"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Record ant sequence:"})," 5→3→5→2→2→4→4→4→4"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Note:"})," These patterns provide additional verification and visual encoding"]})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"10px",borderRadius:"6px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",margin:"0 0 5px 0",color:"#92400e"},children:"🎯 DECRYPTION PROCESS:"}),u.jsxs("ol",{style:{marginLeft:"15px",fontSize:"11px",margin:"5px 0"},children:[u.jsx("li",{children:"Place encrypted letters back in spider web order"}),u.jsx("li",{children:"Verify using bee angles and ant pheromone patterns"}),u.jsx("li",{children:"Read grid row-by-row to get original message"}),u.jsx("li",{children:`Remove padding X's to reveal: "HELLO"`})]})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#fef2f2",padding:"15px",borderRadius:"8px",border:"1px solid #ef4444"},children:[u.jsx("h4",{style:{color:"#dc2626",marginTop:0},children:"🎭 PRESENTATION & DEMO TIPS"}),u.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[u.jsxs("li",{children:["🎨 ",u.jsx("strong",{children:"Use colors:"})," Different colors for each ring of the spider web"]}),u.jsxs("li",{children:["📏 ",u.jsx("strong",{children:"Large grids:"})," Use poster board or whiteboard for audience visibility"]}),u.jsxs("li",{children:["👥 ",u.jsx("strong",{children:"Audience participation:"})," Have volunteers help fill the grid"]}),u.jsxs("li",{children:["🔄 ",u.jsx("strong",{children:"Show both ways:"})," Encrypt a message, then decrypt it back"]}),u.jsxs("li",{children:["🌟 ",u.jsx("strong",{children:"Bio-connection:"})," Explain the spider, bee, and ant inspiration"]}),u.jsxs("li",{children:["📱 ",u.jsx("strong",{children:"Compare:"})," Show manual vs. digital tool results"]}),u.jsxs("li",{children:["🎯 ",u.jsx("strong",{children:"Start simple:"}),' Use 3×3 grid with short words like "HELLO"']}),u.jsxs("li",{children:["🔀 ",u.jsx("strong",{children:"Try different ciphers:"})," Show how each bio-inspired method works differently"]}),u.jsxs("li",{children:["📊 ",u.jsx("strong",{children:"Compare results:"})," Encrypt same message with spider, bee, and ant methods"]})]})]})]})]}):u.jsx("div",{style:{textAlign:"center",marginBottom:"20px"},children:u.jsx("button",{onClick:t,style:{padding:"10px 20px",backgroundColor:"#059669",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold"},children:"📝 Pen & Paper Guide (For Demos)"})})}function R1({isVisible:e,onToggle:t}){return e?u.jsxs(Kr.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},style:{backgroundColor:"#f8fafc",border:"2px solid #e2e8f0",borderRadius:"12px",padding:"20px",marginBottom:"30px",fontSize:"14px",lineHeight:"1.6"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[u.jsx("h3",{style:{color:"#1e293b",margin:0},children:"🧠 How the Bio-Inspired Cipher Works"}),u.jsx("button",{onClick:t,style:{padding:"5px 10px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px"},children:"✕ Close"})]}),u.jsxs("div",{style:{display:"grid",gap:"20px"},children:[u.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"15px",borderRadius:"8px",border:"1px solid #f59e0b"},children:[u.jsx("h4",{style:{color:"#92400e",marginTop:0},children:"🔐 ENCRYPTION PROCESS"}),u.jsxs("div",{style:{marginLeft:"10px"},children:[u.jsx("p",{children:u.jsx("strong",{children:"Step 1: Message Preparation"})}),u.jsxs("ul",{style:{marginLeft:"20px"},children:[u.jsx("li",{children:"Your message is cleaned (only letters A-Z kept)"}),u.jsx("li",{children:"Padded with 'X' characters to fill the grid completely"}),u.jsx("li",{children:'Example: "HELLO" → "HELLOX..." for a 5×5 grid (25 characters)'})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 2: Grid Filling (Like Ants Building)"})}),u.jsxs("ul",{style:{marginLeft:"20px"},children:[u.jsxs("li",{children:["Letters are placed in the grid ",u.jsx("em",{children:"row by row"}),", left to right"]}),u.jsx("li",{children:"Just like ants systematically building their nest"}),u.jsx("li",{children:"Each letter gets bio-inspired properties:"}),u.jsxs("li",{style:{marginLeft:"20px"},children:["🐝 ",u.jsx("strong",{children:"Bee Angle:"})," Each letter has a dance direction (A=0°, B=45°, etc.)"]}),u.jsxs("li",{style:{marginLeft:"20px"},children:["🐜 ",u.jsx("strong",{children:"Pheromone Level:"})," Strength from 1-5 based on the letter"]})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 3: Spider Web Reading Pattern"})}),u.jsxs("ul",{style:{marginLeft:"20px"},children:[u.jsxs("li",{children:["🕷️ ",u.jsx("strong",{children:"Start from center:"})," Like a spider beginning its web"]}),u.jsxs("li",{children:["📍 ",u.jsx("strong",{children:"Expand in rings:"})," Read outward in concentric circles"]}),u.jsxs("li",{children:["🔄 ",u.jsx("strong",{children:"Clockwise spiral:"})," Within each ring, go clockwise"]}),u.jsx("li",{children:"This creates the encrypted message by reading in spider-web order!"})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#dbeafe",padding:"15px",borderRadius:"8px",border:"1px solid #3b82f6"},children:[u.jsx("h4",{style:{color:"#1e40af",marginTop:0},children:"🔓 DECRYPTION PROCESS"}),u.jsxs("div",{style:{marginLeft:"10px"},children:[u.jsx("p",{children:u.jsx("strong",{children:"Step 1: Reverse Spider Web Placement"})}),u.jsxs("ul",{style:{marginLeft:"20px"},children:[u.jsx("li",{children:"Take the encrypted message and place it back using spider web pattern"}),u.jsxs("li",{children:["🕷️ ",u.jsx("strong",{children:"First character:"})," Goes to center of the web"]}),u.jsxs("li",{children:["📍 ",u.jsx("strong",{children:"Next characters:"})," Fill rings outward (center → ring 1 → ring 2...)"]}),u.jsxs("li",{children:["🔄 ",u.jsx("strong",{children:"Clockwise order:"})," Within each ring, place clockwise"]})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 2: Row-by-Row Reading"})}),u.jsxs("ul",{style:{marginLeft:"20px"},children:[u.jsx("li",{children:"Once the grid is filled using spider pattern, read it normally"}),u.jsxs("li",{children:["📖 ",u.jsx("strong",{children:"Left to right, top to bottom"})," (like reading a book)"]}),u.jsx("li",{children:"This reverses the encryption and reveals the original message!"})]}),u.jsx("p",{children:u.jsx("strong",{children:"Step 3: Clean Up"})}),u.jsxs("ul",{style:{marginLeft:"20px"},children:[u.jsx("li",{children:"Remove trailing 'X' characters that were padding"}),u.jsxs("li",{children:["🎉 ",u.jsx("strong",{children:"Result:"})," Your original message is revealed!"]})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"8px",border:"1px solid #16a34a"},children:[u.jsx("h4",{style:{color:"#15803d",marginTop:0},children:"📋 INDIVIDUAL CIPHER EXAMPLES"}),u.jsxs("div",{style:{marginLeft:"10px"},children:[u.jsxs("p",{children:[u.jsx("strong",{children:"Message:"}),' "HELLO" (using 3×3 grid)']}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"15px",marginBottom:"20px"},children:[u.jsxs("div",{children:[u.jsx("p",{children:u.jsx("strong",{children:"🕷️ Spider Web Cipher:"})}),u.jsxs("div",{style:{fontSize:"11px"},children:[u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"8px",borderRadius:"4px",marginBottom:"5px"},children:"Fill: H E L / L O X / X X X"}),u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"8px",borderRadius:"4px",marginBottom:"5px"},children:"Web: 3 1 4 / 2 🕷️ 5 / 8 7 6"}),u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"#fef2f2",padding:"8px",borderRadius:"4px",fontWeight:"bold",color:"#dc2626"},children:"Result: OHELXXXXX"})]})]}),u.jsxs("div",{children:[u.jsx("p",{children:u.jsx("strong",{children:"🐝 Bee Dance Cipher:"})}),u.jsxs("div",{style:{fontSize:"11px"},children:[u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"8px",borderRadius:"4px",marginBottom:"5px"},children:"H=315°, E=180°, L=90°, L=90°, O=135°"}),u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"8px",borderRadius:"4px",marginBottom:"5px"},children:"Sort by angle: L(90°), L(90°), O(135°), E(180°), H(315°)"}),u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"#fef3c7",padding:"8px",borderRadius:"4px",fontWeight:"bold",color:"#92400e"},children:"Result: LLOEH"})]})]}),u.jsxs("div",{children:[u.jsx("p",{children:u.jsx("strong",{children:"🐜 Ant Pheromone Cipher:"})}),u.jsxs("div",{style:{fontSize:"11px"},children:[u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"8px",borderRadius:"4px",marginBottom:"5px"},children:"H=3, E=5, L=2, L=2, O=5"}),u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"8px",borderRadius:"4px",marginBottom:"5px"},children:"Sort by level: L(2), L(2), H(3), E(5), O(5)"}),u.jsx("div",{style:{fontFamily:"monospace",backgroundColor:"#f0fdf4",padding:"8px",borderRadius:"4px",fontWeight:"bold",color:"#16a34a"},children:"Result: LLHEO"})]})]})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"2px solid #a855f7"},children:[u.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"🕷️🐝🐜 COMBINED CIPHER EXAMPLE"}),u.jsxs("div",{style:{marginLeft:"10px"},children:[u.jsxs("p",{children:[u.jsx("strong",{children:"Message:"}),' "HELLO" (using 3×3 grid with all three bio-patterns)']}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px",marginBottom:"15px"},children:[u.jsxs("div",{children:[u.jsx("p",{style:{fontSize:"13px",fontWeight:"bold"},children:"Step 1: Fill Grid + Calculate Bio-Data"}),u.jsxs("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px",fontSize:"10px"},children:["┌─────────┬─────────┬─────────┐",u.jsx("br",{}),"│ H(315°,3)│ E(180°,5)│ L(90°,2) │",u.jsx("br",{}),"├─────────┼─────────┼─────────┤",u.jsx("br",{}),"│ L(90°,2) │ O(135°,5)│ X(270°,4)│",u.jsx("br",{}),"├─────────┼─────────┼─────────┤",u.jsx("br",{}),"│ X(270°,4)│ X(270°,4)│ X(270°,4)│",u.jsx("br",{}),"└─────────┴─────────┴─────────┘",u.jsx("br",{}),u.jsx("span",{style:{fontSize:"9px"},children:"Format: Letter(BeeAngle°, AntPheromone)"})]})]}),u.jsxs("div",{children:[u.jsx("p",{style:{fontSize:"13px",fontWeight:"bold"},children:"Step 2: Apply Spider Web Reading"}),u.jsxs("div",{style:{fontFamily:"monospace",backgroundColor:"white",padding:"10px",borderRadius:"4px",fontSize:"10px"},children:["┌───┬───┬───┐",u.jsx("br",{}),"│ 3 │ 1 │ 4 │",u.jsx("br",{}),"├───┼───┼───┤",u.jsx("br",{}),"│ 2 │🕷️│ 5 │",u.jsx("br",{}),"├───┼───┼───┤",u.jsx("br",{}),"│ 8 │ 7 │ 6 │",u.jsx("br",{}),"└───┴───┴───┘",u.jsx("br",{}),u.jsx("span",{style:{fontSize:"9px"},children:"Spider web order: center → rings"})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#f8fafc",padding:"12px",borderRadius:"6px",marginBottom:"15px"},children:[u.jsx("p",{style:{fontSize:"13px",fontWeight:"bold",margin:"0 0 8px 0"},children:"Step 3: Read in Spider Web Order"}),u.jsx("div",{style:{fontSize:"12px",fontFamily:"monospace"},children:"1→O(135°,5), 2→L(90°,2), 3→H(315°,3), 4→L(90°,2), 5→X(270°,4), 6→X(270°,4), 7→X(270°,4), 8→X(270°,4)"})]}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"15px"},children:[u.jsxs("div",{style:{backgroundColor:"#fef2f2",padding:"10px",borderRadius:"6px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",color:"#dc2626",margin:"0 0 5px 0"},children:"🕷️ Spider Result:"}),u.jsx("div",{style:{fontFamily:"monospace",fontSize:"11px",fontWeight:"bold"},children:"OLHLXXXX"})]}),u.jsxs("div",{style:{backgroundColor:"#fef3c7",padding:"10px",borderRadius:"6px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",color:"#92400e",margin:"0 0 5px 0"},children:"🐝 Bee Pattern:"}),u.jsx("div",{style:{fontFamily:"monospace",fontSize:"11px"},children:"135°→90°→315°→90°→270°..."})]}),u.jsxs("div",{style:{backgroundColor:"#f0fdf4",padding:"10px",borderRadius:"6px"},children:[u.jsx("p",{style:{fontSize:"12px",fontWeight:"bold",color:"#16a34a",margin:"0 0 5px 0"},children:"🐜 Ant Pattern:"}),u.jsx("div",{style:{fontFamily:"monospace",fontSize:"11px"},children:"5→2→3→2→4→4→4→4"})]})]}),u.jsxs("div",{style:{backgroundColor:"#e0e7ff",padding:"12px",borderRadius:"6px",marginTop:"15px"},children:[u.jsx("p",{style:{fontSize:"13px",fontWeight:"bold",color:"#3730a3",margin:"0 0 8px 0"},children:"🎯 Combined Result:"}),u.jsxs("ul",{style:{fontSize:"12px",marginLeft:"20px",margin:"0"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Primary Encryption:"})," OLHLXXXX (spider web pattern)"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Bee Verification:"})," Dance angles provide directional encoding"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Ant Verification:"})," Pheromone levels provide strength encoding"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Security:"})," Triple-layered bio-inspired protection"]})]})]})]})]}),u.jsxs("div",{style:{backgroundColor:"#fdf4ff",padding:"15px",borderRadius:"8px",border:"1px solid #a855f7"},children:[u.jsx("h4",{style:{color:"#7c3aed",marginTop:0},children:"🌿 INDIVIDUAL CIPHER METHODS"}),u.jsxs("div",{style:{marginLeft:"10px"},children:[u.jsxs("div",{style:{marginBottom:"15px"},children:[u.jsx("p",{children:u.jsx("strong",{children:"🕷️ Spider Web Cipher:"})}),u.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Encryption:"})," Fill grid row-by-row, then read in spider web pattern (center → rings clockwise)"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Decryption:"})," Place encrypted letters in spider web order, then read row-by-row"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Inspiration:"})," Spiders build webs from center outward in organized spiral patterns"]})]})]}),u.jsxs("div",{style:{marginBottom:"15px"},children:[u.jsx("p",{children:u.jsx("strong",{children:"🐝 Bee Dance Cipher:"})}),u.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Encryption:"}),' Sort letters by their "dance angles" (A=0°, B=45°, C=90°, etc.)']}),u.jsxs("li",{children:[u.jsx("strong",{children:"Decryption:"})," Reverse the angle-based sorting to restore original order"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Inspiration:"})," Bees communicate direction and distance through waggle dance angles"]})]})]}),u.jsxs("div",{style:{marginBottom:"15px"},children:[u.jsx("p",{children:u.jsx("strong",{children:"🐜 Ant Pheromone Cipher:"})}),u.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Encryption:"}),' Sort letters by "pheromone strength" (A,F,K,P,U=1; B,G,L,Q,V=2; etc.)']}),u.jsxs("li",{children:[u.jsx("strong",{children:"Decryption:"})," Reverse the pheromone-based sorting to restore original order"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Inspiration:"})," Ants leave chemical trails with varying strengths to guide colony members"]})]})]}),u.jsxs("div",{children:[u.jsx("p",{children:u.jsx("strong",{children:"🕷️🐝🐜 Combined Cipher (All Three Together):"})}),u.jsxs("ul",{style:{marginLeft:"20px",fontSize:"13px"},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Step 1 - Spider Web:"})," Use spider web pattern as the primary encryption method"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Step 2 - Bee Dance:"})," Apply bee angle transformations to each letter during placement"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Step 3 - Ant Pheromones:"})," Use pheromone levels to determine visual encoding and priority"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Result:"})," Triple-layered bio-inspired encryption with visual cues from all three methods"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Security:"})," Multiple bio-inspired patterns create a more complex and secure cipher"]})]})]}),u.jsxs("div",{style:{backgroundColor:"#f0f9ff",padding:"10px",borderRadius:"6px",marginTop:"15px"},children:[u.jsx("p",{style:{margin:"0 0 10px 0",fontWeight:"bold",color:"#0369a1"},children:"🔄 Combined Cipher Process:"}),u.jsxs("ol",{style:{marginLeft:"20px",fontSize:"12px",margin:0},children:[u.jsxs("li",{children:[u.jsx("strong",{children:"Fill grid row-by-row"})," with your message (like ants building systematically)"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Calculate bee angles and ant pheromones"})," for each letter for visual encoding"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Read in spider web pattern"})," (center outward, clockwise) for final encryption"]}),u.jsxs("li",{children:[u.jsx("strong",{children:"Visual display"})," shows all three bio-patterns working together"]})]})]}),u.jsx("p",{style:{fontStyle:"italic",color:"#6b46c1",marginTop:"15px"},children:"Each cipher can be used independently or combined for enhanced security. Nature's communication methods inspire beautiful and secure encryption patterns!"})]})]})]})]}):u.jsx("div",{style:{textAlign:"center",marginBottom:"20px"},children:u.jsx("button",{onClick:t,style:{padding:"10px 20px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold"},children:"📚 How Does This Cipher Work?"})})}function A1(){const[e,t]=R.useState("MEETATNOON"),[n,r]=R.useState("encrypt"),[i,o]=R.useState(5),[s,l]=R.useState("combined"),[a,c]=R.useState(!1),[f,d]=R.useState(!1),p=()=>{if(n!=="encrypt")return"";switch(s){case"spider":return Ue.encryptWithSpiderOnly(e,i);case"bee":return Ue.encryptWithBeeOnly(e,i);case"ant":return Ue.encryptWithAntOnly(e,i);case"combined":default:return Ue.encryptWithSpiderOnly(e,i)}};return u.jsxs("div",{style:{backgroundColor:"#ffffff",border:"2px solid #e5e7eb",borderRadius:"16px",padding:"30px",boxShadow:"0 10px 25px rgba(0,0,0,0.1)"},children:[u.jsx("h2",{style:{fontSize:"28px",fontWeight:"bold",color:"#374151",marginBottom:"20px",textAlign:"center"},children:"�️🐝🐜 Bio-Inspired Cipher: Nature's Communication Secrets"}),u.jsxs("div",{style:{backgroundColor:"#f0f9ff",border:"1px solid #0ea5e9",borderRadius:"8px",padding:"15px",marginBottom:"20px",fontSize:"14px",textAlign:"center"},children:[u.jsx("strong",{children:"Inspired by Nature:"})," Spider web construction 🕷️ + Bee waggle dance 🐝 + Ant pheromone trails 🐜"]}),u.jsx(L1,{isVisible:f,onToggle:()=>d(!f)}),u.jsx(R1,{isVisible:a,onToggle:()=>c(!a)}),u.jsxs("div",{style:{marginBottom:"20px"},children:[u.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"Enter your message:"}),u.jsx("input",{type:"text",value:e,onChange:m=>t(m.target.value),placeholder:"Enter your message here...",style:{width:"100%",padding:"12px 16px",border:"2px solid #d1d5db",borderRadius:"8px",fontSize:"16px",outline:"none",transition:"border-color 0.2s"},onFocus:m=>m.target.style.borderColor="#3b82f6",onBlur:m=>m.target.style.borderColor="#d1d5db"})]}),u.jsxs("div",{style:{marginBottom:"20px",textAlign:"center"},children:[u.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"🕸️ Spider Web Size (Grid Dimensions):"}),u.jsx("div",{style:{display:"flex",gap:"8px",justifyContent:"center",flexWrap:"wrap"},children:[3,4,5,6,7,8].map(m=>u.jsxs("button",{onClick:()=>o(m),style:{padding:"8px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:i===m?"#8b5cf6":"#f3f4f6",borderColor:i===m?"#7c3aed":"#d1d5db",color:i===m?"white":"#374151",transform:i===m?"scale(1.05)":"scale(1)"},children:[m,"×",m]},m))}),u.jsxs("p",{style:{fontSize:"12px",color:"#6b7280",marginTop:"8px"},children:["Larger grids can hold more characters (",i,"×",i," = ",i*i," characters)"]})]}),u.jsxs("div",{style:{marginBottom:"20px",textAlign:"center"},children:[u.jsx("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#374151"},children:"🌿 Choose Your Bio-Inspired Cipher:"}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"8px",justifyContent:"center",maxWidth:"800px",margin:"0 auto"},children:[u.jsx("button",{onClick:()=>l("combined"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="combined"?"#8b5cf6":"#f3f4f6",borderColor:s==="combined"?"#7c3aed":"#d1d5db",color:s==="combined"?"white":"#374151",transform:s==="combined"?"scale(1.02)":"scale(1)"},children:"🕷️🐝🐜 All Combined"}),u.jsx("button",{onClick:()=>l("spider"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="spider"?"#dc2626":"#fef2f2",borderColor:s==="spider"?"#991b1b":"#fecaca",color:s==="spider"?"white":"#dc2626",transform:s==="spider"?"scale(1.02)":"scale(1)"},children:"🕷️ Spider Web Only"}),u.jsx("button",{onClick:()=>l("bee"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="bee"?"#f59e0b":"#fef3c7",borderColor:s==="bee"?"#d97706":"#fde68a",color:s==="bee"?"white":"#92400e",transform:s==="bee"?"scale(1.02)":"scale(1)"},children:"🐝 Bee Dance Only"}),u.jsx("button",{onClick:()=>l("ant"),style:{padding:"12px 16px",border:"2px solid",borderRadius:"8px",cursor:"pointer",fontSize:"14px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:s==="ant"?"#16a34a":"#f0fdf4",borderColor:s==="ant"?"#15803d":"#bbf7d0",color:s==="ant"?"white":"#16a34a",transform:s==="ant"?"scale(1.02)":"scale(1)"},children:"🐜 Ant Trails Only"})]}),u.jsxs("p",{style:{fontSize:"12px",color:"#6b7280",marginTop:"8px"},children:[s==="combined"&&"Uses spider web pattern with bee and ant visual encoding",s==="spider"&&"Encrypts by reading grid in spider web pattern (center outward)",s==="bee"&&"Encrypts by sorting letters based on bee dance angles",s==="ant"&&"Encrypts by sorting letters based on ant pheromone strength"]})]}),u.jsxs("div",{style:{display:"flex",gap:"12px",marginBottom:"30px",justifyContent:"center"},children:[u.jsx("button",{onClick:()=>r("encrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="encrypt"?"#f59e0b":"#fef3c7",color:n==="encrypt"?"white":"#92400e",transform:n==="encrypt"?"scale(1.05)":"scale(1)"},children:"🕷️ Encrypt"}),u.jsx("button",{onClick:()=>r("decrypt"),style:{padding:"12px 24px",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px",fontWeight:"bold",transition:"all 0.2s",backgroundColor:n==="decrypt"?"#3b82f6":"#dbeafe",color:n==="decrypt"?"white":"#1e40af",transform:n==="decrypt"?"scale(1.05)":"scale(1)"},children:"🔓 Decrypt"})]}),n==="encrypt"?u.jsxs("div",{children:[u.jsxs("h3",{style:{textAlign:"center",color:"#92400e",marginBottom:"10px"},children:[s==="combined"&&"🕷️ Spider Web Construction + 🐝 Bee Dance + 🐜 Ant Trails",s==="spider"&&"🕷️ Spider Web Construction",s==="bee"&&"🐝 Bee Waggle Dance Encryption",s==="ant"&&"🐜 Ant Pheromone Trail Encryption"]}),u.jsx(E1,{message:e,gridSize:i,cipherType:s}),e&&u.jsx("div",{style:{marginTop:"20px",padding:"15px",backgroundColor:"#fef3c7",border:"2px solid #f59e0b",borderRadius:"8px",textAlign:"center"},children:u.jsxs("strong",{style:{color:"#92400e"},children:["🔐 Bio-Encrypted: ",p()]})})]}):u.jsxs("div",{children:[u.jsx("h3",{style:{textAlign:"center",color:"#1e40af",marginBottom:"10px"},children:"🔍 Bio-Pattern Analysis & Decryption"}),u.jsx(T1,{message:e,gridSize:i,cipherType:s})]})]})}function M1(){return u.jsx("div",{style:{minHeight:"100vh",backgroundColor:"#fffbee",padding:"20px"},children:u.jsxs("div",{style:{maxWidth:"800px",margin:"0 auto"},children:[u.jsx("h1",{style:{textAlign:"center",color:"#92400e",fontSize:"2.5rem",marginBottom:"1rem"},children:"🕷️🐝🐜 Nature's Cipher Laboratory"}),u.jsx("p",{style:{textAlign:"center",color:"#6b7280",fontSize:"1.2rem",marginBottom:"2rem",fontStyle:"italic"},children:"Encryption inspired by spider webs, bee dances, and ant trails"}),u.jsx(A1,{})]})})}Ts.createRoot(document.getElementById("root")).render(u.jsx(Bl.StrictMode,{children:u.jsx(M1,{})}));
