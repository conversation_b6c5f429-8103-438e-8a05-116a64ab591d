{"name": "one-by-one", "version": "3.2.9", "description": "Run async tasks one by one.", "main": "lib/index.js", "directories": {"example": "example"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:IonicaBizau/one-by-one.git"}, "keywords": ["async", "one-by-one", "waterfall"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/one-by-one/issues"}, "homepage": "https://github.com/IonicaBizau/one-by-one", "dependencies": {"obj-def": "^1.0.0", "sliced": "^1.0.1"}, "devDependencies": {}, "blah": {"description": "If you want to run async functions in parallel, check out [`same-time`](https://github.com/IonicaBizau/same-time)."}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"]}