{"name": "git-url-parse", "version": "5.0.1", "description": "A high level git url parser for common git providers.", "main": "lib/index.js", "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "**************:IonicaBizau/git-url-parse.git"}, "keywords": ["parse", "git", "url"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (http://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/git-url-parse/issues"}, "homepage": "https://github.com/IonicaBizau/git-url-parse", "blah": {"h_img": "http://i.imgur.com/HlfMsVf.png"}, "directories": {"example": "example", "test": "test"}, "dependencies": {"git-up": "^1.0.0"}, "devDependencies": {"tester": "^1.3.1"}}