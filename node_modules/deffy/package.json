{"name": "deffy", "version": "2.2.5", "description": "Small and fast library to set default values.", "main": "lib/index.js", "directories": {"example": "example", "test": "test"}, "dependencies": {"typpy": "^2.0.0"}, "devDependencies": {}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:IonicaBizau/deffy.js.git"}, "keywords": ["default", "values", "deffy"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/deffy.js/issues"}, "blah": {"h_img": "http://i.imgur.com/k1hlQxA.png"}, "homepage": "https://github.com/IonicaBizau/deffy.js", "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"]}