{"name": "read-all-stream", "version": "3.1.0", "description": "Read all stream content and pass it to callback", "license": "MIT", "repository": "floatdrop/read-all-stream", "author": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["stream", "read", "buffer", "callback"], "devDependencies": {"mocha": "*"}, "dependencies": {"pinkie-promise": "^2.0.0", "readable-stream": "^2.0.0"}}