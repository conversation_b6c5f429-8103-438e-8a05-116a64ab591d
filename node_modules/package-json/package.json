{"name": "package-json", "version": "2.4.0", "description": "Get the package.json of a package from the npm registry", "license": "MIT", "repository": "sindresorhus/package-json", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["npm", "registry", "package", "pkg", "package.json", "json", "module", "scope", "scoped"], "dependencies": {"got": "^5.0.0", "registry-auth-token": "^3.0.1", "registry-url": "^3.0.3", "semver": "^5.1.0"}, "devDependencies": {"ava": "*", "mock-private-registry": "^1.1.0", "xo": "*"}}