{"name": "tmp", "version": "0.0.28", "description": "Temporary file and directory creator", "author": "KARASZI István <<EMAIL>> (http://raszi.hu/)", "homepage": "http://github.com/raszi/node-tmp", "keywords": ["temporary", "tmp", "temp", "tempdir", "tempfile", "tmpdir", "tmpfile"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/raszi/node-tmp.git"}, "bugs": {"url": "http://github.com/raszi/node-tmp/issues"}, "main": "lib/tmp.js", "scripts": {"test": "vows test/*-test.js"}, "engines": {"node": ">=0.4.0"}, "dependencies": {"os-tmpdir": "~1.0.1"}, "devDependencies": {"vows": "~0.7.0"}}