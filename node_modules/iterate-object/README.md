<!-- Please do not edit this file. Edit the `blah` field in the `package.json` instead. If in doubt, open an issue. -->


















# iterate-object

 [![Support me on Patreon][badge_patreon]][patreon] [![Buy me a book][badge_amazon]][amazon] [![PayPal][badge_paypal_donate]][paypal-donations] [![Ask me anything](https://img.shields.io/badge/ask%20me-anything-1abc9c.svg)](https://github.com/IonicaBizau/ama) [![Version](https://img.shields.io/npm/v/iterate-object.svg)](https://www.npmjs.com/package/iterate-object) [![Downloads](https://img.shields.io/npm/dt/iterate-object.svg)](https://www.npmjs.com/package/iterate-object) [![Get help on Codementor](https://cdn.codementor.io/badges/get_help_github.svg)](https://www.codementor.io/@johnnyb?utm_source=github&utm_medium=button&utm_term=johnnyb&utm_campaign=github)

<a href="https://www.buymeacoffee.com/H96WwChMy" target="_blank"><img src="https://www.buymeacoffee.com/assets/img/custom_images/yellow_img.png" alt="Buy Me A Coffee"></a>







> A convenient way to iterate objects.

















## :cloud: Installation

```sh
# Using npm
npm install --save iterate-object

# Using yarn
yarn add iterate-object
```













## :clipboard: Example



```js
// Dependencies
var IterateObject = require("iterate-object");

// Iterate this object
IterateObject({
    name: "Bob"
  , age: 42
}, function (value, name) {
    console.log(name, value);
});
// => "name", "Bob"
//    "age", 42

// Iterate an array
IterateObject([
    1, 2, 3, 4, 5, 6, 7
], function (value, i) {
    console.log("v[" + i + "] = " + value);
});
// => v[0] = 1
//    v[1] = 2
//    v[2] = 3
//    v[3] = 4
//    v[4] = 5
//    v[5] = 6
//    v[6] = 7

// Iterate an array
IterateObject([
    "Alice", "Bob", "Carol", "Dave"
], function (value, i, arr) {
    console.log("Current: " + value + (arr[i + 1] ? " Next:" + arr[i + 1] : ""));
});
// => Current: Alice Next:Bob
//    Current: Bob Next:Carol
//    Current: Carol Next:Dave
//    Current: Dave
```












## :question: Get Help

There are few ways to get help:



 1. Please [post questions on Stack Overflow](https://stackoverflow.com/questions/ask). You can open issues with questions, as long you add a link to your Stack Overflow question.
 2. For bug reports and feature requests, open issues. :bug:
 3. For direct and quick help, you can [use Codementor](https://www.codementor.io/johnnyb). :rocket:







## :memo: Documentation


### `iterateObject(obj, fn)`
Iterates an object. Note the object field order may differ.

#### Params

- **Object** `obj`: The input object.
- **Function** `fn`: A function that will be called with the current value, field name and provided object.

#### Return
- **Function** The `iterateObject` function.














## :yum: How to contribute
Have an idea? Found a bug? See [how to contribute][contributing].


## :sparkling_heart: Support my projects
I open-source almost everything I can, and I try to reply to everyone needing help using these projects. Obviously,
this takes time. You can integrate and use these projects in your applications *for free*! You can even change the source code and redistribute (even resell it).

However, if you get some profit from this or just want to encourage me to continue creating stuff, there are few ways you can do it:


 - Starring and sharing the projects you like :rocket:
 - [![Buy me a book][badge_amazon]][amazon]—I love books! I will remember you after years if you buy me one. :grin: :book:
 - [![PayPal][badge_paypal]][paypal-donations]—You can make one-time donations via PayPal. I'll probably buy a ~~coffee~~ tea. :tea:
 - [![Support me on Patreon][badge_patreon]][patreon]—Set up a recurring monthly donation and you will get interesting news about what I'm doing (things that I don't share with everyone).
 - **Bitcoin**—You can send me bitcoins at this address (or scanning the code below): `**********************************`

    ![](https://i.imgur.com/z6OQI95.png)


Thanks! :heart:
















## :dizzy: Where is this library used?
If you are using this library in one of your projects, add it in this list. :sparkles:

 - `@acegoal07/json-editor`
 - `@isysd/gpm`
 - `@rivkesse/emoji-category-map`
 - `@slikts/scrape-it`
 - `@ywzhaiqi/scrape-it-core`
 - `angularvezba`
 - `barbe`
 - `bloggify`
 - `bloggify-actions`
 - `bloggify-cli`
 - `bloggify-config`
 - `bloggify-flexible-router`
 - `bloggify-mongoose`
 - `bloggify-on-request`
 - `bloggify-page`
 - `bloggify-paths`
 - `bloggify-redirect`
 - `bloggify-sequelize`
 - `bloggify-shortcode`
 - `bloggify-template-renderer`
 - `bloggify-theme-renderer`
 - `color-it`
 - `couleurs`
 - `edit-json-file`
 - `elly`
 - `emoji-from-word`
 - `emoji-unicode-map`
 - `emoji.css`
 - `emojic`
 - `engine-builder`
 - `engine-flow-types`
 - `engine-parser`
 - `enny`
 - `err`
 - `error-creator`
 - `fixmtoacrisconverter`
 - `fixmtoacrisconverterlib`
 - `fixmtoacristransformer`
 - `gh-following`
 - `git-stats`
 - `gm-tools`
 - `gpm`
 - `html-encoder-decoder`
 - `lien`
 - `love-you`
 - `map-o`
 - `markdownalint-cli2`
 - `match`
 - `mini-lightbox`
 - `nodeice`
 - `oargv`
 - `obj-flatten`
 - `obj-unflatten`
 - `remove-one-element-arrays`
 - `rucksack`
 - `scrape-it-core`
 - `scrape-it-plus`
 - `scraped-core`
 - `stringify-env`
 - `svg.connectable.js`
 - `tilda`
 - `tools_may_24`
 - `validate5`
 - `xml-jsonify`











## :scroll: License

[MIT][license] © [Ionică Bizău][website]






[license]: /LICENSE
[website]: https://ionicabizau.net
[contributing]: /CONTRIBUTING.md
[docs]: /DOCUMENTATION.md
[badge_patreon]: https://ionicabizau.github.io/badges/patreon.svg
[badge_amazon]: https://ionicabizau.github.io/badges/amazon.svg
[badge_paypal]: https://ionicabizau.github.io/badges/paypal.svg
[badge_paypal_donate]: https://ionicabizau.github.io/badges/paypal_donate.svg
[patreon]: https://www.patreon.com/ionicabizau
[amazon]: http://amzn.eu/hRo9sIZ
[paypal-donations]: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=RVXDDLKKLQRJW
