{"name": "iterate-object", "version": "1.3.5", "description": "A convenient way to iterate objects.", "main": "lib/index.js", "directories": {"example": "example"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:IonicaBizau/node-iterate-object.git"}, "keywords": ["iterate", "object"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/node-iterate-object/issues"}, "homepage": "https://github.com/IonicaBizau/node-iterate-object", "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"]}