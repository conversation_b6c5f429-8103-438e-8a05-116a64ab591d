{"name": "got", "version": "5.6.0", "description": "Simplified HTTP/HTTPS requests", "license": "MIT", "repository": "sindresorhus/got", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}], "engines": {"node": ">=0.10.0"}, "browser": {"unzip-response": false}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js"], "keywords": ["http", "https", "get", "got", "url", "uri", "request", "util", "utility", "simple", "curl", "wget", "fetch"], "dependencies": {"create-error-class": "^3.0.1", "duplexer2": "^0.1.4", "is-plain-obj": "^1.0.0", "is-redirect": "^1.0.0", "is-retry-allowed": "^1.0.0", "is-stream": "^1.0.0", "lowercase-keys": "^1.0.0", "node-status-codes": "^1.0.0", "object-assign": "^4.0.1", "parse-json": "^2.1.0", "pinkie-promise": "^2.0.0", "read-all-stream": "^3.0.0", "readable-stream": "^2.0.5", "timed-out": "^2.0.0", "unzip-response": "^1.0.0", "url-parse-lax": "^1.0.0"}, "devDependencies": {"ava": "^0.5.0", "coveralls": "^2.11.4", "get-port": "^2.0.0", "into-stream": "^2.0.0", "nyc": "^3.2.2", "pem": "^1.4.4", "pify": "^2.3.0", "tempfile": "^1.1.1", "xo": "*"}, "xo": {"ignores": ["test/**"]}}