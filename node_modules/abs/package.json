{"name": "abs", "version": "1.3.15", "description": "A library to convert a path into an absolute path.", "main": "lib/index.js", "directories": {"example": "example", "test": "test"}, "dependencies": {"ul": "^5.0.0"}, "devDependencies": {"mocha": "^2.2.5"}, "scripts": {"test": "mocha test"}, "repository": {"type": "git", "url": "**************:IonicaBizau/abs.git"}, "keywords": ["absolute", "path"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/abs/issues"}, "homepage": "https://github.com/IonicaBizau/abs", "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"]}