<!-- Please do not edit this file. Edit the `blah` field in the `package.json` instead. If in doubt, open an issue. -->


















# abs

 [![Support me on Patreon][badge_patreon]][patreon] [![Buy me a book][badge_amazon]][amazon] [![PayPal][badge_paypal_donate]][paypal-donations] [![Ask me anything](https://img.shields.io/badge/ask%20me-anything-1abc9c.svg)](https://github.com/IonicaBizau/ama) [![Version](https://img.shields.io/npm/v/abs.svg)](https://www.npmjs.com/package/abs) [![Downloads](https://img.shields.io/npm/dt/abs.svg)](https://www.npmjs.com/package/abs) [![Get help on Codementor](https://cdn.codementor.io/badges/get_help_github.svg)](https://www.codementor.io/@johnnyb?utm_source=github&utm_medium=button&utm_term=johnnyb&utm_campaign=github)

<a href="https://www.buymeacoffee.com/H96WwChMy" target="_blank"><img src="https://www.buymeacoffee.com/assets/img/custom_images/yellow_img.png" alt="Buy Me A Coffee"></a>







> A library to convert a path into an absolute path.

















## :cloud: Installation

```sh
# Using npm
npm install --save abs

# Using yarn
yarn add abs
```













## :clipboard: Example



```js
const abs = require("abs");

console.log(abs("/foo"));
// => "/foo"

console.log(abs("foo"));
// => "/path/to/where/you/are/foo"

console.log(abs("~/foo"));
// => "/home/<USER>/foo"
```












## :question: Get Help

There are few ways to get help:



 1. Please [post questions on Stack Overflow](https://stackoverflow.com/questions/ask). You can open issues with questions, as long you add a link to your Stack Overflow question.
 2. For bug reports and feature requests, open issues. :bug:
 3. For direct and quick help, you can [use Codementor](https://www.codementor.io/johnnyb). :rocket:







## :memo: Documentation


### `abs(input)`
Computes the absolute path of an input.

#### Params

- **String** `input`: The input path (if not provided, the current working directory will be returned).

#### Return
- **String** The absolute path.














## :yum: How to contribute
Have an idea? Found a bug? See [how to contribute][contributing].


## :sparkling_heart: Support my projects
I open-source almost everything I can, and I try to reply to everyone needing help using these projects. Obviously,
this takes time. You can integrate and use these projects in your applications *for free*! You can even change the source code and redistribute (even resell it).

However, if you get some profit from this or just want to encourage me to continue creating stuff, there are few ways you can do it:


 - Starring and sharing the projects you like :rocket:
 - [![Buy me a book][badge_amazon]][amazon]—I love books! I will remember you after years if you buy me one. :grin: :book:
 - [![PayPal][badge_paypal]][paypal-donations]—You can make one-time donations via PayPal. I'll probably buy a ~~coffee~~ tea. :tea:
 - [![Support me on Patreon][badge_patreon]][patreon]—Set up a recurring monthly donation and you will get interesting news about what I'm doing (things that I don't share with everyone).
 - **Bitcoin**—You can send me bitcoins at this address (or scanning the code below): `**********************************`

    ![](https://i.imgur.com/z6OQI95.png)


Thanks! :heart:
















## :dizzy: Where is this library used?
If you are using this library in one of your projects, add it in this list. :sparkles:

 - `3abn`
 - `@isysd/gpm`
 - `@well-crafted/git-unsaved`
 - `aha-moment`
 - `angularvezba`
 - `arumia`
 - `auto-geo-sunset`
 - `bible`
 - `bible.js`
 - `blah`
 - `bloggify`
 - `bloggify-cli`
 - `bloggify-paths`
 - `cdnjs-importer`
 - `cli-sunset`
 - `emojer-cli`
 - `engine-app`
 - `engine-paths`
 - `engine-tools`
 - `ethers-cli`
 - `extendscript-bundlr`
 - `fwatcher`
 - `gh-notifier`
 - `ghcal`
 - `git-command`
 - `git-issues`
 - `git-repos`
 - `git-stats`
 - `github-labeller`
 - `gpm`
 - `gry`
 - `idea`
 - `image-to-ascii-cli`
 - `markdownalint-cli2`
 - `maybe-require`
 - `mdy`
 - `messager`
 - `mongof`
 - `namy`
 - `np-init`
 - `package-json-path`
 - `packy`
 - `parrot-bot`
 - `pm2-meteor`
 - `pm2-meteor-args`
 - `pm2-meteor-nvm`
 - `read-file-cache`
 - `rucksack`
 - `rucksack.js`
 - `ship-release`
 - `simplywatch`
 - `ssh-remote`
 - `statique`
 - `tester-init`
 - `tilda-init`
 - `tithe`
 - `tools_may_24`
 - `web-term`
 - `write-file-p`











## :scroll: License

[MIT][license] © [Ionică Bizău][website]






[license]: /LICENSE
[website]: https://ionicabizau.net
[contributing]: /CONTRIBUTING.md
[docs]: /DOCUMENTATION.md
[badge_patreon]: https://ionicabizau.github.io/badges/patreon.svg
[badge_amazon]: https://ionicabizau.github.io/badges/amazon.svg
[badge_paypal]: https://ionicabizau.github.io/badges/paypal.svg
[badge_paypal_donate]: https://ionicabizau.github.io/badges/paypal_donate.svg
[patreon]: https://www.patreon.com/ionicabizau
[amazon]: http://amzn.eu/hRo9sIZ
[paypal-donations]: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=RVXDDLKKLQRJW
