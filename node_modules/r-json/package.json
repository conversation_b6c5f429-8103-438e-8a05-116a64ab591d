{"name": "r-json", "version": "1.3.1", "description": "A small module to read JSON files.", "main": "lib/index.js", "directories": {"example": "example"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "**************:IonicaBizau/node-r-json.git"}, "keywords": ["read", "json", "files"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/node-r-json/issues"}, "homepage": "https://github.com/IonicaBizau/node-r-json", "blah": {"description": "If you want to write JSON files, check out [`w-json`](https://github.com/IonicaBizau/node-w-json)."}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"], "dependencies": {"w-json": "1.3.10"}}