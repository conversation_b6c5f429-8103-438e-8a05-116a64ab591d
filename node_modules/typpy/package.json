{"name": "typpy", "version": "2.4.0", "description": "A better typeof for JavaScript.", "main": "lib/index.js", "directories": {"example": "example", "test": "test"}, "scripts": {"test": "mocha test"}, "repository": {"type": "git", "url": "**************:IonicaBizau/typpy.git"}, "keywords": ["typeof", "javascript", "typpy"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/typpy/issues"}, "homepage": "https://github.com/IonicaBizau/typpy", "blah": {"h_img": "http://i.imgur.com/FkoAc5n.png"}, "dependencies": {"function.name": "^1.0.3"}, "devDependencies": {"mocha": "^8.2.1"}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"], "contributors": ["<PERSON> <<EMAIL>>"]}