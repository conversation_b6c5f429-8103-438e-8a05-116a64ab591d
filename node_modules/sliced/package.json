{"name": "sliced", "version": "1.0.1", "description": "A faster Node.js alternative to Array.prototype.slice.call(arguments)", "main": "index.js", "files": ["LICENSE", "README.md", "index.js"], "scripts": {"test": "make test"}, "repository": {"type": "git", "url": "git://github.com/aheckmann/sliced"}, "keywords": ["arguments", "slice", "array"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"mocha": "1.5.0", "benchmark": "~1.0.0"}, "dependencies": {}}