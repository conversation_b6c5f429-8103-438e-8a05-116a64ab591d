{"name": "obj-def", "description": "Easily set default fields in objects.", "keywords": ["obj", "def", "easily", "set", "default", "fields", "in", "objects"], "license": "MIT", "version": "1.0.10", "main": "lib/index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "repository": {"type": "git", "url": "git+ssh://**************/IonicaBizau/obj-def.git"}, "bugs": {"url": "https://github.com/IonicaBizau/obj-def/issues"}, "homepage": "https://github.com/IonicaBizau/obj-def#readme", "dependencies": {"deffy": "^2.2.2"}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "index.d.ts", "package-lock.json", "bloggify.js", "bloggify.json", "bloggify/"]}