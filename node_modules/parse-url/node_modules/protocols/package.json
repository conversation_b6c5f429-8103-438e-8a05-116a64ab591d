{"name": "protocols", "version": "1.4.8", "description": "Get the protocols of an input url.", "main": "lib/index.js", "directories": {"example": "example", "test": "test"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "**************:IonicaBizau/protocols.git"}, "keywords": ["protocols", "protocol", "url", "parse"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/protocols/issues"}, "homepage": "https://github.com/IonicaBizau/protocols", "dependencies": {}, "devDependencies": {"tester": "^1.3.1"}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js", "bloggify.js", "bloggify.json", "bloggify/"]}