{"name": "parse-url", "version": "1.3.11", "description": "An advanced url parser supporting git urls too.", "main": "lib/index.js", "directories": {"example": "example", "test": "test"}, "scripts": {"test": "node test"}, "repository": {"type": "git", "url": "git+https://github.com/IonicaBizau/parse-url.git"}, "keywords": ["parse", "url", "node", "git", "advanced"], "author": "Ionic<PERSON> Bizău <<EMAIL>> (https://ionicabizau.net)", "license": "MIT", "bugs": {"url": "https://github.com/IonicaBizau/parse-url/issues"}, "homepage": "https://github.com/IonicaBizau/parse-url", "devDependencies": {"tester": "^1.3.1"}, "dependencies": {"protocols": "^1.4.0", "is-ssh": "^1.3.0"}, "files": ["bin/", "app/", "lib/", "dist/", "src/", "scripts/", "resources/", "menu/", "cli.js", "index.js"]}