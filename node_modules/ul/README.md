<!-- Please do not edit this file. Edit the `blah` field in the `package.json` instead. If in doubt, open an issue. -->


















# ul

 [![Support me on Patreon][badge_patreon]][patreon] [![Buy me a book][badge_amazon]][amazon] [![PayPal][badge_paypal_donate]][paypal-donations] [![Ask me anything](https://img.shields.io/badge/ask%20me-anything-1abc9c.svg)](https://github.com/IonicaBizau/ama) [![Travis](https://img.shields.io/travis/IonicaBizau/node-ul.svg)](https://travis-ci.org/IonicaBizau/node-ul/) [![Version](https://img.shields.io/npm/v/ul.svg)](https://www.npmjs.com/package/ul) [![Downloads](https://img.shields.io/npm/dt/ul.svg)](https://www.npmjs.com/package/ul) [![Get help on Codementor](https://cdn.codementor.io/badges/get_help_github.svg)](https://www.codementor.io/@johnnyb?utm_source=github&utm_medium=button&utm_term=johnnyb&utm_campaign=github)

<a href="https://www.buymeacoffee.com/H96WwChMy" target="_blank"><img src="https://www.buymeacoffee.com/assets/img/custom_images/yellow_img.png" alt="Buy Me A Coffee"></a>







> A minimalist utility library.

















## :cloud: Installation

```sh
# Using npm
npm install --save ul

# Using yarn
yarn add ul
```













## :clipboard: Example



```js
const Ul = require("ul")

// Input data
let obj = {
       n: null
      , v: 1
    }
  , def = {
        n: 1
      , v: 10
      , a: 20
    }
  , tmp = null


// Merge the two objects and store the result in tmp
console.log(tmp = Ul.deepMerge(obj, def))
// => { n: null, v: 1, a: 20 }

// Clone the tmp object -- the clone will have a
// different reference
console.log(tmp === Ul.clone(tmp))
// => false

// Show the absolute path to the home directory
console.log(Ul.home()) // or `console.log(Ul.HOME_DIR)`
// => /home/<USER>

// One level merge
console.log(Ul.merge({
    foo: {
        bar: 42
    }
}, {
    foo: {
        bar: 1
      , baz: 7
    }
}))
// => { { bar: 42 } }
```












## :question: Get Help

There are few ways to get help:



 1. Please [post questions on Stack Overflow](https://stackoverflow.com/questions/ask). You can open issues with questions, as long you add a link to your Stack Overflow question.
 2. For bug reports and feature requests, open issues. :bug:
 3. For direct and quick help, you can [use Codementor](https://www.codementor.io/johnnyb). :rocket:







## :memo: Documentation


### `merge(dst, src)`
One level merge. Faster than `deepMerge`.

#### Params

- **** `dst`: {Object} The destination object.
- **** `src`: {Object} The source object (usually defaults).

#### Return
- **Object** The result object.

### `deepMerge()`
Recursively merge the objects from arguments, returning a new object.

Usage: `Ul.deepMerge(obj1, obj2, obj3, obj4, ..., objN)`

#### Return
- **Object** The merged objects.

### `clone(item)`
Deep clone of the provided item.

#### Params

- **Anything** `item`: The item that should be cloned

#### Return
- **Anything** The cloned object

### `home()`
Get the home directory path on any platform. The value can be
accessed using `Ul.HOME_DIR` too.

#### Return
- **String** The home directory path.














## :yum: How to contribute
Have an idea? Found a bug? See [how to contribute][contributing].


## :sparkling_heart: Support my projects
I open-source almost everything I can, and I try to reply to everyone needing help using these projects. Obviously,
this takes time. You can integrate and use these projects in your applications *for free*! You can even change the source code and redistribute (even resell it).

However, if you get some profit from this or just want to encourage me to continue creating stuff, there are few ways you can do it:


 - Starring and sharing the projects you like :rocket:
 - [![Buy me a book][badge_amazon]][amazon]—I love books! I will remember you after years if you buy me one. :grin: :book:
 - [![PayPal][badge_paypal]][paypal-donations]—You can make one-time donations via PayPal. I'll probably buy a ~~coffee~~ tea. :tea:
 - [![Support me on Patreon][badge_patreon]][patreon]—Set up a recurring monthly donation and you will get interesting news about what I'm doing (things that I don't share with everyone).
 - **Bitcoin**—You can send me bitcoins at this address (or scanning the code below): `**********************************`

    ![](https://i.imgur.com/z6OQI95.png)


Thanks! :heart:
















## :dizzy: Where is this library used?
If you are using this library in one of your projects, add it in this list. :sparkles:

 - `3abn`
 - `@isysd/gpm`
 - `abs`
 - `angularvezba`
 - `ansy`
 - `ascii-heart`
 - `asciify-pixel`
 - `asciify-pixel-matrix`
 - `bible`
 - `bible.js`
 - `birthday`
 - `blah`
 - `bloggify`
 - `bloggify-cli`
 - `bloggify-config`
 - `bloggify-markdown-adapter`
 - `bloggify-mongoose`
 - `bloggify-paths`
 - `bloggify-plugin-class`
 - `bloggify-sequelize`
 - `bloggify-sequelize-adapter`
 - `bloggify-social`
 - `bloggify-sql-adapter`
 - `bloggify-template-renderer`
 - `bloggify-theme-renderer`
 - `bloggify-tools`
 - `bloggify-viewer`
 - `bnotify`
 - `cdnjs-importer`
 - `class-methods`
 - `cli-box`
 - `cli-circle`
 - `cli-gh-cal`
 - `cli-graph`
 - `cli-pie`
 - `cli-snow`
 - `cobol`
 - `compute-size`
 - `diable`
 - `donate.js`
 - `elasticfire`
 - `electronify`
 - `emoji-logger`
 - `engine-builder`
 - `engine-flow-types`
 - `engine-parser`
 - `engine-tools`
 - `enny`
 - `flow-api`
 - `fortran`
 - `function-data-converter`
 - `fwatcher`
 - `gh-contributions`
 - `gh-fork-source`
 - `gh.js`
 - `ghcal`
 - `ghoos`
 - `ghosty`
 - `git-cloner`
 - `git-stats`
 - `git-stats-importer`
 - `github-colors`
 - `github-stats`
 - `gry`
 - `idea`
 - `image-to-ascii`
 - `img-ssim`
 - `img-to-ascii`
 - `jipics`
 - `jsonrequest`
 - `le-table`
 - `lien`
 - `made-in`
 - `markdown-templator`
 - `markdown-templator-fork`
 - `markdownalint-cli2`
 - `match`
 - `mdify`
 - `mongof`
 - `nodeice`
 - `np-init`
 - `npm-template-html`
 - `npmreserve`
 - `oargv`
 - `packy`
 - `page-changed`
 - `parent-search`
 - `parrot-bot`
 - `phantom-jquery`
 - `promptify`
 - `regarde`
 - `rucksack`
 - `share-term`
 - `ship-release`
 - `statique`
 - `streamp`
 - `striking-clock`
 - `template-html`
 - `tester-init`
 - `tilda`
 - `tilda-init`
 - `tiny-json-request`
 - `tinyreq`
 - `tinyreq-cli`
 - `tithe`
 - `tools_may_24`
 - `transformer`
 - `web-term`
 - `wrabbit`











## :scroll: License

[MIT][license] © [Ionică Bizău][website]






[license]: /LICENSE
[website]: https://ionicabizau.net
[contributing]: /CONTRIBUTING.md
[docs]: /DOCUMENTATION.md
[badge_patreon]: https://ionicabizau.github.io/badges/patreon.svg
[badge_amazon]: https://ionicabizau.github.io/badges/amazon.svg
[badge_paypal]: https://ionicabizau.github.io/badges/paypal.svg
[badge_paypal_donate]: https://ionicabizau.github.io/badges/paypal_donate.svg
[patreon]: https://www.patreon.com/ionicabizau
[amazon]: http://amzn.eu/hRo9sIZ
[paypal-donations]: https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=RVXDDLKKLQRJW
